<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Ano_LX</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>TM4C123GH6PM</Device>
          <Vendor>Texas Instruments</Vendor>
          <PackID>Keil.TM4C_DFP.1.1.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x008000) IROM(0x00000000,0x040000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0TM4C123_256 -FS00 -FL040000 -FP0($$Device:TM4C123GH6PM$Flash\TM4C123_256.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:TM4C123GH6PM$Device\Include\TM4C123\TM4C123.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:TM4C123GH6PM$SVD\TM4C123\TM4C123GH6PM.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\</OutputDirectory>
          <OutputName>ANO_PioneerPro_Ti</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\build\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4098</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\CMSIS_AGDI.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>TARGET_IS_TM4C123_RB1 PART_TM4C123GH6PM</Define>
              <Undefine></Undefine>
              <IncludePath>..\DriversBsp;..\DriversBsp\MyMath;..\DriversMcu\TM4C123\Drivers;..\DriversMcu\TM4C123\Libraries\inc;..\DriversMcu\TM4C123\Libraries\inc\driver_hw;..\DriversMcu\TM4C123\Libraries\inc\driver_inc;..\DriversMcu\TM4C123\Libraries\inc\driver_usb;..\DriversMcu\TM4C123\Libraries\inc\driver_usb\device;..\FcSrc;..\FcSrc\FcGeneral;..\FcSrc\FcSpecific;..\DriversMcu\TM4C123</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--entry Reset_Handler</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>FcSrc</GroupName>
          <Files>
            <File>
              <FileName>SysConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\SysConfig.h</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\main.c</FilePath>
            </File>
            <File>
              <FileName>Ano_Scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\Ano_Scheduler.c</FilePath>
            </File>
            <File>
              <FileName>Ano_Scheduler.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\Ano_Scheduler.h</FilePath>
            </File>
            <File>
              <FileName>User_Task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User_Task.c</FilePath>
            </File>
            <File>
              <FileName>User_Task.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\User_Task.h</FilePath>
            </File>
            <File>
              <FileName>ANO_DT_LX.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\ANO_DT_LX.c</FilePath>
            </File>
            <File>
              <FileName>ANO_DT_LX.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\ANO_DT_LX.h</FilePath>
            </File>
            <File>
              <FileName>ANO_LX.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\ANO_LX.c</FilePath>
            </File>
            <File>
              <FileName>ANO_LX.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\ANO_LX.h</FilePath>
            </File>
            <File>
              <FileName>LX_FC_EXT_Sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_FC_EXT_Sensor.c</FilePath>
            </File>
            <File>
              <FileName>LX_FC_EXT_Sensor.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_FC_EXT_Sensor.h</FilePath>
            </File>
            <File>
              <FileName>LX_FC_Fun.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_FC_Fun.c</FilePath>
            </File>
            <File>
              <FileName>LX_FC_Fun.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_FC_Fun.h</FilePath>
            </File>
            <File>
              <FileName>LX_FC_State.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_FC_State.c</FilePath>
            </File>
            <File>
              <FileName>LX_FC_State.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_FC_State.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DriversBsp</GroupName>
          <Files>
            <File>
              <FileName>Drv_BSP.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Drv_BSP.c</FilePath>
            </File>
            <File>
              <FileName>Drv_BSP.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Drv_BSP.h</FilePath>
            </File>
            <File>
              <FileName>Ano_Math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Ano_Math.c</FilePath>
            </File>
            <File>
              <FileName>Ano_Math.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Ano_Math.h</FilePath>
            </File>
            <File>
              <FileName>Drv_AnoOf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Drv_AnoOf.c</FilePath>
            </File>
            <File>
              <FileName>Drv_AnoOf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Drv_AnoOf.h</FilePath>
            </File>
            <File>
              <FileName>Drv_UbloxGPS.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Drv_UbloxGPS.c</FilePath>
            </File>
            <File>
              <FileName>Drv_UbloxGPS.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Drv_UbloxGPS.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DriversMcu</GroupName>
          <Files>
            <File>
              <FileName>McuConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\McuConfig.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Sys.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Sys.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Sys.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Adc.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Adc.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Led.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Led.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Led.h</FilePath>
            </File>
            <File>
              <FileName>Drv_PwmOut.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_PwmOut.c</FilePath>
            </File>
            <File>
              <FileName>Drv_PwmOut.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_PwmOut.h</FilePath>
            </File>
            <File>
              <FileName>Drv_RcIn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_RcIn.c</FilePath>
            </File>
            <File>
              <FileName>Drv_RcIn.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_RcIn.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Uart.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Uart.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Usb.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Usb.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Usb.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Timer.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Timer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Drivers\Drv_Timer.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LibariesMcu</GroupName>
          <Files>
            <File>
              <FileName>driverlib.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DriversMcu\TM4C123\Libraries\driverlib.lib</FilePath>
            </File>
            <File>
              <FileName>usblib.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DriversMcu\TM4C123\Libraries\usblib.lib</FilePath>
            </File>
            <File>
              <FileName>startup_TM4C123.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s</FilePath>
            </File>
            <File>
              <FileName>system_TM4C123.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Libraries\src\system_TM4C123.c</FilePath>
            </File>
            <File>
              <FileName>usb_serial_structs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\TM4C123\Libraries\src\usb_serial_structs.c</FilePath>
            </File>
            <File>
              <FileName>usb_serial_structs.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\TM4C123\Libraries\inc\usb_serial_structs.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Note</GroupName>
          <Files>
            <File>
              <FileName>note.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\Doc\note.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

</Project>
