<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\build\ANO_LX.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\build\ANO_LX.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 17:54:35 2025
<BR><P>
<H3>Maximum Stack Usage =        200 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
Loop_1000Hz &rArr; user_task &rArr; Send_zong &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[3]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">HardFault_Handler</a><BR>
 <LI><a href="#[9]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">PendSV_Handler</a><BR>
 <LI><a href="#[1d]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1d]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1d]">ADC_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX0_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[20]">CAN1_RX1_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[21]">CAN1_SCE_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[1e]">CAN1_TX_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[4b]">CAN2_RX0_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[4c]">CAN2_RX1_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[4d]">CAN2_SCE_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[4a]">CAN2_TX_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[5a]">CRYP_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[59]">DCMI_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream0_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream1_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream2_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream3_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream5_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream0_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream1_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream2_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream3_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream4_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream6_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream7_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[48]">ETH_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[49]">ETH_WKUP_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[33]">EXTI15_10_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[22]">EXTI9_5_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[f]">FLASH_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[5c]">FPU_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3b]">FSMC_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[5b]">HASH_RNG_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2b]">I2C1_ER_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2a]">I2C1_EV_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2d]">I2C2_ER_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2c]">I2C2_EV_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[54]">I2C3_ER_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[53]">I2C3_EV_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[61]">Loop_1000Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[64]">Loop_100Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[63]">Loop_200Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[66]">Loop_20Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[67]">Loop_2Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[62]">Loop_500Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[65]">Loop_50Hz</a> from ano_scheduler.o(.text) referenced from ano_scheduler.o(.data)
 <LI><a href="#[4]">MemManage_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[4e]">OTG_FS_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[35]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[56]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[58]">OTG_HS_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[57]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[c]">PVD_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[10]">RCC_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3c]">SDIO_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[2f]">SPI2_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3e]">SPI3_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from drv_sys.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[5e]">SystemInit</a> from system_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(.text)
 <LI><a href="#[d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[23]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[26]">TIM1_CC_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[25]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[24]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[27]">TIM2_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[28]">TIM3_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[29]">TIM4_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3d]">TIM5_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[41]">TIM6_DAC_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[42]">TIM7_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[36]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[39]">TIM8_CC_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[38]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[37]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[40]">UART5_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[30]">USART1_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[32]">USART3_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[52]">USART6_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[b]">WWDG_IRQHandler</a> from startup_stm32f4xx.o(.text) referenced from startup_stm32f4xx.o(RESET)
 <LI><a href="#[5f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f4xx.o(.text)
 <LI><a href="#[60]">_sputc</a> from printf3.o(i._sputc) referenced from printf3.o(i.__0sprintf$3)
 <LI><a href="#[5d]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[5f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(.text)
</UL>
<P><STRONG><a name="[1a0]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[68]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[18f]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1a1]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[1a2]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[1a3]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[1a4]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[1a5]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1a6]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[d7]"></a>assert_failed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SystemLPConfig
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetVectorTable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartConv
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RegularChannelConfig
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetFlagStatus
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetConversionValue
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearITPendingBit
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMACmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_IrDACmd
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_IrDAConfig
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SmartCardNACKCmd
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SmartCardCmd
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SetGuardTime
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_HalfDuplexCmd
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendBreak
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_LINCmd
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_LINBreakDetectLengthConfig
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_WakeUpConfig
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiverWakeUpCmd
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SetAddress
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_OneBitMethodCmd
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_OverSampling8Cmd
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SetPrescaler
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_RemapConfig
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectHallSensor
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_EncoderInterfaceConfig
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectMasterSlaveMode
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectSlaveMode
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOutputTrigger
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode2Config
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode1Config
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRxExternalClockConfig
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_InternalClockConfig
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectCCDMA
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DMACmd
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DMAConfig
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetFlagStatus
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GenerateEvent
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCPreloadControl
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectCOM
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_BDTRConfig
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture4
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture3
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture2
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture1
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC3Prescaler
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC4Prescaler
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxNCmd
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxCmd
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PolarityConfig
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3NPolarityConfig
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PolarityConfig
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2NPolarityConfig
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PolarityConfig
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1NPolarityConfig
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PolarityConfig
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC4Ref
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC3Ref
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC2Ref
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC1Ref
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4FastConfig
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3FastConfig
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2FastConfig
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1FastConfig
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC4Config
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC3Config
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC2Config
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC1Config
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare4
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare1
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOCxM
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetClockDivision
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOnePulseMode
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_UpdateRequestConfig
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_UpdateDisableConfig
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetPrescaler
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCounter
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetAutoreload
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CounterModeConfig
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PrescalerConfig
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClearITPendingBit
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetITStatus
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ITConfig
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockLPModeCmd
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockLPModeCmd
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphClockLPModeCmd
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB2PeriphClockLPModeCmd
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockLPModeCmd
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphResetCmd
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB2PeriphResetCmd
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphClockCmd
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB2PeriphClockCmd
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_I2SCLKConfig
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_BackupResetCmd
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_RTCCLKCmd
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_RTCCLKConfig
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_MCO2Config
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_MCO1Config
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClockSecuritySystemCmd
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLI2SCmd
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLI2SConfig
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LSICmd
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LSEConfig
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSICmd
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AdjustHSICalibrationValue
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphResetCmd
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ToggleBits
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Write
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputData
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputDataBit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputData
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinLockConfig
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_DeInit
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearFlag
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetFlagStatus
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GenerateSWInterrupt
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ClearITPendingBit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetITStatus
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ClearFlag
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ITConfig
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetInjectedConversionValue
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedDiscModeCmd
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AutoInjectedConvCmd
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetSoftwareStartInjectedConvCmdStatus
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartInjectedConv
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ExternalTrigInjectedConvEdgeConfig
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ExternalTrigInjectedConvConfig
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SetInjectedOffset
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedSequencerLengthConfig
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedChannelConfig
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_MultiModeDMARequestAfterLastTransferCmd
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMARequestAfterLastTransferCmd
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMACmd
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DiscModeCmd
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DiscModeChannelCountConfig
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ContinuousModeCmd
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EOCOnEachRegularChannelCmd
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetSoftwareStartConvStatus
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_VBATCmd
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TempSensorVrefintCmd
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AnalogWatchdogSingleChannelConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AnalogWatchdogThresholdsConfig
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AnalogWatchdogCmd
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CommonInit
</UL>

<P><STRONG><a name="[5d]"></a>main</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = main &rArr; All_Init &rArr; DrvUart3Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Setup
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Run
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[6b]"></a>Scheduler_Setup</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6c]"></a>Scheduler_Run</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, ano_scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Scheduler_Run
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>user_task</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = user_task &rArr; Send_zong &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send02
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send01
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_zong
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_pandian
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_1000Hz
</UL>

<P><STRONG><a name="[73]"></a>user_1ms</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = user_1ms &rArr; led &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
</UL>

<P><STRONG><a name="[6a]"></a>All_Init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, drv_bsp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = All_Init &rArr; DrvUart3Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSysInit
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init_Single_Channel
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[83]"></a>get_data_length_by_cmd</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, protocol.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_receive_byte
</UL>

<P><STRONG><a name="[82]"></a>protocol_reset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, protocol.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_receive_byte
</UL>

<P><STRONG><a name="[84]"></a>protocol_process_frame</STRONG> (Thumb, 456 bytes, Stack size 0 bytes, protocol.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_receive_byte
</UL>

<P><STRONG><a name="[81]"></a>protocol_receive_byte</STRONG> (Thumb, 164 bytes, Stack size 4 bytes, protocol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = protocol_receive_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_process_frame
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_reset
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_data_length_by_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU5DataCheck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU1DataCheck
</UL>

<P><STRONG><a name="[1a7]"></a>decimal_to_bcd</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, protocol.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>usart_send01</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, protocol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = usart_send01 &rArr; DrvUart1SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>

<P><STRONG><a name="[70]"></a>usart_send02</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, protocol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usart_send02 &rArr; DrvUart1SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>

<P><STRONG><a name="[71]"></a>Send_pandian</STRONG> (Thumb, 178 bytes, Stack size 120 bytes, protocol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = Send_pandian &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>

<P><STRONG><a name="[72]"></a>Send_zong</STRONG> (Thumb, 420 bytes, Stack size 128 bytes, protocol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = Send_zong &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>

<P><STRONG><a name="[89]"></a>no_animal</STRONG> (Thumb, 46 bytes, Stack size 112 bytes, protocol.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>

<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI9_5_IRQHandler &rArr; EXTI_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM3_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM4_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM7_IRQHandler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TIM7_IRQHandler &rArr; user_1ms &rArr; led &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_1ms
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART1_IRQHandler &rArr; Usart1_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART2_IRQHandler &rArr; Usart2_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART3_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART3_IRQHandler &rArr; Usart3_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART4_IRQHandler &rArr; Uart4_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART5_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART5_IRQHandler &rArr; Uart5_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USART6_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>SysTick_Init</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SysTick_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSysInit
</UL>

<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, drv_sys.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>GetSysRunTimeMs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, drv_sys.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Run
</UL>

<P><STRONG><a name="[97]"></a>GetSysRunTimeUs</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GetSysRunTimeUs
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayUs
</UL>

<P><STRONG><a name="[96]"></a>MyDelayUs</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = MyDelayUs &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeUs
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
</UL>

<P><STRONG><a name="[77]"></a>MyDelayMs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = MyDelayMs &rArr; MyDelayUs &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;no_animal
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_zong
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_pandian
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[76]"></a>DrvSysInit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = DrvSysInit &rArr; SysTick_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[7a]"></a>ADC_Init_Single_Channel</STRONG> (Thumb, 152 bytes, Stack size 40 bytes, drv_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ADC_Init_Single_Channel &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[9e]"></a>Get_Adc_Value</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, drv_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartConv
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RegularChannelConfig
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetFlagStatus
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetConversionValue
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IRDistance
</UL>

<P><STRONG><a name="[a3]"></a>Get_IRDistance</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, drv_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Adc_Value
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[78]"></a>DvrLedInit</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, drv_led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DvrLedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[75]"></a>led</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, drv_led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = led &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_1ms
</UL>

<P><STRONG><a name="[79]"></a>DrvPwmOutInit</STRONG> (Thumb, 340 bytes, Stack size 56 bytes, drv_pwmout.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = DrvPwmOutInit &rArr; TIM_OC4Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseStructInit
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OCStructInit
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[1a8]"></a>DrvMotorPWMSet</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, drv_pwmout.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>motor_set</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_pwmout.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>TIM_CONF</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, drv_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIM_CONF &rArr; TIM_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
</UL>

<P><STRONG><a name="[bf]"></a>TIM_NVIC</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, drv_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TIM_NVIC &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
</UL>

<P><STRONG><a name="[80]"></a>DrvTimerFcInit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, drv_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = DrvTimerFcInit &rArr; TIM_CONF &rArr; TIM_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_NVIC
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[1aa]"></a>NoUse</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, drv_uart.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>DrvUart1Init</STRONG> (Thumb, 244 bytes, Stack size 48 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = DrvUart1Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[85]"></a>DrvUart1SendBuf</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart1SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send02
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send01
</UL>

<P><STRONG><a name="[ca]"></a>drvU1GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
</UL>

<P><STRONG><a name="[c7]"></a>drvU1DataCheck</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = drvU1DataCheck &rArr; protocol_receive_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_receive_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[8e]"></a>Usart1_IRQ</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Usart1_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU1GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[7c]"></a>DrvUart2Init</STRONG> (Thumb, 236 bytes, Stack size 48 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = DrvUart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[cb]"></a>DrvUart2SendBuf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, drv_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>

<P><STRONG><a name="[cc]"></a>drvU2GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
</UL>

<P><STRONG><a name="[d3]"></a>drvU2DataCheck</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[8f]"></a>Usart2_IRQ</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Usart2_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU2GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[7d]"></a>DrvUart3Init</STRONG> (Thumb, 310 bytes, Stack size 48 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = DrvUart3Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[cd]"></a>DrvUart3SendBuf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, drv_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>

<P><STRONG><a name="[ce]"></a>drvU3GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
</UL>

<P><STRONG><a name="[d4]"></a>drvU3DataCheck</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[90]"></a>Usart3_IRQ</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Usart3_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU3GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[7e]"></a>DrvUart4Init</STRONG> (Thumb, 206 bytes, Stack size 40 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvUart4Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[cf]"></a>DrvUart4SendBuf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, drv_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>

<P><STRONG><a name="[d0]"></a>drvU4GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
</UL>

<P><STRONG><a name="[d5]"></a>drvU4DataCheck</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[91]"></a>Uart4_IRQ</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Uart4_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU4GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[7f]"></a>DrvUart5Init</STRONG> (Thumb, 280 bytes, Stack size 40 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvUart5Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[88]"></a>DrvUart5SendBuf</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;no_animal
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_zong
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_pandian
</UL>

<P><STRONG><a name="[d2]"></a>drvU5GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
</UL>

<P><STRONG><a name="[d1]"></a>drvU5DataCheck</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = drvU5DataCheck &rArr; protocol_receive_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_receive_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[92]"></a>Uart5_IRQ</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Uart5_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU5GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[74]"></a>DrvUartDataCheck</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DrvUartDataCheck &rArr; drvU5DataCheck &rArr; protocol_receive_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU5DataCheck
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU4DataCheck
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU3DataCheck
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU2DataCheck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU1DataCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_1ms
</UL>

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f4xx.o(.text)
</UL>
<P><STRONG><a name="[1ab]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, system_stm32f4xx.o(.text), UNUSED)

<P><STRONG><a name="[98]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_PriorityGroupConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSysInit
</UL>

<P><STRONG><a name="[c0]"></a>NVIC_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_NVIC
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[d8]"></a>NVIC_SetVectorTable</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, misc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[d9]"></a>NVIC_SystemLPConfig</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, misc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[95]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_CLKSourceConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[da]"></a>ADC_DeInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>

<P><STRONG><a name="[9c]"></a>ADC_Init</STRONG> (Thumb, 402 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init_Single_Channel
</UL>

<P><STRONG><a name="[1ac]"></a>ADC_StructInit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_adc.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>ADC_CommonInit</STRONG> (Thumb, 342 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ad]"></a>ADC_CommonStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_adc.o(.text), UNUSED)

<P><STRONG><a name="[9d]"></a>ADC_Cmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init_Single_Channel
</UL>

<P><STRONG><a name="[dd]"></a>ADC_AnalogWatchdogCmd</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[de]"></a>ADC_AnalogWatchdogThresholdsConfig</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[df]"></a>ADC_AnalogWatchdogSingleChannelConfig</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e0]"></a>ADC_TempSensorVrefintCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e1]"></a>ADC_VBATCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[9f]"></a>ADC_RegularChannelConfig</STRONG> (Thumb, 408 bytes, Stack size 32 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Adc_Value
</UL>

<P><STRONG><a name="[a0]"></a>ADC_SoftwareStartConv</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Adc_Value
</UL>

<P><STRONG><a name="[e2]"></a>ADC_GetSoftwareStartConvStatus</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e3]"></a>ADC_EOCOnEachRegularChannelCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e4]"></a>ADC_ContinuousModeCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e5]"></a>ADC_DiscModeChannelCountConfig</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e6]"></a>ADC_DiscModeCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[a2]"></a>ADC_GetConversionValue</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Adc_Value
</UL>

<P><STRONG><a name="[1ae]"></a>ADC_GetMultiModeConversionValue</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_adc.o(.text), UNUSED)

<P><STRONG><a name="[e7]"></a>ADC_DMACmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e8]"></a>ADC_DMARequestAfterLastTransferCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[e9]"></a>ADC_MultiModeDMARequestAfterLastTransferCmd</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ea]"></a>ADC_InjectedChannelConfig</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[eb]"></a>ADC_InjectedSequencerLengthConfig</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ec]"></a>ADC_SetInjectedOffset</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ed]"></a>ADC_ExternalTrigInjectedConvConfig</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ee]"></a>ADC_ExternalTrigInjectedConvEdgeConfig</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ef]"></a>ADC_SoftwareStartInjectedConv</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f0]"></a>ADC_GetSoftwareStartInjectedConvCmdStatus</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f1]"></a>ADC_AutoInjectedConvCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f2]"></a>ADC_InjectedDiscModeCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f3]"></a>ADC_GetInjectedConversionValue</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f4]"></a>ADC_ITConfig</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[a1]"></a>ADC_GetFlagStatus</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Adc_Value
</UL>

<P><STRONG><a name="[f5]"></a>ADC_ClearFlag</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f6]"></a>ADC_GetITStatus</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[f7]"></a>ADC_ClearITPendingBit</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1af]"></a>EXTI_DeInit</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_exti.o(.text), UNUSED)

<P><STRONG><a name="[f8]"></a>EXTI_Init</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1b0]"></a>EXTI_StructInit</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_exti.o(.text), UNUSED)

<P><STRONG><a name="[f9]"></a>EXTI_GenerateSWInterrupt</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[fa]"></a>EXTI_GetFlagStatus</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[fb]"></a>EXTI_ClearFlag</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[8a]"></a>EXTI_GetITStatus</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, stm32f4xx_exti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EXTI_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI9_5_IRQHandler
</UL>

<P><STRONG><a name="[8b]"></a>EXTI_ClearITPendingBit</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_exti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI9_5_IRQHandler
</UL>

<P><STRONG><a name="[fc]"></a>GPIO_DeInit</STRONG> (Thumb, 284 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphResetCmd
</UL>

<P><STRONG><a name="[9b]"></a>GPIO_Init</STRONG> (Thumb, 342 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init_Single_Channel
</UL>

<P><STRONG><a name="[a8]"></a>GPIO_StructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[fe]"></a>GPIO_PinLockConfig</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ff]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[100]"></a>GPIO_ReadInputData</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[101]"></a>GPIO_ReadOutputDataBit</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[102]"></a>GPIO_ReadOutputData</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[aa]"></a>GPIO_SetBits</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led
</UL>

<P><STRONG><a name="[a9]"></a>GPIO_ResetBits</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led
</UL>

<P><STRONG><a name="[103]"></a>GPIO_WriteBit</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[104]"></a>GPIO_Write</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[105]"></a>GPIO_ToggleBits</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ae]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 382 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1b1]"></a>RCC_DeInit</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[106]"></a>RCC_HSEConfig</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[107]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
</UL>

<P><STRONG><a name="[108]"></a>RCC_WaitForHSEStartUp</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
</UL>

<P><STRONG><a name="[109]"></a>RCC_AdjustHSICalibrationValue</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[10a]"></a>RCC_HSICmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[10b]"></a>RCC_LSEConfig</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[10c]"></a>RCC_LSICmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[10d]"></a>RCC_PLLConfig</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[10e]"></a>RCC_PLLCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[10f]"></a>RCC_PLLI2SConfig</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[110]"></a>RCC_PLLI2SCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[111]"></a>RCC_ClockSecuritySystemCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[112]"></a>RCC_MCO1Config</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[113]"></a>RCC_MCO2Config</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[114]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1b2]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[115]"></a>RCC_HCLKConfig</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[116]"></a>RCC_PCLK1Config</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[117]"></a>RCC_PCLK2Config</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[94]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[118]"></a>RCC_RTCCLKConfig</STRONG> (Thumb, 260 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[119]"></a>RCC_RTCCLKCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[11a]"></a>RCC_BackupResetCmd</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[11b]"></a>RCC_I2SCLKConfig</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[99]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init_Single_Channel
</UL>

<P><STRONG><a name="[11c]"></a>RCC_AHB2PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[11d]"></a>RCC_AHB3PeriphClockCmd</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ad]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[9a]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init_Single_Channel
</UL>

<P><STRONG><a name="[fd]"></a>RCC_AHB1PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_DeInit
</UL>

<P><STRONG><a name="[11e]"></a>RCC_AHB2PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[11f]"></a>RCC_AHB3PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[120]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>

<P><STRONG><a name="[db]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DeInit
</UL>

<P><STRONG><a name="[121]"></a>RCC_AHB1PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[122]"></a>RCC_AHB2PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[123]"></a>RCC_AHB3PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[124]"></a>RCC_APB1PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[125]"></a>RCC_APB2PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[126]"></a>RCC_ITConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1b3]"></a>RCC_ClearFlag</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[127]"></a>RCC_GetITStatus</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[128]"></a>RCC_ClearITPendingBit</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[bc]"></a>TIM_DeInit</STRONG> (Thumb, 440 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[af]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 278 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_TimeBaseInit
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[ab]"></a>TIM_TimeBaseStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[129]"></a>TIM_PrescalerConfig</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[12a]"></a>TIM_CounterModeConfig</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[12b]"></a>TIM_SetCounter</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[12c]"></a>TIM_SetAutoreload</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[12d]"></a>TIM_GetCounter</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[12e]"></a>TIM_GetPrescaler</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[12f]"></a>TIM_UpdateDisableConfig</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[130]"></a>TIM_UpdateRequestConfig</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[b9]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ARRPreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[131]"></a>TIM_SelectOnePulseMode</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[132]"></a>TIM_SetClockDivision</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[ba]"></a>TIM_Cmd</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b0]"></a>TIM_OC1Init</STRONG> (Thumb, 400 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC1Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b2]"></a>TIM_OC2Init</STRONG> (Thumb, 474 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC2Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b4]"></a>TIM_OC3Init</STRONG> (Thumb, 400 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC3Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b6]"></a>TIM_OC4Init</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC4Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[ac]"></a>TIM_OCStructInit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[133]"></a>TIM_SelectOCxM</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[134]"></a>TIM_SetCompare1</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[135]"></a>TIM_SetCompare2</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[136]"></a>TIM_SetCompare3</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[137]"></a>TIM_SetCompare4</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[138]"></a>TIM_ForcedOC1Config</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[139]"></a>TIM_ForcedOC2Config</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[13a]"></a>TIM_ForcedOC3Config</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[13b]"></a>TIM_ForcedOC4Config</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[b1]"></a>TIM_OC1PreloadConfig</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC1PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b3]"></a>TIM_OC2PreloadConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC2PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b5]"></a>TIM_OC3PreloadConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC3PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[b7]"></a>TIM_OC4PreloadConfig</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC4PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[13c]"></a>TIM_OC1FastConfig</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[13d]"></a>TIM_OC2FastConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[13e]"></a>TIM_OC3FastConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[13f]"></a>TIM_OC4FastConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[140]"></a>TIM_ClearOC1Ref</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[141]"></a>TIM_ClearOC2Ref</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[142]"></a>TIM_ClearOC3Ref</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[143]"></a>TIM_ClearOC4Ref</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[144]"></a>TIM_OC1PolarityConfig</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[145]"></a>TIM_OC1NPolarityConfig</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[146]"></a>TIM_OC2PolarityConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[147]"></a>TIM_OC2NPolarityConfig</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[148]"></a>TIM_OC3PolarityConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[149]"></a>TIM_OC3NPolarityConfig</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[14a]"></a>TIM_OC4PolarityConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[14b]"></a>TIM_CCxCmd</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[14c]"></a>TIM_CCxNCmd</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[14d]"></a>TIM_SetIC4Prescaler</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[14e]"></a>TIM_SetIC3Prescaler</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[14f]"></a>TIM_SetIC2Prescaler</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[150]"></a>TIM_SetIC1Prescaler</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[151]"></a>TIM_ICInit</STRONG> (Thumb, 460 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC3Prescaler
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC4Prescaler
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI3_Config
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI4_Config
</UL>

<P><STRONG><a name="[1b4]"></a>TIM_ICStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[156]"></a>TIM_PWMIConfig</STRONG> (Thumb, 234 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>

<P><STRONG><a name="[157]"></a>TIM_GetCapture1</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[158]"></a>TIM_GetCapture2</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[159]"></a>TIM_GetCapture3</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[15a]"></a>TIM_GetCapture4</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[15b]"></a>TIM_BDTRConfig</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1b5]"></a>TIM_BDTRStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[b8]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_CtrlPWMOutputs
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[15c]"></a>TIM_SelectCOM</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[15d]"></a>TIM_CCPreloadControl</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[be]"></a>TIM_ITConfig</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[15e]"></a>TIM_GenerateEvent</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[15f]"></a>TIM_GetFlagStatus</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[bd]"></a>TIM_ClearFlag</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ClearFlag
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[8c]"></a>TIM_GetITStatus</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
</UL>

<P><STRONG><a name="[8d]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
</UL>

<P><STRONG><a name="[160]"></a>TIM_DMAConfig</STRONG> (Thumb, 328 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[161]"></a>TIM_DMACmd</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[162]"></a>TIM_SelectCCDMA</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[163]"></a>TIM_InternalClockConfig</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[164]"></a>TIM_SelectInputTrigger</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRxExternalClockConfig
</UL>

<P><STRONG><a name="[165]"></a>TIM_ITRxExternalClockConfig</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
</UL>

<P><STRONG><a name="[166]"></a>TIM_TIxExternalClockConfig</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>

<P><STRONG><a name="[167]"></a>TIM_ETRConfig</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode2Config
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode1Config
</UL>

<P><STRONG><a name="[168]"></a>TIM_ETRClockMode1Config</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
</UL>

<P><STRONG><a name="[169]"></a>TIM_ETRClockMode2Config</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
</UL>

<P><STRONG><a name="[16a]"></a>TIM_SelectOutputTrigger</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[16b]"></a>TIM_SelectSlaveMode</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[16c]"></a>TIM_SelectMasterSlaveMode</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[16d]"></a>TIM_EncoderInterfaceConfig</STRONG> (Thumb, 226 bytes, Stack size 32 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[16e]"></a>TIM_SelectHallSensor</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[16f]"></a>TIM_RemapConfig</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[c2]"></a>USART_DeInit</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[c3]"></a>USART_Init</STRONG> (Thumb, 456 bytes, Stack size 48 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[c1]"></a>USART_StructInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[c4]"></a>USART_ClockInit</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_ClockInit
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[1b6]"></a>USART_ClockStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[c6]"></a>USART_Cmd</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[170]"></a>USART_SetPrescaler</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[171]"></a>USART_OverSampling8Cmd</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[172]"></a>USART_OneBitMethodCmd</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[173]"></a>USART_SendData</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[174]"></a>USART_ReceiveData</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[175]"></a>USART_SetAddress</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[176]"></a>USART_ReceiverWakeUpCmd</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[177]"></a>USART_WakeUpConfig</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[178]"></a>USART_LINBreakDetectLengthConfig</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[179]"></a>USART_LINCmd</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17a]"></a>USART_SendBreak</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17b]"></a>USART_HalfDuplexCmd</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17c]"></a>USART_SetGuardTime</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17d]"></a>USART_SmartCardCmd</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17e]"></a>USART_SmartCardNACKCmd</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17f]"></a>USART_IrDAConfig</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[180]"></a>USART_IrDACmd</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[181]"></a>USART_DMACmd</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[c5]"></a>USART_ITConfig</STRONG> (Thumb, 260 bytes, Stack size 32 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4SendBuf
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3SendBuf
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2SendBuf
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1SendBuf
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[182]"></a>USART_GetFlagStatus</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[183]"></a>USART_ClearFlag</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[c8]"></a>USART_GetITStatus</STRONG> (Thumb, 278 bytes, Stack size 32 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
</UL>

<P><STRONG><a name="[c9]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
</UL>

<P><STRONG><a name="[87]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;no_animal
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_zong
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_pandian
</UL>

<P><STRONG><a name="[184]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>

<P><STRONG><a name="[189]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[18a]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IRDistance
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
</UL>

<P><STRONG><a name="[18b]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[a4]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IRDistance
</UL>

<P><STRONG><a name="[19c]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[a7]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IRDistance
</UL>

<P><STRONG><a name="[1b7]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[19f]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[185]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[1b8]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[186]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1b9]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[1ba]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[18c]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[1bb]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[188]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[187]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[18e]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>

<P><STRONG><a name="[19d]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1bc]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[69]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[1bd]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[18d]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[1be]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[190]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[191]"></a>__0sprintf$3</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printf3.o(i.__0sprintf$3), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1bf]"></a>__1sprintf$3</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0sprintf$3), UNUSED)

<P><STRONG><a name="[86]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0sprintf$3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;no_animal
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_zong
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_pandian
</UL>

<P><STRONG><a name="[19e]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[a5]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IRDistance
</UL>

<P><STRONG><a name="[19b]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[195]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[193]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[199]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[196]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[19a]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1c0]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[1c1]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[1c2]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[194]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[198]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[197]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[61]"></a>Loop_1000Hz</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ano_scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = Loop_1000Hz &rArr; user_task &rArr; Send_zong &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_task
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[62]"></a>Loop_500Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[63]"></a>Loop_200Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[64]"></a>Loop_100Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[65]"></a>Loop_50Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[66]"></a>Loop_20Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[67]"></a>Loop_2Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[d6]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[155]"></a>TI4_Config</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[154]"></a>TI3_Config</STRONG> (Thumb, 72 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[153]"></a>TI2_Config</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[152]"></a>TI1_Config</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[192]"></a>_printf_core</STRONG> (Thumb, 436 bytes, Stack size 96 bytes, printf3.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$3
</UL>

<P><STRONG><a name="[60]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printf3.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$3
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf3.o(i.__0sprintf$3)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
