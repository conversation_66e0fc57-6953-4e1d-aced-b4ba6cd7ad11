<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>DSP_Lib CM4 LE</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>Cortex-M4</Device>
          <Vendor>ARM</Vendor>
          <Cpu>CLOCK(12000000) CPUTYPE("Cortex-M4") ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>5125</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\intermediateFiles\</OutputDirectory>
          <OutputName>arm_cortexM4l_math</OutputName>
          <CreateExecutable>0</CreateExecutable>
          <CreateLib>1</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\intermediateFiles\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "!L" "..\..\..\Lib\ARM\"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>-1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver></Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>0</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <Flash2></Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>0</hadIROM>
            <hadIRAM>0</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>1</StupSel>
            <useUlib>0</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>0</RoSelD>
            <RwSelD>5</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>1</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM>
              <IROM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>ARM_MATH_CM4, ARM_MATH_MATRIX_CHECK, ARM_MATH_ROUNDING</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>BasicMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FastMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ComplexMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cmplx_conj_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FilteringFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>MatrixFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_mat_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_inverse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_inverse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>TransformFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cfft_radix4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ControllerFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_pid_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>StatisticsFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_max_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SupportFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_copy_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q15.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CommonTables</GroupName>
          <Files>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CommonTables\arm_common_tables.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>DSP_Lib CM4 LE FPU</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>Cortex-M4 FPU</Device>
          <Vendor>ARM</Vendor>
          <Cpu>CLOCK(12000000) CPUTYPE("Cortex-M4") ESEL ELITTLE FPU2</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>5237</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\intermediateFiles\</OutputDirectory>
          <OutputName>arm_cortexM4lf_math</OutputName>
          <CreateExecutable>0</CreateExecutable>
          <CreateLib>1</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\intermediateFiles\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "!L" "..\..\..\Lib\ARM\"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>-1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver></Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>0</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <Flash2></Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>0</hadIROM>
            <hadIRAM>0</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>1</StupSel>
            <useUlib>0</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>0</RoSelD>
            <RwSelD>5</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>1</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM>
              <IROM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>ARM_MATH_CM4, ARM_MATH_MATRIX_CHECK, ARM_MATH_ROUNDING, __FPU_PRESENT = 1</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>BasicMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FastMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ComplexMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cmplx_conj_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FilteringFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>MatrixFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_mat_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_inverse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_inverse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>TransformFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cfft_radix4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ControllerFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_pid_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>StatisticsFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_max_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SupportFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_copy_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q15.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CommonTables</GroupName>
          <Files>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CommonTables\arm_common_tables.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>DSP_Lib CM4 BE</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>Cortex-M4</Device>
          <Vendor>ARM</Vendor>
          <Cpu>CLOCK(12000000) CPUTYPE("Cortex-M4") ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>5125</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\intermediateFiles\</OutputDirectory>
          <OutputName>arm_cortexM4b_math</OutputName>
          <CreateExecutable>0</CreateExecutable>
          <CreateLib>1</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\intermediateFiles\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "!L" "..\..\..\Lib\ARM\"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>-1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver></Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>0</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <Flash2></Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>1</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>0</hadIROM>
            <hadIRAM>0</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>1</StupSel>
            <useUlib>0</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>0</RoSelD>
            <RwSelD>5</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>1</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM>
              <IROM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>ARM_MATH_CM4, ARM_MATH_MATRIX_CHECK, ARM_MATH_ROUNDING,ARM_MATH_BIG_ENDIAN</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>BasicMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FastMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ComplexMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cmplx_conj_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FilteringFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>MatrixFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_mat_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_inverse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_inverse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>TransformFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cfft_radix4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ControllerFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_pid_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>StatisticsFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_max_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SupportFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_copy_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q15.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CommonTables</GroupName>
          <Files>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CommonTables\arm_common_tables.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>DSP_Lib CM4 BE FPU</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>Cortex-M4 FPU</Device>
          <Vendor>ARM</Vendor>
          <Cpu>CLOCK(12000000) CPUTYPE("Cortex-M4") ESEL ELITTLE FPU2</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>5237</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\intermediateFiles\</OutputDirectory>
          <OutputName>arm_cortexM4bf_math</OutputName>
          <CreateExecutable>0</CreateExecutable>
          <CreateLib>1</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\intermediateFiles\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "!L" "..\..\..\Lib\ARM\"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>-1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver></Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>0</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <Flash2></Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>1</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>0</hadIROM>
            <hadIRAM>0</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>1</StupSel>
            <useUlib>0</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>0</RoSelD>
            <RwSelD>5</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>1</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM>
              <IROM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>ARM_MATH_CM4, ARM_MATH_MATRIX_CHECK, ARM_MATH_ROUNDING, ARM_MATH_BIG_ENDIAN, __FPU_PRESENT = 1</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>BasicMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_abs_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_negate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_negate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_offset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_offset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_shift_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_shift_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BasicMathFunctions\arm_sub_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FastMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FastMathFunctions\arm_sqrt_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ComplexMathFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cmplx_conj_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_conj_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_conj_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_dot_prod_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mag_squared_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_cmplx_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cmplx_mult_real_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ComplexMathFunctions\arm_cmplx_mult_real_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FilteringFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_32x64_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df1_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df1_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_biquad_cascade_df2T_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_partial_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_partial_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_conv_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_conv_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_correlate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_correlate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_decimate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_decimate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_interpolate_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_interpolate_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fir_sparse_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_fir_sparse_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_iir_lattice_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_iir_lattice_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_norm_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_norm_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_lms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FilteringFunctions\arm_lms_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>MatrixFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_mat_add_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_add_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_add_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_inverse_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_inverse_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_fast_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_fast_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_mult_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_mult_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_scale_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_scale_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_sub_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_sub_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mat_trans_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\MatrixFunctions\arm_mat_trans_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>TransformFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_cfft_radix4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cfft_radix4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_cfft_radix4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_dct4_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_dct4_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rfft_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\TransformFunctions\arm_rfft_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ControllerFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_pid_init_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_init_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_init_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_pid_reset_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_pid_reset_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ControllerFunctions\arm_sin_cos_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>StatisticsFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_max_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_max_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_max_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_mean_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_mean_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_min_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_min_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_power_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_power_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_rms_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_rms_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_std_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_std_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_var_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\StatisticsFunctions\arm_var_q31.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SupportFunctions</GroupName>
          <Files>
            <File>
              <FileName>arm_copy_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_copy_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_copy_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_fill_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_fill_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_float_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q7_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q7_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q7.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SupportFunctions\arm_q31_to_q15.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CommonTables</GroupName>
          <Files>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CommonTables\arm_common_tables.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
