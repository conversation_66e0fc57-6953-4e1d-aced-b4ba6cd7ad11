


ARM Macro Assembler    Page 1 


    1 00000000         ;/******************************************************
                       ********************//**
    2 00000000         ; * @file     startup_TM4C123.s
    3 00000000         ; * @brief    CMSIS Cortex-M4 Core Device Startup File f
                       or
    4 00000000         ; *           TI Tiva TM4C123 Blizzard Class Device
    5 00000000         ; * @version  V1.00
    6 00000000         ; * @date     15. May 2013
    7 00000000         ; *
    8 00000000         ; * @note
    9 00000000         ; * Copyright (C) 2011 ARM Limited. All rights reserved.
                       
   10 00000000         ; *
   11 00000000         ; * @par
   12 00000000         ; * ARM Limited (ARM) is supplying this software for use
                        with Cortex-M
   13 00000000         ; * processor based microcontrollers.  This file can be 
                       freely distributed
   14 00000000         ; * within development tools that are supporting such AR
                       M based processors.
   15 00000000         ; *
   16 00000000         ; * @par
   17 00000000         ; * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, W
                       HETHER EXPRESS, IMPLIED
   18 00000000         ; * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED
                        WARRANTIES OF
   19 00000000         ; * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
                        APPLY TO THIS SOFTWARE.
   20 00000000         ; * ARM SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR S
                       PECIAL, INCIDENTAL, OR
   21 00000000         ; * CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
   22 00000000         ; *
   23 00000000         ; ******************************************************
                       ************************/
   24 00000000         ;/*
   25 00000000         ;//-------- <<< Use Configuration Wizard in Context Menu
                        >>> ------------------
   26 00000000         ;*/
   27 00000000         
   28 00000000         
   29 00000000         ; <h> Stack Configuration
   30 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   31 00000000         ; </h>
   32 00000000         
   33 00000000 00000A00 
                       Stack_Size
                               EQU              0x00000A00
   34 00000000         
   35 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   36 00000000         Stack_Mem
                               SPACE            Stack_Size
   37 00000A00         __initial_sp
   38 00000A00         
   39 00000A00         
   40 00000A00         ; <h> Heap Configuration
   41 00000A00         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   42 00000A00         ; </h>
   43 00000A00         



ARM Macro Assembler    Page 2 


   44 00000A00 00000500 
                       Heap_Size
                               EQU              0x00000500
   45 00000A00         
   46 00000A00                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   47 00000000         __heap_base
   48 00000000         Heap_Mem
                               SPACE            Heap_Size
   49 00000500         __heap_limit
   50 00000500         
   51 00000500         
   52 00000500                 PRESERVE8
   53 00000500                 THUMB
   54 00000500         
   55 00000500         
   56 00000500         ; Vector Table Mapped to Address 0 at Reset
   57 00000500         
   58 00000500                 AREA             RESET, DATA, READONLY
   59 00000000                 EXPORT           __Vectors
   60 00000000                 EXPORT           __Vectors_End
   61 00000000                 EXPORT           __Vectors_Size
   62 00000000         
   63 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   64 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   65 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   66 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   67 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   68 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   69 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   70 0000001C 00000000        DCD              0           ; Reserved
   71 00000020 00000000        DCD              0           ; Reserved
   72 00000024 00000000        DCD              0           ; Reserved
   73 00000028 00000000        DCD              0           ; Reserved
   74 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   75 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   76 00000034 00000000        DCD              0           ; Reserved
   77 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   78 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   79 00000040         
   80 00000040         ; External Interrupts
   81 00000040         
   82 00000040 00000000        DCD              GPIOA_Handler 
                                                            ;   0: GPIO Port A
   83 00000044 00000000        DCD              GPIOB_Handler 
                                                            ;   1: GPIO Port B
   84 00000048 00000000        DCD              GPIOC_Handler 
                                                            ;   2: GPIO Port C



ARM Macro Assembler    Page 3 


   85 0000004C 00000000        DCD              GPIOD_Handler 
                                                            ;   3: GPIO Port D
   86 00000050 00000000        DCD              GPIOE_Handler 
                                                            ;   4: GPIO Port E
   87 00000054 00000000        DCD              UART0_Handler ;   5: UART0 Rx a
                                                            nd Tx
   88 00000058 00000000        DCD              UART1_Handler ;   6: UART1 Rx a
                                                            nd Tx
   89 0000005C 00000000        DCD              SSI0_Handler ;   7: SSI0 Rx and
                                                             Tx
   90 00000060 00000000        DCD              I2C0_Handler ;   8: I2C0 Master
                                                             and Slave
   91 00000064 00000000        DCD              PMW0_FAULT_Handler 
                                                            ;   9: PWM Fault
   92 00000068 00000000        DCD              PWM0_0_Handler ;  10: PWM Gener
                                                            ator 0
   93 0000006C 00000000        DCD              PWM0_1_Handler ;  11: PWM Gener
                                                            ator 1
   94 00000070 00000000        DCD              PWM0_2_Handler ;  12: PWM Gener
                                                            ator 2
   95 00000074 00000000        DCD              QEI0_Handler ;  13: Quadrature 
                                                            Encoder 0
   96 00000078 00000000        DCD              ADC0SS0_Handler ;  14: ADC Sequ
                                                            ence 0
   97 0000007C 00000000        DCD              ADC0SS1_Handler ;  15: ADC Sequ
                                                            ence 1
   98 00000080 00000000        DCD              ADC0SS2_Handler ;  16: ADC Sequ
                                                            ence 2
   99 00000084 00000000        DCD              ADC0SS3_Handler ;  17: ADC Sequ
                                                            ence 3
  100 00000088 00000000        DCD              WDT0_Handler ;  18: Watchdog ti
                                                            mer
  101 0000008C 00000000        DCD              TIMER0A_Handler ;  19: Timer 0 
                                                            subtimer A
  102 00000090 00000000        DCD              TIMER0B_Handler ;  20: Timer 0 
                                                            subtimer B
  103 00000094 00000000        DCD              TIMER1A_Handler ;  21: Timer 1 
                                                            subtimer A
  104 00000098 00000000        DCD              TIMER1B_Handler ;  22: Timer 1 
                                                            subtimer B
  105 0000009C 00000000        DCD              TIMER2A_Handler ;  23: Timer 2 
                                                            subtimer A
  106 000000A0 00000000        DCD              TIMER2B_Handler ;  24: Timer 2 
                                                            subtimer B
  107 000000A4 00000000        DCD              COMP0_Handler ;  25: Analog Com
                                                            parator 0
  108 000000A8 00000000        DCD              COMP1_Handler ;  26: Analog Com
                                                            parator 1
  109 000000AC 00000000        DCD              COMP2_Handler ;  27: Analog Com
                                                            parator 2
  110 000000B0 00000000        DCD              SYSCTL_Handler ;  28: System Co
                                                            ntrol (PLL, OSC, BO
                                                            )
  111 000000B4 00000000        DCD              FLASH_Handler ;  29: FLASH Cont
                                                            rol
  112 000000B8 00000000        DCD              GPIOF_Handler 
                                                            ;  30: GPIO Port F
  113 000000BC 00000000        DCD              GPIOG_Handler 
                                                            ;  31: GPIO Port G



ARM Macro Assembler    Page 4 


  114 000000C0 00000000        DCD              GPIOH_Handler 
                                                            ;  32: GPIO Port H
  115 000000C4 00000000        DCD              UART2_Handler ;  33: UART2 Rx a
                                                            nd Tx
  116 000000C8 00000000        DCD              SSI1_Handler ;  34: SSI1 Rx and
                                                             Tx
  117 000000CC 00000000        DCD              TIMER3A_Handler ;  35: Timer 3 
                                                            subtimer A
  118 000000D0 00000000        DCD              TIMER3B_Handler ;  36: Timer 3 
                                                            subtimer B
  119 000000D4 00000000        DCD              I2C1_Handler ;  37: I2C1 Master
                                                             and Slave
  120 000000D8 00000000        DCD              QEI1_Handler ;  38: Quadrature 
                                                            Encoder 1
  121 000000DC 00000000        DCD              CAN0_Handler ;  39: CAN0
  122 000000E0 00000000        DCD              CAN1_Handler ;  40: CAN1
  123 000000E4 00000000        DCD              CAN2_Handler ;  41: CAN2
  124 000000E8 00000000        DCD              0           ;  42: Reserved
  125 000000EC 00000000        DCD              HIB_Handler ;  43: Hibernate
  126 000000F0 00000000        DCD              USB0_Handler ;  44: USB0
  127 000000F4 00000000        DCD              PWM0_3_Handler ;  45: PWM Gener
                                                            ator 3
  128 000000F8 00000000        DCD              UDMA_Handler ;  46: uDMA Softwa
                                                            re Transfer
  129 000000FC 00000000        DCD              UDMAERR_Handler 
                                                            ;  47: uDMA Error
  130 00000100 00000000        DCD              ADC1SS0_Handler ;  48: ADC1 Seq
                                                            uence 0
  131 00000104 00000000        DCD              ADC1SS1_Handler ;  49: ADC1 Seq
                                                            uence 1
  132 00000108 00000000        DCD              ADC1SS2_Handler ;  50: ADC1 Seq
                                                            uence 2
  133 0000010C 00000000        DCD              ADC1SS3_Handler ;  51: ADC1 Seq
                                                            uence 3
  134 00000110 00000000        DCD              0           ;  52: Reserved
  135 00000114 00000000        DCD              0           ;  53: Reserved
  136 00000118 00000000        DCD              GPIOJ_Handler 
                                                            ;  54: GPIO Port J
  137 0000011C 00000000        DCD              GPIOK_Handler 
                                                            ;  55: GPIO Port K
  138 00000120 00000000        DCD              GPIOL_Handler 
                                                            ;  56: GPIO Port L
  139 00000124 00000000        DCD              SSI2_Handler ;  57: SSI2 Rx and
                                                             Tx
  140 00000128 00000000        DCD              SSI3_Handler ;  58: SSI3 Rx and
                                                             Tx
  141 0000012C 00000000        DCD              UART3_Handler ;  59: UART3 Rx a
                                                            nd Tx
  142 00000130 00000000        DCD              UART4_Handler ;  60: UART4 Rx a
                                                            nd Tx
  143 00000134 00000000        DCD              UART5_Handler ;  61: UART5 Rx a
                                                            nd Tx
  144 00000138 00000000        DCD              UART6_Handler ;  62: UART6 Rx a
                                                            nd Tx
  145 0000013C 00000000        DCD              UART7_Handler ;  63: UART7 Rx a
                                                            nd Tx
  146 00000140 00000000        DCD              0           ;  64: Reserved
  147 00000144 00000000        DCD              0           ;  65: Reserved
  148 00000148 00000000        DCD              0           ;  66: Reserved



ARM Macro Assembler    Page 5 


  149 0000014C 00000000        DCD              0           ;  67: Reserved
  150 00000150 00000000        DCD              I2C2_Handler ;  68: I2C2 Master
                                                             and Slave
  151 00000154 00000000        DCD              I2C3_Handler ;  69: I2C3 Master
                                                             and Slave
  152 00000158 00000000        DCD              TIMER4A_Handler ;  70: Timer 4 
                                                            subtimer A
  153 0000015C 00000000        DCD              TIMER4B_Handler ;  71: Timer 4 
                                                            subtimer B
  154 00000160 00000000        DCD              0           ;  72: Reserved
  155 00000164 00000000        DCD              0           ;  73: Reserved
  156 00000168 00000000        DCD              0           ;  74: Reserved
  157 0000016C 00000000        DCD              0           ;  75: Reserved
  158 00000170 00000000        DCD              0           ;  76: Reserved
  159 00000174 00000000        DCD              0           ;  77: Reserved
  160 00000178 00000000        DCD              0           ;  78: Reserved
  161 0000017C 00000000        DCD              0           ;  79: Reserved
  162 00000180 00000000        DCD              0           ;  80: Reserved
  163 00000184 00000000        DCD              0           ;  81: Reserved
  164 00000188 00000000        DCD              0           ;  82: Reserved
  165 0000018C 00000000        DCD              0           ;  83: Reserved
  166 00000190 00000000        DCD              0           ;  84: Reserved
  167 00000194 00000000        DCD              0           ;  85: Reserved
  168 00000198 00000000        DCD              0           ;  86: Reserved
  169 0000019C 00000000        DCD              0           ;  87: Reserved
  170 000001A0 00000000        DCD              0           ;  88: Reserved
  171 000001A4 00000000        DCD              0           ;  89: Reserved
  172 000001A8 00000000        DCD              0           ;  90: Reserved
  173 000001AC 00000000        DCD              0           ;  91: Reserved
  174 000001B0 00000000        DCD              TIMER5A_Handler ;  92: Timer 5 
                                                            subtimer A
  175 000001B4 00000000        DCD              TIMER5B_Handler ;  93: Timer 5 
                                                            subtimer B
  176 000001B8 00000000        DCD              WTIMER0A_Handler ;  94: Wide Ti
                                                            mer 0 subtimer A
  177 000001BC 00000000        DCD              WTIMER0B_Handler ;  95: Wide Ti
                                                            mer 0 subtimer B
  178 000001C0 00000000        DCD              WTIMER1A_Handler ;  96: Wide Ti
                                                            mer 1 subtimer A
  179 000001C4 00000000        DCD              WTIMER1B_Handler ;  97: Wide Ti
                                                            mer 1 subtimer B
  180 000001C8 00000000        DCD              WTIMER2A_Handler ;  98: Wide Ti
                                                            mer 2 subtimer A
  181 000001CC 00000000        DCD              WTIMER2B_Handler ;  99: Wide Ti
                                                            mer 2 subtimer B
  182 000001D0 00000000        DCD              WTIMER3A_Handler ; 100: Wide Ti
                                                            mer 3 subtimer A
  183 000001D4 00000000        DCD              WTIMER3B_Handler ; 101: Wide Ti
                                                            mer 3 subtimer B
  184 000001D8 00000000        DCD              WTIMER4A_Handler ; 102: Wide Ti
                                                            mer 4 subtimer A
  185 000001DC 00000000        DCD              WTIMER4B_Handler ; 103: Wide Ti
                                                            mer 4 subtimer B
  186 000001E0 00000000        DCD              WTIMER5A_Handler ; 104: Wide Ti
                                                            mer 5 subtimer A
  187 000001E4 00000000        DCD              WTIMER5B_Handler ; 105: Wide Ti
                                                            mer 5 subtimer B
  188 000001E8 00000000        DCD              FPU_Handler ; 106: FPU
  189 000001EC 00000000        DCD              0           ; 107: Reserved



ARM Macro Assembler    Page 6 


  190 000001F0 00000000        DCD              0           ; 108: Reserved
  191 000001F4 00000000        DCD              I2C4_Handler ; 109: I2C4 Master
                                                             and Slave
  192 000001F8 00000000        DCD              I2C5_Handler ; 110: I2C5 Master
                                                             and Slave
  193 000001FC 00000000        DCD              GPIOM_Handler 
                                                            ; 111: GPIO Port M
  194 00000200 00000000        DCD              GPION_Handler 
                                                            ; 112: GPIO Port N
  195 00000204 00000000        DCD              QEI2_Handler ; 113: Quadrature 
                                                            Encoder 2
  196 00000208 00000000        DCD              0           ; 114: Reserved
  197 0000020C 00000000        DCD              0           ; 115: Reserved
  198 00000210 00000000        DCD              GPIOP0_Handler ; 116: GPIO Port
                                                             P (Summary or P0)
  199 00000214 00000000        DCD              GPIOP1_Handler 
                                                            ; 117: GPIO Port P1
                                                            
  200 00000218 00000000        DCD              GPIOP2_Handler 
                                                            ; 118: GPIO Port P2
                                                            
  201 0000021C 00000000        DCD              GPIOP3_Handler 
                                                            ; 119: GPIO Port P3
                                                            
  202 00000220 00000000        DCD              GPIOP4_Handler 
                                                            ; 120: GPIO Port P4
                                                            
  203 00000224 00000000        DCD              GPIOP5_Handler 
                                                            ; 121: GPIO Port P5
                                                            
  204 00000228 00000000        DCD              GPIOP6_Handler 
                                                            ; 122: GPIO Port P6
                                                            
  205 0000022C 00000000        DCD              GPIOP7_Handler 
                                                            ; 123: GPIO Port P7
                                                            
  206 00000230 00000000        DCD              GPIOQ0_Handler ; 124: GPIO Port
                                                             Q (Summary or Q0)
  207 00000234 00000000        DCD              GPIOQ1_Handler 
                                                            ; 125: GPIO Port Q1
                                                            
  208 00000238 00000000        DCD              GPIOQ2_Handler 
                                                            ; 126: GPIO Port Q2
                                                            
  209 0000023C 00000000        DCD              GPIOQ3_Handler 
                                                            ; 127: GPIO Port Q3
                                                            
  210 00000240 00000000        DCD              GPIOQ4_Handler 
                                                            ; 128: GPIO Port Q4
                                                            
  211 00000244 00000000        DCD              GPIOQ5_Handler 
                                                            ; 129: GPIO Port Q5
                                                            
  212 00000248 00000000        DCD              GPIOQ6_Handler 
                                                            ; 130: GPIO Port Q6
                                                            
  213 0000024C 00000000        DCD              GPIOQ7_Handler 
                                                            ; 131: GPIO Port Q7
                                                            



ARM Macro Assembler    Page 7 


  214 00000250 00000000        DCD              GPIOR_Handler 
                                                            ; 132: GPIO Port R
  215 00000254 00000000        DCD              GPIOS_Handler 
                                                            ; 133: GPIO Port S
  216 00000258 00000000        DCD              PMW1_0_Handler ; 134: PWM 1 Gen
                                                            erator 0
  217 0000025C 00000000        DCD              PWM1_1_Handler ; 135: PWM 1 Gen
                                                            erator 1
  218 00000260 00000000        DCD              PWM1_2_Handler ; 136: PWM 1 Gen
                                                            erator 2
  219 00000264 00000000        DCD              PWM1_3_Handler ; 137: PWM 1 Gen
                                                            erator 3
  220 00000268 00000000        DCD              PWM1_FAULT_Handler 
                                                            ; 138: PWM 1 Fault
  221 0000026C         
  222 0000026C         __Vectors_End
  223 0000026C         
  224 0000026C 0000026C 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  225 0000026C         
  226 0000026C                 AREA             |.text|, CODE, READONLY
  227 00000000         
  228 00000000         
  229 00000000         ; Reset Handler
  230 00000000         
  231 00000000         Reset_Handler
                               PROC
  232 00000000                 EXPORT           Reset_Handler             [WEAK
]
  233 00000000                 IMPORT           SystemInit
  234 00000000                 IMPORT           __main
  235 00000000 483C            LDR              R0, =SystemInit
  236 00000002 4780            BLX              R0
  237 00000004 483C            LDR              R0, =__main
  238 00000006 4700            BX               R0
  239 00000008                 ENDP
  240 00000008         
  241 00000008         
  242 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  243 00000008         
  244 00000008         NMI_Handler
                               PROC
  245 00000008                 EXPORT           NMI_Handler               [WEAK
]
  246 00000008 E7FE            B                .
  247 0000000A                 ENDP
  249 0000000A         HardFault_Handler
                               PROC
  250 0000000A                 EXPORT           HardFault_Handler         [WEAK
]
  251 0000000A E7FE            B                .
  252 0000000C                 ENDP
  254 0000000C         MemManage_Handler
                               PROC
  255 0000000C                 EXPORT           MemManage_Handler         [WEAK
]
  256 0000000C E7FE            B                .



ARM Macro Assembler    Page 8 


  257 0000000E                 ENDP
  259 0000000E         BusFault_Handler
                               PROC
  260 0000000E                 EXPORT           BusFault_Handler          [WEAK
]
  261 0000000E E7FE            B                .
  262 00000010                 ENDP
  264 00000010         UsageFault_Handler
                               PROC
  265 00000010                 EXPORT           UsageFault_Handler        [WEAK
]
  266 00000010 E7FE            B                .
  267 00000012                 ENDP
  268 00000012         SVC_Handler
                               PROC
  269 00000012                 EXPORT           SVC_Handler               [WEAK
]
  270 00000012 E7FE            B                .
  271 00000014                 ENDP
  273 00000014         DebugMon_Handler
                               PROC
  274 00000014                 EXPORT           DebugMon_Handler          [WEAK
]
  275 00000014 E7FE            B                .
  276 00000016                 ENDP
  278 00000016         PendSV_Handler
                               PROC
  279 00000016                 EXPORT           PendSV_Handler            [WEAK
]
  280 00000016 E7FE            B                .
  281 00000018                 ENDP
  283 00000018         SysTick_Handler
                               PROC
  284 00000018                 EXPORT           SysTick_Handler           [WEAK
]
  285 00000018 E7FE            B                .
  286 0000001A                 ENDP
  287 0000001A         
  289 0000001A         GPIOA_Handler
                               PROC
  290 0000001A                 EXPORT           GPIOA_Handler [WEAK]
  291 0000001A E7FE            B                .
  292 0000001C                 ENDP
  293 0000001C         
  295 0000001C         GPIOB_Handler
                               PROC
  296 0000001C                 EXPORT           GPIOB_Handler [WEAK]
  297 0000001C E7FE            B                .
  298 0000001E                 ENDP
  299 0000001E         
  301 0000001E         GPIOC_Handler
                               PROC
  302 0000001E                 EXPORT           GPIOC_Handler [WEAK]
  303 0000001E E7FE            B                .
  304 00000020                 ENDP
  305 00000020         
  307 00000020         GPIOD_Handler
                               PROC
  308 00000020                 EXPORT           GPIOD_Handler [WEAK]



ARM Macro Assembler    Page 9 


  309 00000020 E7FE            B                .
  310 00000022                 ENDP
  311 00000022         
  313 00000022         GPIOE_Handler
                               PROC
  314 00000022                 EXPORT           GPIOE_Handler [WEAK]
  315 00000022 E7FE            B                .
  316 00000024                 ENDP
  317 00000024         
  319 00000024         UART0_Handler
                               PROC
  320 00000024                 EXPORT           UART0_Handler [WEAK]
  321 00000024 E7FE            B                .
  322 00000026                 ENDP
  323 00000026         
  325 00000026         UART1_Handler
                               PROC
  326 00000026                 EXPORT           UART1_Handler [WEAK]
  327 00000026 E7FE            B                .
  328 00000028                 ENDP
  329 00000028         
  331 00000028         SSI0_Handler
                               PROC
  332 00000028                 EXPORT           SSI0_Handler [WEAK]
  333 00000028 E7FE            B                .
  334 0000002A                 ENDP
  335 0000002A         
  337 0000002A         I2C0_Handler
                               PROC
  338 0000002A                 EXPORT           I2C0_Handler [WEAK]
  339 0000002A E7FE            B                .
  340 0000002C                 ENDP
  341 0000002C         
  343 0000002C         PMW0_FAULT_Handler
                               PROC
  344 0000002C                 EXPORT           PMW0_FAULT_Handler [WEAK]
  345 0000002C E7FE            B                .
  346 0000002E                 ENDP
  347 0000002E         
  349 0000002E         PWM0_0_Handler
                               PROC
  350 0000002E                 EXPORT           PWM0_0_Handler [WEAK]
  351 0000002E E7FE            B                .
  352 00000030                 ENDP
  353 00000030         
  355 00000030         PWM0_1_Handler
                               PROC
  356 00000030                 EXPORT           PWM0_1_Handler [WEAK]
  357 00000030 E7FE            B                .
  358 00000032                 ENDP
  359 00000032         
  361 00000032         PWM0_2_Handler
                               PROC
  362 00000032                 EXPORT           PWM0_2_Handler [WEAK]
  363 00000032 E7FE            B                .
  364 00000034                 ENDP
  365 00000034         
  367 00000034         QEI0_Handler
                               PROC



ARM Macro Assembler    Page 10 


  368 00000034                 EXPORT           QEI0_Handler [WEAK]
  369 00000034 E7FE            B                .
  370 00000036                 ENDP
  371 00000036         
  373 00000036         ADC0SS0_Handler
                               PROC
  374 00000036                 EXPORT           ADC0SS0_Handler [WEAK]
  375 00000036 E7FE            B                .
  376 00000038                 ENDP
  377 00000038         
  379 00000038         ADC0SS1_Handler
                               PROC
  380 00000038                 EXPORT           ADC0SS1_Handler [WEAK]
  381 00000038 E7FE            B                .
  382 0000003A                 ENDP
  383 0000003A         
  385 0000003A         ADC0SS2_Handler
                               PROC
  386 0000003A                 EXPORT           ADC0SS2_Handler [WEAK]
  387 0000003A E7FE            B                .
  388 0000003C                 ENDP
  389 0000003C         
  391 0000003C         ADC0SS3_Handler
                               PROC
  392 0000003C                 EXPORT           ADC0SS3_Handler [WEAK]
  393 0000003C E7FE            B                .
  394 0000003E                 ENDP
  395 0000003E         
  397 0000003E         WDT0_Handler
                               PROC
  398 0000003E                 EXPORT           WDT0_Handler [WEAK]
  399 0000003E E7FE            B                .
  400 00000040                 ENDP
  401 00000040         
  403 00000040         TIMER0A_Handler
                               PROC
  404 00000040                 EXPORT           TIMER0A_Handler [WEAK]
  405 00000040 E7FE            B                .
  406 00000042                 ENDP
  407 00000042         
  409 00000042         TIMER0B_Handler
                               PROC
  410 00000042                 EXPORT           TIMER0B_Handler [WEAK]
  411 00000042 E7FE            B                .
  412 00000044                 ENDP
  413 00000044         
  415 00000044         TIMER1A_Handler
                               PROC
  416 00000044                 EXPORT           TIMER1A_Handler [WEAK]
  417 00000044 E7FE            B                .
  418 00000046                 ENDP
  419 00000046         
  421 00000046         TIMER1B_Handler
                               PROC
  422 00000046                 EXPORT           TIMER1B_Handler [WEAK]
  423 00000046 E7FE            B                .
  424 00000048                 ENDP
  425 00000048         
  427 00000048         TIMER2A_Handler



ARM Macro Assembler    Page 11 


                               PROC
  428 00000048                 EXPORT           TIMER2A_Handler [WEAK]
  429 00000048 E7FE            B                .
  430 0000004A                 ENDP
  431 0000004A         
  433 0000004A         TIMER2B_Handler
                               PROC
  434 0000004A                 EXPORT           TIMER2B_Handler [WEAK]
  435 0000004A E7FE            B                .
  436 0000004C                 ENDP
  437 0000004C         
  439 0000004C         COMP0_Handler
                               PROC
  440 0000004C                 EXPORT           COMP0_Handler [WEAK]
  441 0000004C E7FE            B                .
  442 0000004E                 ENDP
  443 0000004E         
  445 0000004E         COMP1_Handler
                               PROC
  446 0000004E                 EXPORT           COMP1_Handler [WEAK]
  447 0000004E E7FE            B                .
  448 00000050                 ENDP
  449 00000050         
  451 00000050         COMP2_Handler
                               PROC
  452 00000050                 EXPORT           COMP2_Handler [WEAK]
  453 00000050 E7FE            B                .
  454 00000052                 ENDP
  455 00000052         
  457 00000052         SYSCTL_Handler
                               PROC
  458 00000052                 EXPORT           SYSCTL_Handler [WEAK]
  459 00000052 E7FE            B                .
  460 00000054                 ENDP
  461 00000054         
  463 00000054         FLASH_Handler
                               PROC
  464 00000054                 EXPORT           FLASH_Handler [WEAK]
  465 00000054 E7FE            B                .
  466 00000056                 ENDP
  467 00000056         
  469 00000056         GPIOF_Handler
                               PROC
  470 00000056                 EXPORT           GPIOF_Handler [WEAK]
  471 00000056 E7FE            B                .
  472 00000058                 ENDP
  473 00000058         
  475 00000058         GPIOG_Handler
                               PROC
  476 00000058                 EXPORT           GPIOG_Handler [WEAK]
  477 00000058 E7FE            B                .
  478 0000005A                 ENDP
  479 0000005A         
  481 0000005A         GPIOH_Handler
                               PROC
  482 0000005A                 EXPORT           GPIOH_Handler [WEAK]
  483 0000005A E7FE            B                .
  484 0000005C                 ENDP
  485 0000005C         



ARM Macro Assembler    Page 12 


  487 0000005C         UART2_Handler
                               PROC
  488 0000005C                 EXPORT           UART2_Handler [WEAK]
  489 0000005C E7FE            B                .
  490 0000005E                 ENDP
  491 0000005E         
  493 0000005E         SSI1_Handler
                               PROC
  494 0000005E                 EXPORT           SSI1_Handler [WEAK]
  495 0000005E E7FE            B                .
  496 00000060                 ENDP
  497 00000060         
  499 00000060         TIMER3A_Handler
                               PROC
  500 00000060                 EXPORT           TIMER3A_Handler [WEAK]
  501 00000060 E7FE            B                .
  502 00000062                 ENDP
  503 00000062         
  505 00000062         TIMER3B_Handler
                               PROC
  506 00000062                 EXPORT           TIMER3B_Handler [WEAK]
  507 00000062 E7FE            B                .
  508 00000064                 ENDP
  509 00000064         
  511 00000064         I2C1_Handler
                               PROC
  512 00000064                 EXPORT           I2C1_Handler [WEAK]
  513 00000064 E7FE            B                .
  514 00000066                 ENDP
  515 00000066         
  517 00000066         QEI1_Handler
                               PROC
  518 00000066                 EXPORT           QEI1_Handler [WEAK]
  519 00000066 E7FE            B                .
  520 00000068                 ENDP
  521 00000068         
  523 00000068         CAN0_Handler
                               PROC
  524 00000068                 EXPORT           CAN0_Handler [WEAK]
  525 00000068 E7FE            B                .
  526 0000006A                 ENDP
  527 0000006A         
  529 0000006A         CAN1_Handler
                               PROC
  530 0000006A                 EXPORT           CAN1_Handler [WEAK]
  531 0000006A E7FE            B                .
  532 0000006C                 ENDP
  533 0000006C         
  535 0000006C         CAN2_Handler
                               PROC
  536 0000006C                 EXPORT           CAN2_Handler [WEAK]
  537 0000006C E7FE            B                .
  538 0000006E                 ENDP
  539 0000006E         
  541 0000006E         HIB_Handler
                               PROC
  542 0000006E                 EXPORT           HIB_Handler [WEAK]
  543 0000006E E7FE            B                .
  544 00000070                 ENDP



ARM Macro Assembler    Page 13 


  545 00000070         
  547 00000070         USB0_Handler
                               PROC
  548 00000070                 EXPORT           USB0_Handler [WEAK]
  549 00000070 E7FE            B                .
  550 00000072                 ENDP
  551 00000072         
  553 00000072         PWM0_3_Handler
                               PROC
  554 00000072                 EXPORT           PWM0_3_Handler [WEAK]
  555 00000072 E7FE            B                .
  556 00000074                 ENDP
  557 00000074         
  559 00000074         UDMA_Handler
                               PROC
  560 00000074                 EXPORT           UDMA_Handler [WEAK]
  561 00000074 E7FE            B                .
  562 00000076                 ENDP
  563 00000076         
  565 00000076         UDMAERR_Handler
                               PROC
  566 00000076                 EXPORT           UDMAERR_Handler [WEAK]
  567 00000076 E7FE            B                .
  568 00000078                 ENDP
  569 00000078         
  571 00000078         ADC1SS0_Handler
                               PROC
  572 00000078                 EXPORT           ADC1SS0_Handler [WEAK]
  573 00000078 E7FE            B                .
  574 0000007A                 ENDP
  575 0000007A         
  577 0000007A         ADC1SS1_Handler
                               PROC
  578 0000007A                 EXPORT           ADC1SS1_Handler [WEAK]
  579 0000007A E7FE            B                .
  580 0000007C                 ENDP
  581 0000007C         
  583 0000007C         ADC1SS2_Handler
                               PROC
  584 0000007C                 EXPORT           ADC1SS2_Handler [WEAK]
  585 0000007C E7FE            B                .
  586 0000007E                 ENDP
  587 0000007E         
  589 0000007E         ADC1SS3_Handler
                               PROC
  590 0000007E                 EXPORT           ADC1SS3_Handler [WEAK]
  591 0000007E E7FE            B                .
  592 00000080                 ENDP
  593 00000080         
  595 00000080         GPIOJ_Handler
                               PROC
  596 00000080                 EXPORT           GPIOJ_Handler [WEAK]
  597 00000080 E7FE            B                .
  598 00000082                 ENDP
  599 00000082         
  601 00000082         GPIOK_Handler
                               PROC
  602 00000082                 EXPORT           GPIOK_Handler [WEAK]
  603 00000082 E7FE            B                .



ARM Macro Assembler    Page 14 


  604 00000084                 ENDP
  605 00000084         
  607 00000084         GPIOL_Handler
                               PROC
  608 00000084                 EXPORT           GPIOL_Handler [WEAK]
  609 00000084 E7FE            B                .
  610 00000086                 ENDP
  611 00000086         
  613 00000086         SSI2_Handler
                               PROC
  614 00000086                 EXPORT           SSI2_Handler [WEAK]
  615 00000086 E7FE            B                .
  616 00000088                 ENDP
  617 00000088         
  619 00000088         SSI3_Handler
                               PROC
  620 00000088                 EXPORT           SSI3_Handler [WEAK]
  621 00000088 E7FE            B                .
  622 0000008A                 ENDP
  623 0000008A         
  625 0000008A         UART3_Handler
                               PROC
  626 0000008A                 EXPORT           UART3_Handler [WEAK]
  627 0000008A E7FE            B                .
  628 0000008C                 ENDP
  629 0000008C         
  631 0000008C         UART4_Handler
                               PROC
  632 0000008C                 EXPORT           UART4_Handler [WEAK]
  633 0000008C E7FE            B                .
  634 0000008E                 ENDP
  635 0000008E         
  637 0000008E         UART5_Handler
                               PROC
  638 0000008E                 EXPORT           UART5_Handler [WEAK]
  639 0000008E E7FE            B                .
  640 00000090                 ENDP
  641 00000090         
  643 00000090         UART6_Handler
                               PROC
  644 00000090                 EXPORT           UART6_Handler [WEAK]
  645 00000090 E7FE            B                .
  646 00000092                 ENDP
  647 00000092         
  649 00000092         UART7_Handler
                               PROC
  650 00000092                 EXPORT           UART7_Handler [WEAK]
  651 00000092 E7FE            B                .
  652 00000094                 ENDP
  653 00000094         
  655 00000094         I2C2_Handler
                               PROC
  656 00000094                 EXPORT           I2C2_Handler [WEAK]
  657 00000094 E7FE            B                .
  658 00000096                 ENDP
  659 00000096         
  661 00000096         I2C3_Handler
                               PROC
  662 00000096                 EXPORT           I2C3_Handler [WEAK]



ARM Macro Assembler    Page 15 


  663 00000096 E7FE            B                .
  664 00000098                 ENDP
  665 00000098         
  667 00000098         TIMER4A_Handler
                               PROC
  668 00000098                 EXPORT           TIMER4A_Handler [WEAK]
  669 00000098 E7FE            B                .
  670 0000009A                 ENDP
  671 0000009A         
  673 0000009A         TIMER4B_Handler
                               PROC
  674 0000009A                 EXPORT           TIMER4B_Handler [WEAK]
  675 0000009A E7FE            B                .
  676 0000009C                 ENDP
  677 0000009C         
  679 0000009C         TIMER5A_Handler
                               PROC
  680 0000009C                 EXPORT           TIMER5A_Handler [WEAK]
  681 0000009C E7FE            B                .
  682 0000009E                 ENDP
  683 0000009E         
  685 0000009E         TIMER5B_Handler
                               PROC
  686 0000009E                 EXPORT           TIMER5B_Handler [WEAK]
  687 0000009E E7FE            B                .
  688 000000A0                 ENDP
  689 000000A0         
  691 000000A0         WTIMER0A_Handler
                               PROC
  692 000000A0                 EXPORT           WTIMER0A_Handler [WEAK]
  693 000000A0 E7FE            B                .
  694 000000A2                 ENDP
  695 000000A2         
  697 000000A2         WTIMER0B_Handler
                               PROC
  698 000000A2                 EXPORT           WTIMER0B_Handler [WEAK]
  699 000000A2 E7FE            B                .
  700 000000A4                 ENDP
  701 000000A4         
  703 000000A4         WTIMER1A_Handler
                               PROC
  704 000000A4                 EXPORT           WTIMER1A_Handler [WEAK]
  705 000000A4 E7FE            B                .
  706 000000A6                 ENDP
  707 000000A6         
  709 000000A6         WTIMER1B_Handler
                               PROC
  710 000000A6                 EXPORT           WTIMER1B_Handler [WEAK]
  711 000000A6 E7FE            B                .
  712 000000A8                 ENDP
  713 000000A8         
  715 000000A8         WTIMER2A_Handler
                               PROC
  716 000000A8                 EXPORT           WTIMER2A_Handler [WEAK]
  717 000000A8 E7FE            B                .
  718 000000AA                 ENDP
  719 000000AA         
  721 000000AA         WTIMER2B_Handler
                               PROC



ARM Macro Assembler    Page 16 


  722 000000AA                 EXPORT           WTIMER2B_Handler [WEAK]
  723 000000AA E7FE            B                .
  724 000000AC                 ENDP
  725 000000AC         
  727 000000AC         WTIMER3A_Handler
                               PROC
  728 000000AC                 EXPORT           WTIMER3A_Handler [WEAK]
  729 000000AC E7FE            B                .
  730 000000AE                 ENDP
  731 000000AE         
  733 000000AE         WTIMER3B_Handler
                               PROC
  734 000000AE                 EXPORT           WTIMER3B_Handler [WEAK]
  735 000000AE E7FE            B                .
  736 000000B0                 ENDP
  737 000000B0         
  739 000000B0         WTIMER4A_Handler
                               PROC
  740 000000B0                 EXPORT           WTIMER4A_Handler [WEAK]
  741 000000B0 E7FE            B                .
  742 000000B2                 ENDP
  743 000000B2         
  745 000000B2         WTIMER4B_Handler
                               PROC
  746 000000B2                 EXPORT           WTIMER4B_Handler [WEAK]
  747 000000B2 E7FE            B                .
  748 000000B4                 ENDP
  749 000000B4         
  751 000000B4         WTIMER5A_Handler
                               PROC
  752 000000B4                 EXPORT           WTIMER5A_Handler [WEAK]
  753 000000B4 E7FE            B                .
  754 000000B6                 ENDP
  755 000000B6         
  757 000000B6         WTIMER5B_Handler
                               PROC
  758 000000B6                 EXPORT           WTIMER5B_Handler [WEAK]
  759 000000B6 E7FE            B                .
  760 000000B8                 ENDP
  761 000000B8         
  763 000000B8         FPU_Handler
                               PROC
  764 000000B8                 EXPORT           FPU_Handler [WEAK]
  765 000000B8 E7FE            B                .
  766 000000BA                 ENDP
  767 000000BA         
  769 000000BA         I2C4_Handler
                               PROC
  770 000000BA                 EXPORT           I2C4_Handler [WEAK]
  771 000000BA E7FE            B                .
  772 000000BC                 ENDP
  773 000000BC         
  775 000000BC         I2C5_Handler
                               PROC
  776 000000BC                 EXPORT           I2C5_Handler [WEAK]
  777 000000BC E7FE            B                .
  778 000000BE                 ENDP
  779 000000BE         
  781 000000BE         GPIOM_Handler



ARM Macro Assembler    Page 17 


                               PROC
  782 000000BE                 EXPORT           GPIOM_Handler [WEAK]
  783 000000BE E7FE            B                .
  784 000000C0                 ENDP
  785 000000C0         
  787 000000C0         GPION_Handler
                               PROC
  788 000000C0                 EXPORT           GPION_Handler [WEAK]
  789 000000C0 E7FE            B                .
  790 000000C2                 ENDP
  791 000000C2         
  793 000000C2         QEI2_Handler
                               PROC
  794 000000C2                 EXPORT           QEI2_Handler [WEAK]
  795 000000C2 E7FE            B                .
  796 000000C4                 ENDP
  797 000000C4         
  799 000000C4         GPIOP0_Handler
                               PROC
  800 000000C4                 EXPORT           GPIOP0_Handler [WEAK]
  801 000000C4 E7FE            B                .
  802 000000C6                 ENDP
  803 000000C6         
  805 000000C6         GPIOP1_Handler
                               PROC
  806 000000C6                 EXPORT           GPIOP1_Handler [WEAK]
  807 000000C6 E7FE            B                .
  808 000000C8                 ENDP
  809 000000C8         
  811 000000C8         GPIOP2_Handler
                               PROC
  812 000000C8                 EXPORT           GPIOP2_Handler [WEAK]
  813 000000C8 E7FE            B                .
  814 000000CA                 ENDP
  815 000000CA         
  817 000000CA         GPIOP3_Handler
                               PROC
  818 000000CA                 EXPORT           GPIOP3_Handler [WEAK]
  819 000000CA E7FE            B                .
  820 000000CC                 ENDP
  821 000000CC         
  823 000000CC         GPIOP4_Handler
                               PROC
  824 000000CC                 EXPORT           GPIOP4_Handler [WEAK]
  825 000000CC E7FE            B                .
  826 000000CE                 ENDP
  827 000000CE         
  829 000000CE         GPIOP5_Handler
                               PROC
  830 000000CE                 EXPORT           GPIOP5_Handler [WEAK]
  831 000000CE E7FE            B                .
  832 000000D0                 ENDP
  833 000000D0         
  835 000000D0         GPIOP6_Handler
                               PROC
  836 000000D0                 EXPORT           GPIOP6_Handler [WEAK]
  837 000000D0 E7FE            B                .
  838 000000D2                 ENDP
  839 000000D2         



ARM Macro Assembler    Page 18 


  841 000000D2         GPIOP7_Handler
                               PROC
  842 000000D2                 EXPORT           GPIOP7_Handler [WEAK]
  843 000000D2 E7FE            B                .
  844 000000D4                 ENDP
  845 000000D4         
  847 000000D4         GPIOQ0_Handler
                               PROC
  848 000000D4                 EXPORT           GPIOQ0_Handler [WEAK]
  849 000000D4 E7FE            B                .
  850 000000D6                 ENDP
  851 000000D6         
  853 000000D6         GPIOQ1_Handler
                               PROC
  854 000000D6                 EXPORT           GPIOQ1_Handler [WEAK]
  855 000000D6 E7FE            B                .
  856 000000D8                 ENDP
  857 000000D8         
  859 000000D8         GPIOQ2_Handler
                               PROC
  860 000000D8                 EXPORT           GPIOQ2_Handler [WEAK]
  861 000000D8 E7FE            B                .
  862 000000DA                 ENDP
  863 000000DA         
  865 000000DA         GPIOQ3_Handler
                               PROC
  866 000000DA                 EXPORT           GPIOQ3_Handler [WEAK]
  867 000000DA E7FE            B                .
  868 000000DC                 ENDP
  869 000000DC         
  871 000000DC         GPIOQ4_Handler
                               PROC
  872 000000DC                 EXPORT           GPIOQ4_Handler [WEAK]
  873 000000DC E7FE            B                .
  874 000000DE                 ENDP
  875 000000DE         
  877 000000DE         GPIOQ5_Handler
                               PROC
  878 000000DE                 EXPORT           GPIOQ5_Handler [WEAK]
  879 000000DE E7FE            B                .
  880 000000E0                 ENDP
  881 000000E0         
  883 000000E0         GPIOQ6_Handler
                               PROC
  884 000000E0                 EXPORT           GPIOQ6_Handler [WEAK]
  885 000000E0 E7FE            B                .
  886 000000E2                 ENDP
  887 000000E2         
  889 000000E2         GPIOQ7_Handler
                               PROC
  890 000000E2                 EXPORT           GPIOQ7_Handler [WEAK]
  891 000000E2 E7FE            B                .
  892 000000E4                 ENDP
  893 000000E4         
  895 000000E4         GPIOR_Handler
                               PROC
  896 000000E4                 EXPORT           GPIOR_Handler [WEAK]
  897 000000E4 E7FE            B                .
  898 000000E6                 ENDP



ARM Macro Assembler    Page 19 


  899 000000E6         
  901 000000E6         GPIOS_Handler
                               PROC
  902 000000E6                 EXPORT           GPIOS_Handler [WEAK]
  903 000000E6 E7FE            B                .
  904 000000E8                 ENDP
  905 000000E8         
  907 000000E8         PMW1_0_Handler
                               PROC
  908 000000E8                 EXPORT           PMW1_0_Handler [WEAK]
  909 000000E8 E7FE            B                .
  910 000000EA                 ENDP
  911 000000EA         
  913 000000EA         PWM1_1_Handler
                               PROC
  914 000000EA                 EXPORT           PWM1_1_Handler [WEAK]
  915 000000EA E7FE            B                .
  916 000000EC                 ENDP
  917 000000EC         
  919 000000EC         PWM1_2_Handler
                               PROC
  920 000000EC                 EXPORT           PWM1_2_Handler [WEAK]
  921 000000EC E7FE            B                .
  922 000000EE                 ENDP
  923 000000EE         
  925 000000EE         PWM1_3_Handler
                               PROC
  926 000000EE                 EXPORT           PWM1_3_Handler [WEAK]
  927 000000EE E7FE            B                .
  928 000000F0                 ENDP
  929 000000F0         
  931 000000F0         PWM1_FAULT_Handler
                               PROC
  932 000000F0                 EXPORT           PWM1_FAULT_Handler [WEAK]
  933 000000F0 E7FE            B                .
  934 000000F2                 ENDP
  935 000000F2         
  936 000000F2 00 00           ALIGN
  937 000000F4         
  938 000000F4         
  939 000000F4         ; User Initial Stack & Heap
  940 000000F4         
  941 000000F4                 IF               :DEF:__MICROLIB
  942 000000F4         
  943 000000F4                 EXPORT           __initial_sp
  944 000000F4                 EXPORT           __heap_base
  945 000000F4                 EXPORT           __heap_limit
  946 000000F4         
  947 000000F4                 ELSE
  961                          ENDIF
  962 000000F4         
  963 000000F4         
  964 000000F4                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\build\startup_tm4c123.d -o.\build\startup_tm4c123.o -ID:\a
pp\ARM\PACK\Keil\TM4C_DFP\1.1.0\Device\Include\TM4C123 --predefine="__MICROLIB 
SETA 1" --predefine="__UVISION_VERSION SETA 536" --predefine="TM4C123GH6PM SETA



ARM Macro Assembler    Page 20 


 1" --list=.\build\startup_tm4c123.lst ..\DriversMcu\TM4C123\Libraries\src\star
tup_TM4C123.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 35 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 36 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000A00

Symbol: __initial_sp
   Definitions
      At line 37 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 63 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 943 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 46 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 48 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 47 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 944 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: __heap_base used once
__heap_limit 00000500

Symbol: __heap_limit
   Definitions
      At line 49 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 945 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 58 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 63 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 59 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 224 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

__Vectors_End 0000026C

Symbol: __Vectors_End
   Definitions
      At line 222 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 60 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 224 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 226 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      None
Comment: .text unused
ADC0SS0_Handler 00000036

Symbol: ADC0SS0_Handler
   Definitions
      At line 373 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 96 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 374 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC0SS1_Handler 00000038

Symbol: ADC0SS1_Handler
   Definitions
      At line 379 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 97 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 380 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC0SS2_Handler 0000003A

Symbol: ADC0SS2_Handler
   Definitions
      At line 385 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 98 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 386 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC0SS3_Handler 0000003C

Symbol: ADC0SS3_Handler
   Definitions
      At line 391 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 99 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 392 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC1SS0_Handler 00000078

Symbol: ADC1SS0_Handler
   Definitions
      At line 571 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 130 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 572 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC1SS1_Handler 0000007A

Symbol: ADC1SS1_Handler
   Definitions
      At line 577 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 131 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 578 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC1SS2_Handler 0000007C

Symbol: ADC1SS2_Handler
   Definitions
      At line 583 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 132 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 584 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

ADC1SS3_Handler 0000007E

Symbol: ADC1SS3_Handler
   Definitions
      At line 589 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 133 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 590 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 259 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 68 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 260 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

CAN0_Handler 00000068

Symbol: CAN0_Handler
   Definitions
      At line 523 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 121 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 524 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

CAN1_Handler 0000006A

Symbol: CAN1_Handler
   Definitions
      At line 529 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 122 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 530 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

CAN2_Handler 0000006C

Symbol: CAN2_Handler
   Definitions
      At line 535 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 123 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 536 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

COMP0_Handler 0000004C




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: COMP0_Handler
   Definitions
      At line 439 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 107 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 440 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

COMP1_Handler 0000004E

Symbol: COMP1_Handler
   Definitions
      At line 445 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 108 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 446 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

COMP2_Handler 00000050

Symbol: COMP2_Handler
   Definitions
      At line 451 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 109 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 452 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 273 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 75 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 274 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

FLASH_Handler 00000054

Symbol: FLASH_Handler
   Definitions
      At line 463 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 111 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 464 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

FPU_Handler 000000B8

Symbol: FPU_Handler
   Definitions
      At line 763 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 188 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 764 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOA_Handler 0000001A

Symbol: GPIOA_Handler
   Definitions
      At line 289 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 82 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 290 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOB_Handler 0000001C

Symbol: GPIOB_Handler
   Definitions
      At line 295 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 83 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 296 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOC_Handler 0000001E

Symbol: GPIOC_Handler
   Definitions
      At line 301 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 84 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 302 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOD_Handler 00000020

Symbol: GPIOD_Handler
   Definitions
      At line 307 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 85 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 308 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOE_Handler 00000022

Symbol: GPIOE_Handler
   Definitions
      At line 313 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 86 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 314 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOF_Handler 00000056

Symbol: GPIOF_Handler
   Definitions
      At line 469 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 112 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 470 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOG_Handler 00000058

Symbol: GPIOG_Handler
   Definitions
      At line 475 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 113 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 476 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOH_Handler 0000005A

Symbol: GPIOH_Handler



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 481 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 114 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 482 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOJ_Handler 00000080

Symbol: GPIOJ_Handler
   Definitions
      At line 595 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 136 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 596 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOK_Handler 00000082

Symbol: GPIOK_Handler
   Definitions
      At line 601 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 137 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 602 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOL_Handler 00000084

Symbol: GPIOL_Handler
   Definitions
      At line 607 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 138 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 608 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOM_Handler 000000BE

Symbol: GPIOM_Handler
   Definitions
      At line 781 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 193 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 782 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPION_Handler 000000C0

Symbol: GPION_Handler
   Definitions
      At line 787 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 194 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 788 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP0_Handler 000000C4

Symbol: GPIOP0_Handler
   Definitions
      At line 799 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 198 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 800 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


GPIOP1_Handler 000000C6

Symbol: GPIOP1_Handler
   Definitions
      At line 805 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 199 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 806 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP2_Handler 000000C8

Symbol: GPIOP2_Handler
   Definitions
      At line 811 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 200 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 812 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP3_Handler 000000CA

Symbol: GPIOP3_Handler
   Definitions
      At line 817 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 201 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 818 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP4_Handler 000000CC

Symbol: GPIOP4_Handler
   Definitions
      At line 823 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 202 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 824 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP5_Handler 000000CE

Symbol: GPIOP5_Handler
   Definitions
      At line 829 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 203 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 830 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP6_Handler 000000D0

Symbol: GPIOP6_Handler
   Definitions
      At line 835 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 204 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 836 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOP7_Handler 000000D2

Symbol: GPIOP7_Handler
   Definitions



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

      At line 841 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 205 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 842 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ0_Handler 000000D4

Symbol: GPIOQ0_Handler
   Definitions
      At line 847 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 206 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 848 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ1_Handler 000000D6

Symbol: GPIOQ1_Handler
   Definitions
      At line 853 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 207 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 854 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ2_Handler 000000D8

Symbol: GPIOQ2_Handler
   Definitions
      At line 859 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 208 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 860 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ3_Handler 000000DA

Symbol: GPIOQ3_Handler
   Definitions
      At line 865 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 209 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 866 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ4_Handler 000000DC

Symbol: GPIOQ4_Handler
   Definitions
      At line 871 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 210 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 872 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ5_Handler 000000DE

Symbol: GPIOQ5_Handler
   Definitions
      At line 877 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 211 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 878 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s




ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols

GPIOQ6_Handler 000000E0

Symbol: GPIOQ6_Handler
   Definitions
      At line 883 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 212 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 884 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOQ7_Handler 000000E2

Symbol: GPIOQ7_Handler
   Definitions
      At line 889 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 213 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 890 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOR_Handler 000000E4

Symbol: GPIOR_Handler
   Definitions
      At line 895 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 214 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 896 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

GPIOS_Handler 000000E6

Symbol: GPIOS_Handler
   Definitions
      At line 901 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 215 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 902 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

HIB_Handler 0000006E

Symbol: HIB_Handler
   Definitions
      At line 541 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 125 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 542 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 249 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 66 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 250 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

I2C0_Handler 0000002A

Symbol: I2C0_Handler
   Definitions
      At line 337 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 90 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 338 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

I2C1_Handler 00000064

Symbol: I2C1_Handler
   Definitions
      At line 511 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 119 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 512 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

I2C2_Handler 00000094

Symbol: I2C2_Handler
   Definitions
      At line 655 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 150 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 656 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

I2C3_Handler 00000096

Symbol: I2C3_Handler
   Definitions
      At line 661 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 151 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 662 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

I2C4_Handler 000000BA

Symbol: I2C4_Handler
   Definitions
      At line 769 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 191 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 770 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

I2C5_Handler 000000BC

Symbol: I2C5_Handler
   Definitions
      At line 775 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 192 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 776 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 254 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 67 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 255 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

NMI_Handler 00000008



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols


Symbol: NMI_Handler
   Definitions
      At line 244 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 65 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 245 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PMW0_FAULT_Handler 0000002C

Symbol: PMW0_FAULT_Handler
   Definitions
      At line 343 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 91 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 344 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PMW1_0_Handler 000000E8

Symbol: PMW1_0_Handler
   Definitions
      At line 907 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 216 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 908 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM0_0_Handler 0000002E

Symbol: PWM0_0_Handler
   Definitions
      At line 349 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 92 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 350 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM0_1_Handler 00000030

Symbol: PWM0_1_Handler
   Definitions
      At line 355 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 93 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 356 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM0_2_Handler 00000032

Symbol: PWM0_2_Handler
   Definitions
      At line 361 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 94 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 362 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM0_3_Handler 00000072

Symbol: PWM0_3_Handler
   Definitions
      At line 553 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 127 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 554 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM1_1_Handler 000000EA

Symbol: PWM1_1_Handler
   Definitions
      At line 913 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 217 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 914 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM1_2_Handler 000000EC

Symbol: PWM1_2_Handler
   Definitions
      At line 919 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 218 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 920 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM1_3_Handler 000000EE

Symbol: PWM1_3_Handler
   Definitions
      At line 925 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 219 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 926 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PWM1_FAULT_Handler 000000F0

Symbol: PWM1_FAULT_Handler
   Definitions
      At line 931 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 220 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 932 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 278 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 77 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 279 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

QEI0_Handler 00000034

Symbol: QEI0_Handler
   Definitions
      At line 367 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 95 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 368 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

QEI1_Handler 00000066




ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

Symbol: QEI1_Handler
   Definitions
      At line 517 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 120 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 518 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

QEI2_Handler 000000C2

Symbol: QEI2_Handler
   Definitions
      At line 793 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 195 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 794 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 231 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 64 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 232 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SSI0_Handler 00000028

Symbol: SSI0_Handler
   Definitions
      At line 331 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 89 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 332 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SSI1_Handler 0000005E

Symbol: SSI1_Handler
   Definitions
      At line 493 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 116 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 494 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SSI2_Handler 00000086

Symbol: SSI2_Handler
   Definitions
      At line 613 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 139 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 614 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SSI3_Handler 00000088

Symbol: SSI3_Handler
   Definitions
      At line 619 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 140 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols

      At line 620 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 268 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 74 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 269 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SYSCTL_Handler 00000052

Symbol: SYSCTL_Handler
   Definitions
      At line 457 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 110 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 458 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 283 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 78 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 284 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER0A_Handler 00000040

Symbol: TIMER0A_Handler
   Definitions
      At line 403 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 101 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 404 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER0B_Handler 00000042

Symbol: TIMER0B_Handler
   Definitions
      At line 409 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 102 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 410 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER1A_Handler 00000044

Symbol: TIMER1A_Handler
   Definitions
      At line 415 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 103 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 416 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER1B_Handler 00000046

Symbol: TIMER1B_Handler



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 421 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 104 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 422 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER2A_Handler 00000048

Symbol: TIMER2A_Handler
   Definitions
      At line 427 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 105 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 428 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER2B_Handler 0000004A

Symbol: TIMER2B_Handler
   Definitions
      At line 433 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 106 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 434 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER3A_Handler 00000060

Symbol: TIMER3A_Handler
   Definitions
      At line 499 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 117 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 500 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER3B_Handler 00000062

Symbol: TIMER3B_Handler
   Definitions
      At line 505 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 118 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 506 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER4A_Handler 00000098

Symbol: TIMER4A_Handler
   Definitions
      At line 667 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 152 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 668 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER4B_Handler 0000009A

Symbol: TIMER4B_Handler
   Definitions
      At line 673 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 153 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 674 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s



ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols


TIMER5A_Handler 0000009C

Symbol: TIMER5A_Handler
   Definitions
      At line 679 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 174 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 680 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

TIMER5B_Handler 0000009E

Symbol: TIMER5B_Handler
   Definitions
      At line 685 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 175 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 686 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART0_Handler 00000024

Symbol: UART0_Handler
   Definitions
      At line 319 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 87 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 320 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART1_Handler 00000026

Symbol: UART1_Handler
   Definitions
      At line 325 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 88 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 326 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART2_Handler 0000005C

Symbol: UART2_Handler
   Definitions
      At line 487 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 115 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 488 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART3_Handler 0000008A

Symbol: UART3_Handler
   Definitions
      At line 625 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 141 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 626 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART4_Handler 0000008C

Symbol: UART4_Handler
   Definitions



ARM Macro Assembler    Page 16 Alphabetic symbol ordering
Relocatable symbols

      At line 631 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 142 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 632 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART5_Handler 0000008E

Symbol: UART5_Handler
   Definitions
      At line 637 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 143 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 638 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART6_Handler 00000090

Symbol: UART6_Handler
   Definitions
      At line 643 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 144 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 644 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UART7_Handler 00000092

Symbol: UART7_Handler
   Definitions
      At line 649 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 145 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 650 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UDMAERR_Handler 00000076

Symbol: UDMAERR_Handler
   Definitions
      At line 565 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 129 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 566 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

UDMA_Handler 00000074

Symbol: UDMA_Handler
   Definitions
      At line 559 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 128 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 560 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

USB0_Handler 00000070

Symbol: USB0_Handler
   Definitions
      At line 547 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 126 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 548 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s




ARM Macro Assembler    Page 17 Alphabetic symbol ordering
Relocatable symbols

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 264 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 69 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 265 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WDT0_Handler 0000003E

Symbol: WDT0_Handler
   Definitions
      At line 397 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 100 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 398 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER0A_Handler 000000A0

Symbol: WTIMER0A_Handler
   Definitions
      At line 691 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 176 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 692 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER0B_Handler 000000A2

Symbol: WTIMER0B_Handler
   Definitions
      At line 697 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 177 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 698 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER1A_Handler 000000A4

Symbol: WTIMER1A_Handler
   Definitions
      At line 703 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 178 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 704 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER1B_Handler 000000A6

Symbol: WTIMER1B_Handler
   Definitions
      At line 709 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 179 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 710 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER2A_Handler 000000A8

Symbol: WTIMER2A_Handler
   Definitions
      At line 715 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s



ARM Macro Assembler    Page 18 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 180 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 716 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER2B_Handler 000000AA

Symbol: WTIMER2B_Handler
   Definitions
      At line 721 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 181 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 722 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER3A_Handler 000000AC

Symbol: WTIMER3A_Handler
   Definitions
      At line 727 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 182 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 728 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER3B_Handler 000000AE

Symbol: WTIMER3B_Handler
   Definitions
      At line 733 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 183 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 734 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER4A_Handler 000000B0

Symbol: WTIMER4A_Handler
   Definitions
      At line 739 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 184 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 740 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER4B_Handler 000000B2

Symbol: WTIMER4B_Handler
   Definitions
      At line 745 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 185 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 746 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER5A_Handler 000000B4

Symbol: WTIMER5A_Handler
   Definitions
      At line 751 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 186 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 752 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

WTIMER5B_Handler 000000B6



ARM Macro Assembler    Page 19 Alphabetic symbol ordering
Relocatable symbols


Symbol: WTIMER5B_Handler
   Definitions
      At line 757 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 187 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
      At line 758 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s

119 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000500

Symbol: Heap_Size
   Definitions
      At line 44 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 48 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: Heap_Size used once
Stack_Size 00000A00

Symbol: Stack_Size
   Definitions
      At line 33 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 36 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: Stack_Size used once
__Vectors_Size 0000026C

Symbol: __Vectors_Size
   Definitions
      At line 224 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 61 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 233 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 235 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 234 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
   Uses
      At line 237 in file ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s
Comment: __main used once
2 symbols
470 symbols in table
