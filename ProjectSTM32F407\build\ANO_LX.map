Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to drv_bsp.o(.text) for All_Init
    main.o(.text) refers to ano_scheduler.o(.text) for Scheduler_Setup
    ano_scheduler.o(.text) refers to user_task.o(.text) for user_task
    ano_scheduler.o(.text) refers to drv_sys.o(.text) for GetSysRunTimeMs
    ano_scheduler.o(.text) refers to ano_scheduler.o(.data) for sched_tasks
    ano_scheduler.o(.data) refers to ano_scheduler.o(.text) for Loop_1000Hz
    user_task.o(.text) refers to protocol.o(.text) for usart_send01
    user_task.o(.text) refers to drv_uart.o(.text) for DrvUartDataCheck
    user_task.o(.text) refers to drv_led.o(.text) for led
    user_task.o(.text) refers to user_task.o(.data) for send_01_flag
    user_task.o(.text) refers to protocol.o(.data) for point
    user_task.o(.text) refers to protocol.o(.bss) for animal_total_count
    ano_math.o(.text) refers to cdcmple.o(.text) for __aeabi_cdcmple
    ano_math.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    ano_math.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    ano_math.o(.text) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ano_math.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    ano_math.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    ano_math.o(.text) refers to ano_math.o(.constdata) for fast_atan_table
    drv_bsp.o(.text) refers to drv_sys.o(.text) for DrvSysInit
    drv_bsp.o(.text) refers to drv_led.o(.text) for DvrLedInit
    drv_bsp.o(.text) refers to drv_pwmout.o(.text) for DrvPwmOutInit
    drv_bsp.o(.text) refers to drv_adc.o(.text) for ADC_Init_Single_Channel
    drv_bsp.o(.text) refers to drv_uart.o(.text) for DrvUart1Init
    drv_bsp.o(.text) refers to drv_timer.o(.text) for DrvTimerFcInit
    protocol.o(.text) refers to drv_uart.o(.text) for DrvUart1SendBuf
    protocol.o(.text) refers to printf3.o(i.__0sprintf$3) for __2sprintf
    protocol.o(.text) refers to strlen.o(.text) for strlen
    protocol.o(.text) refers to drv_sys.o(.text) for MyDelayMs
    protocol.o(.text) refers to protocol.o(.bss) for g_protocol
    protocol.o(.text) refers to protocol.o(.data) for point
    protocol.o(.text) refers to user_task.o(.data) for send_01_flag
    stm32f4xx_it.o(.text) refers to stm32f4xx_exti.o(.text) for EXTI_GetITStatus
    stm32f4xx_it.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetITStatus
    stm32f4xx_it.o(.text) refers to user_task.o(.text) for user_1ms
    stm32f4xx_it.o(.text) refers to drv_uart.o(.text) for Usart1_IRQ
    drv_sys.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_GetClocksFreq
    drv_sys.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    drv_sys.o(.text) refers to drv_sys.o(.data) for SysRunTimeMs
    drv_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    drv_adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    drv_adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_Init
    drv_adc.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    drv_adc.o(.text) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    drv_adc.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    drv_adc.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    drv_led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    drv_led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    drv_pwmout.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    drv_pwmout.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseStructInit
    drv_pwmout.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    drv_pwmout.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    drv_timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    drv_timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_DeInit
    drv_timer.o(.text) refers to misc.o(.text) for NVIC_Init
    drv_uart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_StructInit
    drv_uart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    drv_uart.o(.text) refers to misc.o(.text) for NVIC_Init
    drv_uart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    drv_uart.o(.text) refers to protocol.o(.text) for protocol_receive_byte
    drv_uart.o(.text) refers to drv_uart.o(.data) for count1
    drv_uart.o(.text) refers to drv_uart.o(.bss) for Tx1Buffer
    startup_stm32f4xx.o(RESET) refers to startup_stm32f4xx.o(STACK) for __initial_sp
    startup_stm32f4xx.o(RESET) refers to startup_stm32f4xx.o(.text) for Reset_Handler
    startup_stm32f4xx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f4xx.o(RESET) refers to drv_sys.o(.text) for SysTick_Handler
    startup_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f4xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    misc.o(.text) refers to main.o(.text) for assert_failed
    misc.o(.text) refers to misc.o(.conststring) for .conststring
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_adc.o(.text) refers to stm32f4xx_adc.o(.conststring) for .conststring
    stm32f4xx_dma.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_dma.o(.text) refers to stm32f4xx_dma.o(.conststring) for .conststring
    stm32f4xx_exti.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_exti.o(.text) refers to stm32f4xx_exti.o(.conststring) for .conststring
    stm32f4xx_flash.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.conststring) for .conststring
    stm32f4xx_gpio.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_gpio.o(.conststring) for .conststring
    stm32f4xx_rcc.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.conststring) for .conststring
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_spi.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_spi.o(.conststring) for .conststring
    stm32f4xx_tim.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.conststring) for .conststring
    stm32f4xx_usart.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_usart.o(.conststring) for .conststring
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f4xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f4xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.emb_text), (8 bytes).
    Removing ano_scheduler.o(.emb_text), (8 bytes).
    Removing user_task.o(.emb_text), (8 bytes).
    Removing ano_math.o(.emb_text), (8 bytes).
    Removing ano_math.o(.text), (2172 bytes).
    Removing ano_math.o(.constdata), (1028 bytes).
    Removing drv_bsp.o(.emb_text), (8 bytes).
    Removing protocol.o(.emb_text), (8 bytes).
    Removing stm32f4xx_it.o(.emb_text), (8 bytes).
    Removing drv_sys.o(.emb_text), (8 bytes).
    Removing drv_adc.o(.emb_text), (8 bytes).
    Removing drv_led.o(.emb_text), (8 bytes).
    Removing drv_pwmout.o(.emb_text), (8 bytes).
    Removing drv_timer.o(.emb_text), (8 bytes).
    Removing drv_uart.o(.emb_text), (8 bytes).
    Removing startup_stm32f4xx.o(HEAP), (1024 bytes).
    Removing system_stm32f4xx.o(.emb_text), (8 bytes).
    Removing misc.o(.emb_text), (8 bytes).
    Removing stm32f4xx_adc.o(.emb_text), (8 bytes).
    Removing stm32f4xx_dma.o(.emb_text), (8 bytes).
    Removing stm32f4xx_dma.o(.text), (4968 bytes).
    Removing stm32f4xx_dma.o(.conststring), (81 bytes).
    Removing stm32f4xx_exti.o(.emb_text), (8 bytes).
    Removing stm32f4xx_flash.o(.emb_text), (8 bytes).
    Removing stm32f4xx_flash.o(.text), (1864 bytes).
    Removing stm32f4xx_flash.o(.conststring), (83 bytes).
    Removing stm32f4xx_gpio.o(.emb_text), (8 bytes).
    Removing stm32f4xx_rcc.o(.emb_text), (8 bytes).
    Removing stm32f4xx_spi.o(.emb_text), (8 bytes).
    Removing stm32f4xx_spi.o(.text), (2888 bytes).
    Removing stm32f4xx_spi.o(.conststring), (81 bytes).
    Removing stm32f4xx_tim.o(.emb_text), (8 bytes).
    Removing stm32f4xx_usart.o(.emb_text), (8 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dfixul.o(.text), (48 bytes).

36 unused section(s) (total 14483 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\DriversBsp\Ano_Math.c                 0x00000000   Number         0  ano_math.o ABSOLUTE
    ..\DriversBsp\Drv_BSP.c                  0x00000000   Number         0  drv_bsp.o ABSOLUTE
    ..\DriversBsp\pid.c                      0x00000000   Number         0  pid.o ABSOLUTE
    ..\DriversBsp\protocol.c                 0x00000000   Number         0  protocol.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\Drv_PwmOut.c 0x00000000   Number         0  drv_pwmout.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\Drv_Sys.c 0x00000000   Number         0  drv_sys.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\Drv_Timer.c 0x00000000   Number         0  drv_timer.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\Drv_Uart.c 0x00000000   Number         0  drv_uart.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\Drv_adc.c 0x00000000   Number         0  drv_adc.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\Drv_led.c 0x00000000   Number         0  drv_led.o ABSOLUTE
    ..\DriversMcu\STM32F407\Drivers\stm32f4xx_it.c 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\CMSIS\ST\STM32F4xx\Source\Templates\arm\startup_stm32f4xx.s 0x00000000   Number         0  startup_stm32f4xx.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\CMSIS\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\DriversMcu\STM32F407\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FcSrc\Ano_Scheduler.c                 0x00000000   Number         0  ano_scheduler.o ABSOLUTE
    ..\FcSrc\User_Task.c                     0x00000000   Number         0  user_task.o ABSOLUTE
    ..\FcSrc\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\DriversBsp\\Ano_Math.c               0x00000000   Number         0  ano_math.o ABSOLUTE
    ..\\DriversBsp\\Drv_BSP.c                0x00000000   Number         0  drv_bsp.o ABSOLUTE
    ..\\DriversBsp\\protocol.c               0x00000000   Number         0  protocol.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\Drv_PwmOut.c 0x00000000   Number         0  drv_pwmout.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\Drv_Sys.c 0x00000000   Number         0  drv_sys.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\Drv_Timer.c 0x00000000   Number         0  drv_timer.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\Drv_Uart.c 0x00000000   Number         0  drv_uart.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\Drv_adc.c 0x00000000   Number         0  drv_adc.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\Drv_led.c 0x00000000   Number         0  drv_led.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Drivers\\stm32f4xx_it.c 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\CMSIS\\ST\\STM32F4xx\\Source\\Templates\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\DriversMcu\\STM32F407\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FcSrc\\Ano_Scheduler.c               0x00000000   Number         0  ano_scheduler.o ABSOLUTE
    ..\\FcSrc\\User_Task.c                   0x00000000   Number         0  user_task.o ABSOLUTE
    ..\\FcSrc\\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f4xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section        0  main.o(.text)
    .text                                    0x080001b4   Section        0  ano_scheduler.o(.text)
    Loop_1000Hz                              0x080001b5   Thumb Code     8  ano_scheduler.o(.text)
    Loop_500Hz                               0x080001bd   Thumb Code     2  ano_scheduler.o(.text)
    Loop_200Hz                               0x080001bf   Thumb Code     2  ano_scheduler.o(.text)
    Loop_100Hz                               0x080001c1   Thumb Code     2  ano_scheduler.o(.text)
    Loop_50Hz                                0x080001c3   Thumb Code     2  ano_scheduler.o(.text)
    Loop_20Hz                                0x080001c5   Thumb Code     2  ano_scheduler.o(.text)
    Loop_2Hz                                 0x080001c7   Thumb Code     2  ano_scheduler.o(.text)
    .text                                    0x08000264   Section        0  user_task.o(.text)
    .text                                    0x08000344   Section        0  drv_bsp.o(.text)
    .text                                    0x080003a4   Section        0  protocol.o(.text)
    .text                                    0x08000974   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x080009e4   Section        0  drv_sys.o(.text)
    .text                                    0x08000aec   Section        0  drv_adc.o(.text)
    .text                                    0x08000c6c   Section        0  drv_led.o(.text)
    .text                                    0x08000cd0   Section        0  drv_pwmout.o(.text)
    .text                                    0x08000e5c   Section        0  drv_timer.o(.text)
    .text                                    0x08000ee8   Section        0  drv_uart.o(.text)
    .text                                    0x0800190c   Section       36  startup_stm32f4xx.o(.text)
    $v0                                      0x0800190c   Number         0  startup_stm32f4xx.o(.text)
    .text                                    0x08001930   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08001931   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08001b40   Section        0  misc.o(.text)
    .text                                    0x08001d00   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x08002cdc   Section        0  stm32f4xx_exti.o(.text)
    .text                                    0x08002fb0   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08003878   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08004574   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x080061ed   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x080062d1   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x0800638f   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI1_Config                               0x08006465   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08007760   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08008584   Section        0  strlen.o(.text)
    .text                                    0x08008592   Section        0  dadd.o(.text)
    .text                                    0x080086e0   Section        0  dmul.o(.text)
    .text                                    0x080087c4   Section        0  dflti.o(.text)
    .text                                    0x080087e6   Section        0  f2d.o(.text)
    .text                                    0x0800880c   Section       48  cdrcmple.o(.text)
    .text                                    0x0800883c   Section        0  d2f.o(.text)
    .text                                    0x08008874   Section        0  uidiv.o(.text)
    .text                                    0x080088a0   Section        0  llshl.o(.text)
    .text                                    0x080088be   Section        0  llsshr.o(.text)
    .text                                    0x080088e2   Section        0  fepilogue.o(.text)
    .text                                    0x080088e2   Section        0  iusefp.o(.text)
    .text                                    0x08008950   Section        0  depilogue.o(.text)
    .text                                    0x08008a0a   Section        0  ddiv.o(.text)
    .text                                    0x08008ae8   Section        0  dscalb.o(.text)
    .text                                    0x08008b18   Section       36  init.o(.text)
    .text                                    0x08008b3c   Section        0  llushr.o(.text)
    .text                                    0x08008b5c   Section        0  dsqrt.o(.text)
    i.__0sprintf$3                           0x08008c00   Section        0  printf3.o(i.__0sprintf$3)
    i.__ARM_fpclassify                       0x08008c28   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_pow                           0x08008c58   Section        0  pow.o(i.__hardfp_pow)
    i.__kernel_poly                          0x080098a8   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x080099a0   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x080099d0   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080099e8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08009a08   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08009a28   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08009a48   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08009a56   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08009a58   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08009a68   Section        0  errno.o(i.__set_errno)
    i._printf_core                           0x08009a74   Section        0  printf3.o(i._printf_core)
    _printf_core                             0x08009a75   Thumb Code   436  printf3.o(i._printf_core)
    i._sputc                                 0x08009c2c   Section        0  printf3.o(i._sputc)
    _sputc                                   0x08009c2d   Thumb Code    10  printf3.o(i._sputc)
    i.fabs                                   0x08009c36   Section        0  fabs.o(i.fabs)
    i.sqrt                                   0x08009c4e   Section        0  sqrt.o(i.sqrt)
    .constdata                               0x08009cc0   Section      136  pow.o(.constdata)
    bp                                       0x08009cc0   Data          16  pow.o(.constdata)
    dp_h                                     0x08009cd0   Data          16  pow.o(.constdata)
    dp_l                                     0x08009ce0   Data          16  pow.o(.constdata)
    L                                        0x08009cf0   Data          48  pow.o(.constdata)
    P                                        0x08009d20   Data          40  pow.o(.constdata)
    .constdata                               0x08009d48   Section        8  qnan.o(.constdata)
    .conststring                             0x08009d50   Section       72  misc.o(.conststring)
    .conststring                             0x08009d98   Section       81  stm32f4xx_adc.o(.conststring)
    .conststring                             0x08009dec   Section       82  stm32f4xx_exti.o(.conststring)
    .conststring                             0x08009e40   Section       82  stm32f4xx_gpio.o(.conststring)
    .conststring                             0x08009e94   Section       81  stm32f4xx_rcc.o(.conststring)
    .conststring                             0x08009ee8   Section       81  stm32f4xx_tim.o(.conststring)
    .conststring                             0x08009f3c   Section       83  stm32f4xx_usart.o(.conststring)
    .data                                    0x20000000   Section       84  ano_scheduler.o(.data)
    sched_tasks                              0x20000000   Data          84  ano_scheduler.o(.data)
    .data                                    0x20000054   Section        9  user_task.o(.data)
    .data                                    0x2000005e   Section       13  protocol.o(.data)
    .data                                    0x20000070   Section        8  drv_sys.o(.data)
    SysRunTimeMs                             0x20000070   Data           8  drv_sys.o(.data)
    .data                                    0x20000078   Section       20  drv_uart.o(.data)
    .data                                    0x2000008c   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x200000a0   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x200000a0   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x200000b0   Section        4  errno.o(.data)
    _errno                                   0x200000b0   Data           4  errno.o(.data)
    .bss                                     0x200000b4   Section     5712  protocol.o(.bss)
    shuju                                    0x2000163c   Data         100  protocol.o(.bss)
    shuju                                    0x200016a0   Data         100  protocol.o(.bss)
    .bss                                     0x20001704   Section     1780  drv_uart.o(.bss)
    STACK                                    0x20001df8   Section     2048  startup_stm32f4xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f4xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f4xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f4xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    assert_failed                            0x080001a1   Thumb Code     4  main.o(.text)
    main                                     0x080001a5   Thumb Code    16  main.o(.text)
    Scheduler_Setup                          0x080001c9   Thumb Code    76  ano_scheduler.o(.text)
    Scheduler_Run                            0x08000215   Thumb Code    76  ano_scheduler.o(.text)
    user_task                                0x08000265   Thumb Code   124  user_task.o(.text)
    user_1ms                                 0x080002e1   Thumb Code    64  user_task.o(.text)
    All_Init                                 0x08000345   Thumb Code    94  drv_bsp.o(.text)
    get_data_length_by_cmd                   0x080003a5   Thumb Code    40  protocol.o(.text)
    protocol_reset                           0x080003cd   Thumb Code    14  protocol.o(.text)
    protocol_process_frame                   0x080003db   Thumb Code   456  protocol.o(.text)
    protocol_receive_byte                    0x080005a3   Thumb Code   164  protocol.o(.text)
    decimal_to_bcd                           0x08000647   Thumb Code    44  protocol.o(.text)
    usart_send01                             0x08000673   Thumb Code    44  protocol.o(.text)
    usart_send02                             0x0800069f   Thumb Code    34  protocol.o(.text)
    Send_pandian                             0x080006c1   Thumb Code   178  protocol.o(.text)
    Send_zong                                0x08000773   Thumb Code   420  protocol.o(.text)
    no_animal                                0x08000917   Thumb Code    46  protocol.o(.text)
    NMI_Handler                              0x08000975   Thumb Code     2  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000977   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x0800097b   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800097f   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x08000983   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000985   Thumb Code     2  stm32f4xx_it.o(.text)
    EXTI9_5_IRQHandler                       0x08000987   Thumb Code    18  stm32f4xx_it.o(.text)
    TIM3_IRQHandler                          0x08000999   Thumb Code     2  stm32f4xx_it.o(.text)
    TIM4_IRQHandler                          0x0800099b   Thumb Code     2  stm32f4xx_it.o(.text)
    TIM7_IRQHandler                          0x0800099d   Thumb Code    26  stm32f4xx_it.o(.text)
    USART1_IRQHandler                        0x080009b7   Thumb Code     8  stm32f4xx_it.o(.text)
    USART2_IRQHandler                        0x080009bf   Thumb Code     8  stm32f4xx_it.o(.text)
    USART3_IRQHandler                        0x080009c7   Thumb Code     8  stm32f4xx_it.o(.text)
    UART4_IRQHandler                         0x080009cf   Thumb Code     8  stm32f4xx_it.o(.text)
    UART5_IRQHandler                         0x080009d7   Thumb Code     8  stm32f4xx_it.o(.text)
    USART6_IRQHandler                        0x080009df   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Init                             0x080009e5   Thumb Code   100  drv_sys.o(.text)
    SysTick_Handler                          0x08000a49   Thumb Code    20  drv_sys.o(.text)
    GetSysRunTimeMs                          0x08000a5d   Thumb Code     6  drv_sys.o(.text)
    GetSysRunTimeUs                          0x08000a63   Thumb Code    62  drv_sys.o(.text)
    MyDelayUs                                0x08000aa1   Thumb Code    24  drv_sys.o(.text)
    MyDelayMs                                0x08000ab9   Thumb Code    24  drv_sys.o(.text)
    DrvSysInit                               0x08000ad1   Thumb Code    16  drv_sys.o(.text)
    ADC_Init_Single_Channel                  0x08000aed   Thumb Code   152  drv_adc.o(.text)
    Get_Adc_Value                            0x08000b85   Thumb Code    52  drv_adc.o(.text)
    Get_IRDistance                           0x08000bb9   Thumb Code   134  drv_adc.o(.text)
    DvrLedInit                               0x08000c6d   Thumb Code    68  drv_led.o(.text)
    led                                      0x08000cb1   Thumb Code    26  drv_led.o(.text)
    DrvPwmOutInit                            0x08000cd1   Thumb Code   340  drv_pwmout.o(.text)
    DrvMotorPWMSet                           0x08000e25   Thumb Code    28  drv_pwmout.o(.text)
    motor_set                                0x08000e41   Thumb Code     8  drv_pwmout.o(.text)
    TIM_CONF                                 0x08000e5d   Thumb Code    80  drv_timer.o(.text)
    TIM_NVIC                                 0x08000ead   Thumb Code    32  drv_timer.o(.text)
    DrvTimerFcInit                           0x08000ecd   Thumb Code    24  drv_timer.o(.text)
    NoUse                                    0x08000ee9   Thumb Code     2  drv_uart.o(.text)
    DrvUart1Init                             0x08000eeb   Thumb Code   244  drv_uart.o(.text)
    DrvUart1SendBuf                          0x08000fdf   Thumb Code    60  drv_uart.o(.text)
    drvU1GetByte                             0x0800101b   Thumb Code    32  drv_uart.o(.text)
    drvU1DataCheck                           0x0800103b   Thumb Code    52  drv_uart.o(.text)
    Usart1_IRQ                               0x0800106f   Thumb Code   110  drv_uart.o(.text)
    DrvUart2Init                             0x080010dd   Thumb Code   236  drv_uart.o(.text)
    DrvUart2SendBuf                          0x080011c9   Thumb Code    62  drv_uart.o(.text)
    drvU2GetByte                             0x08001207   Thumb Code    32  drv_uart.o(.text)
    drvU2DataCheck                           0x08001227   Thumb Code    38  drv_uart.o(.text)
    Usart2_IRQ                               0x0800124d   Thumb Code   120  drv_uart.o(.text)
    DrvUart3Init                             0x080012c5   Thumb Code   310  drv_uart.o(.text)
    DrvUart3SendBuf                          0x080013fb   Thumb Code    62  drv_uart.o(.text)
    drvU3GetByte                             0x08001439   Thumb Code    32  drv_uart.o(.text)
    drvU3DataCheck                           0x08001459   Thumb Code    38  drv_uart.o(.text)
    Usart3_IRQ                               0x0800147f   Thumb Code   120  drv_uart.o(.text)
    DrvUart4Init                             0x080014f7   Thumb Code   206  drv_uart.o(.text)
    DrvUart4SendBuf                          0x080015c5   Thumb Code    62  drv_uart.o(.text)
    drvU4GetByte                             0x08001603   Thumb Code    32  drv_uart.o(.text)
    drvU4DataCheck                           0x08001623   Thumb Code    38  drv_uart.o(.text)
    Uart4_IRQ                                0x08001649   Thumb Code   120  drv_uart.o(.text)
    DrvUart5Init                             0x080016c1   Thumb Code   280  drv_uart.o(.text)
    DrvUart5SendBuf                          0x080017d9   Thumb Code    60  drv_uart.o(.text)
    drvU5GetByte                             0x08001815   Thumb Code    32  drv_uart.o(.text)
    drvU5DataCheck                           0x08001835   Thumb Code    52  drv_uart.o(.text)
    Uart5_IRQ                                0x08001869   Thumb Code   110  drv_uart.o(.text)
    DrvUartDataCheck                         0x080018d7   Thumb Code    24  drv_uart.o(.text)
    Reset_Handler                            0x0800190d   Thumb Code     8  startup_stm32f4xx.o(.text)
    HardFault_Handler                        0x08001917   Thumb Code     2  startup_stm32f4xx.o(.text)
    PendSV_Handler                           0x08001923   Thumb Code     2  startup_stm32f4xx.o(.text)
    ADC_IRQHandler                           0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN1_TX_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CAN2_TX_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    CRYP_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DCMI_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    ETH_IRQHandler                           0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    EXTI0_IRQHandler                         0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    EXTI15_10_IRQHandler                     0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    EXTI1_IRQHandler                         0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    EXTI2_IRQHandler                         0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    EXTI3_IRQHandler                         0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    EXTI4_IRQHandler                         0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    FLASH_IRQHandler                         0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    FPU_IRQHandler                           0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    FSMC_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    HASH_RNG_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    I2C1_ER_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    I2C1_EV_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    I2C2_ER_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    I2C2_EV_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    I2C3_ER_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    I2C3_EV_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    OTG_FS_IRQHandler                        0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    OTG_HS_IRQHandler                        0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    PVD_IRQHandler                           0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    RCC_IRQHandler                           0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    SDIO_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    SPI1_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    SPI2_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    SPI3_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM1_CC_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM2_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM5_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM8_CC_IRQHandler                       0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    WWDG_IRQHandler                          0x08001927   Thumb Code     0  startup_stm32f4xx.o(.text)
    SystemInit                               0x08001a0d   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08001a65   Thumb Code   174  system_stm32f4xx.o(.text)
    NVIC_PriorityGroupConfig                 0x08001b41   Thumb Code    54  misc.o(.text)
    NVIC_Init                                0x08001b77   Thumb Code   164  misc.o(.text)
    NVIC_SetVectorTable                      0x08001c1b   Thumb Code    60  misc.o(.text)
    NVIC_SystemLPConfig                      0x08001c57   Thumb Code    78  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08001ca5   Thumb Code    62  misc.o(.text)
    ADC_DeInit                               0x08001d01   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x08001d17   Thumb Code   402  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x08001ea9   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x08001ebd   Thumb Code   342  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x08002013   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x0800201f   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x0800206b   Thumb Code   102  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x080020d1   Thumb Code   126  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x0800214f   Thumb Code   136  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x080021d7   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x0800220f   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x08002247   Thumb Code   408  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x080023df   Thumb Code    44  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x0800240b   Thumb Code    54  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x08002441   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x0800248d   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x080024d9   Thumb Code   100  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x0800253d   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x08002589   Thumb Code    40  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x080025b1   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x080025b7   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x08002603   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x0800264f   Thumb Code    60  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x0800268b   Thumb Code   332  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x080027d7   Thumb Code    80  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x08002827   Thumb Code   102  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x0800288d   Thumb Code   176  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x0800293d   Thumb Code    82  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x0800298f   Thumb Code    44  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x080029bb   Thumb Code    54  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x080029f1   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x08002a3d   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x08002a89   Thumb Code    88  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x08002ae1   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x08002b63   Thumb Code    90  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x08002bbd   Thumb Code    62  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x08002bfb   Thumb Code   122  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x08002c75   Thumb Code    88  stm32f4xx_adc.o(.text)
    EXTI_DeInit                              0x08002cdd   Thumb Code    28  stm32f4xx_exti.o(.text)
    EXTI_Init                                0x08002cf9   Thumb Code   236  stm32f4xx_exti.o(.text)
    EXTI_StructInit                          0x08002de5   Thumb Code    16  stm32f4xx_exti.o(.text)
    EXTI_GenerateSWInterrupt                 0x08002df5   Thumb Code    38  stm32f4xx_exti.o(.text)
    EXTI_GetFlagStatus                       0x08002e1b   Thumb Code   158  stm32f4xx_exti.o(.text)
    EXTI_ClearFlag                           0x08002eb9   Thumb Code    30  stm32f4xx_exti.o(.text)
    EXTI_GetITStatus                         0x08002ed7   Thumb Code   172  stm32f4xx_exti.o(.text)
    EXTI_ClearITPendingBit                   0x08002f83   Thumb Code    32  stm32f4xx_exti.o(.text)
    GPIO_DeInit                              0x08002fb1   Thumb Code   284  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x080030cd   Thumb Code   342  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x08003223   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x08003235   Thumb Code   122  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080032af   Thumb Code   182  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08003365   Thumb Code   116  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080033d9   Thumb Code   182  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800348f   Thumb Code    76  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x080034db   Thumb Code    94  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08003539   Thumb Code    94  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x08003597   Thumb Code   194  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08003659   Thumb Code    76  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x080036a5   Thumb Code    80  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x080036f5   Thumb Code   382  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08003879   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x080038b5   Thumb Code    38  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x080038db   Thumb Code   130  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800395d   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08003995   Thumb Code    38  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080039bb   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080039d7   Thumb Code    72  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08003a1f   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08003a3b   Thumb Code   156  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08003ad7   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08003af3   Thumb Code    62  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x08003b31   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08003b4d   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08003b69   Thumb Code   102  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x08003bcf   Thumb Code   102  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08003c35   Thumb Code    68  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08003c79   Thumb Code    10  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08003c83   Thumb Code    66  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x08003cc5   Thumb Code    58  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08003cff   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08003d3b   Thumb Code   214  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08003e11   Thumb Code   260  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08003f15   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08003f31   Thumb Code    30  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x08003f4f   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08003f6b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x08003fb9   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08004007   Thumb Code   234  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x080040f1   Thumb Code    70  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08004137   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08004185   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x080041d3   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08004221   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x0800426f   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x080042bd   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x0800430b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08004359   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x080043a7   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x080043f5   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08004443   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08004491   Thumb Code   108  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x080044fd   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x0800450b   Thumb Code    66  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x0800454d   Thumb Code    32  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08004575   Thumb Code   440  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800472d   Thumb Code   278  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08004843   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x08004855   Thumb Code   128  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x080048d5   Thumb Code   100  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08004939   Thumb Code   164  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x080049dd   Thumb Code   106  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08004a47   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x08004aaf   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08004b17   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08004ba7   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08004c37   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08004cc7   Thumb Code   138  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x08004d51   Thumb Code   190  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08004e0f   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08004e9f   Thumb Code   400  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x0800502f   Thumb Code   474  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08005209   Thumb Code   400  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08005399   Thumb Code   290  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x080054bb   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x080054cf   Thumb Code   250  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x080055c9   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x08005659   Thumb Code    70  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x0800569f   Thumb Code    58  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x080056d9   Thumb Code    58  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08005713   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08005791   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x080057ff   Thumb Code    90  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08005859   Thumb Code    98  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080058bb   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08005937   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080059a3   Thumb Code    88  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080059fb   Thumb Code   146  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08005a8d   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08005b09   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08005b75   Thumb Code    88  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08005bcd   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x08005c2d   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08005ca9   Thumb Code   106  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08005d13   Thumb Code    88  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x08005d6b   Thumb Code    94  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08005dc9   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08005e45   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08005eb7   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08005f23   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08005f6b   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08005fcb   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08006013   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08006073   Thumb Code   168  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x0800611b   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08006183   Thumb Code   106  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x0800623d   Thumb Code   148  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08006319   Thumb Code   118  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080063df   Thumb Code   134  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x0800649f   Thumb Code   460  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x0800666b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x0800667d   Thumb Code   234  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08006767   Thumb Code    92  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x080067c3   Thumb Code    68  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08006807   Thumb Code    56  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x0800683f   Thumb Code    56  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08006877   Thumb Code   222  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08006955   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08006967   Thumb Code    78  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x080069b5   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x080069fd   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08006a45   Thumb Code   216  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08006b1d   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08006b9b   Thumb Code   188  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08006c57   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x08006cc3   Thumb Code   186  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08006d7d   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08006de9   Thumb Code   328  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08006f31   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08006faf   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x0800700f   Thumb Code    76  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x0800705b   Thumb Code   148  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x080070ef   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x0800715d   Thumb Code   184  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08007215   Thumb Code   164  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x080072b9   Thumb Code   228  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800739d   Thumb Code   152  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08007435   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x080074b3   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08007523   Thumb Code   102  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08007589   Thumb Code   226  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x0800766b   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x080076d7   Thumb Code   138  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08007761   Thumb Code   202  stm32f4xx_usart.o(.text)
    USART_Init                               0x0800782b   Thumb Code   456  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x080079f3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08007a0b   Thumb Code   166  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08007ab1   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08007abd   Thumb Code    96  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08007b1d   Thumb Code   102  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08007b83   Thumb Code    94  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08007be1   Thumb Code    96  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08007c41   Thumb Code    80  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08007c91   Thumb Code    60  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08007ccd   Thumb Code    88  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08007d25   Thumb Code    96  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08007d85   Thumb Code    92  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08007de1   Thumb Code    90  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08007e3b   Thumb Code    96  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08007e9b   Thumb Code    62  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08007ed9   Thumb Code    96  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08007f39   Thumb Code    86  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08007f8f   Thumb Code    84  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08007fe3   Thumb Code    84  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08008037   Thumb Code    90  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08008091   Thumb Code    96  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x080080f1   Thumb Code   114  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08008163   Thumb Code   260  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08008267   Thumb Code   170  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08008311   Thumb Code   158  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x080083af   Thumb Code   278  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x080084c5   Thumb Code   164  stm32f4xx_usart.o(.text)
    strlen                                   0x08008585   Thumb Code    14  strlen.o(.text)
    __aeabi_dadd                             0x08008593   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080086d5   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080086db   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080086e1   Thumb Code   228  dmul.o(.text)
    __aeabi_i2d                              0x080087c5   Thumb Code    34  dflti.o(.text)
    __aeabi_f2d                              0x080087e7   Thumb Code    38  f2d.o(.text)
    __aeabi_cdrcmple                         0x0800880d   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x0800883d   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08008875   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08008875   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080088a1   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080088a1   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x080088bf   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080088bf   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x080088e3   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080088e3   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080088f5   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08008951   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800896f   Thumb Code   156  depilogue.o(.text)
    __aeabi_ddiv                             0x08008a0b   Thumb Code   222  ddiv.o(.text)
    __ARM_scalbn                             0x08008ae9   Thumb Code    46  dscalb.o(.text)
    scalbn                                   0x08008ae9   Thumb Code     0  dscalb.o(.text)
    __scatterload                            0x08008b19   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08008b19   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x08008b3d   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08008b3d   Thumb Code     0  llushr.o(.text)
    _dsqrt                                   0x08008b5d   Thumb Code   162  dsqrt.o(.text)
    __0sprintf$3                             0x08008c01   Thumb Code    34  printf3.o(i.__0sprintf$3)
    __1sprintf$3                             0x08008c01   Thumb Code     0  printf3.o(i.__0sprintf$3)
    __2sprintf                               0x08008c01   Thumb Code     0  printf3.o(i.__0sprintf$3)
    __ARM_fpclassify                         0x08008c29   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_pow                             0x08008c59   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __kernel_poly                            0x080098a9   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x080099a1   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x080099d1   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080099e9   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08009a09   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08009a29   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08009a49   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08009a57   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08009a59   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08009a69   Thumb Code     6  errno.o(i.__set_errno)
    fabs                                     0x08009c37   Thumb Code    24  fabs.o(i.fabs)
    sqrt                                     0x08009c4f   Thumb Code   110  sqrt.o(i.sqrt)
    __mathlib_zero                           0x08009d48   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08009f90   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009fb0   Number         0  anon$$obj.o(Region$$Table)
    time_1s                                  0x20000054   Data           1  user_task.o(.data)
    time                                     0x20000058   Data           4  user_task.o(.data)
    send_01_flag                             0x2000005c   Data           1  user_task.o(.data)
    point                                    0x2000005e   Data           3  protocol.o(.data)
    send                                     0x20000061   Data           4  protocol.o(.data)
    current_max_count                        0x20000066   Data           2  protocol.o(.data)
    fly                                      0x20000068   Data           1  protocol.o(.data)
    get_now                                  0x20000069   Data           1  protocol.o(.data)
    all_get                                  0x2000006a   Data           1  protocol.o(.data)
    Tx1Counter                               0x20000078   Data           1  drv_uart.o(.data)
    count1                                   0x20000079   Data           1  drv_uart.o(.data)
    U1RxInCnt                                0x2000007a   Data           1  drv_uart.o(.data)
    U1RxoutCnt                               0x2000007b   Data           1  drv_uart.o(.data)
    TxCounter                                0x2000007c   Data           1  drv_uart.o(.data)
    count                                    0x2000007d   Data           1  drv_uart.o(.data)
    U2RxInCnt                                0x2000007e   Data           1  drv_uart.o(.data)
    U2RxoutCnt                               0x2000007f   Data           1  drv_uart.o(.data)
    Tx3Counter                               0x20000080   Data           1  drv_uart.o(.data)
    count3                                   0x20000081   Data           1  drv_uart.o(.data)
    U3RxInCnt                                0x20000082   Data           1  drv_uart.o(.data)
    U3RxoutCnt                               0x20000083   Data           1  drv_uart.o(.data)
    Tx4Counter                               0x20000084   Data           1  drv_uart.o(.data)
    count4                                   0x20000085   Data           1  drv_uart.o(.data)
    U4RxInCnt                                0x20000086   Data           1  drv_uart.o(.data)
    U4RxoutCnt                               0x20000087   Data           1  drv_uart.o(.data)
    Tx5Counter                               0x20000088   Data           1  drv_uart.o(.data)
    count5                                   0x20000089   Data           1  drv_uart.o(.data)
    U5RxInCnt                                0x2000008a   Data           1  drv_uart.o(.data)
    U5RxoutCnt                               0x2000008b   Data           1  drv_uart.o(.data)
    SystemCoreClock                          0x2000008c   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000090   Data          16  system_stm32f4xx.o(.data)
    g_protocol                               0x200000b4   Data          24  protocol.o(.bss)
    static_point_array                       0x200000cc   Data        3000  protocol.o(.bss)
    position_animal_count                    0x20000c84   Data        2400  protocol.o(.bss)
    position_completed                       0x200015e4   Data          62  protocol.o(.bss)
    animal_total_count                       0x20001624   Data          24  protocol.o(.bss)
    Tx1Buffer                                0x20001704   Data         256  drv_uart.o(.bss)
    U1RxDataTmp                              0x20001804   Data         100  drv_uart.o(.bss)
    TxBuffer                                 0x20001868   Data         256  drv_uart.o(.bss)
    U2RxDataTmp                              0x20001968   Data         100  drv_uart.o(.bss)
    Tx3Buffer                                0x200019cc   Data         256  drv_uart.o(.bss)
    U3RxDataTmp                              0x20001acc   Data         100  drv_uart.o(.bss)
    Tx4Buffer                                0x20001b30   Data         256  drv_uart.o(.bss)
    U4RxDataTmp                              0x20001c30   Data         100  drv_uart.o(.bss)
    Tx5Buffer                                0x20001c94   Data         256  drv_uart.o(.bss)
    U5RxDataTmp                              0x20001d94   Data         100  drv_uart.o(.bss)
    __initial_sp                             0x200025f8   Data           0  startup_stm32f4xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a064, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009fb0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          464    RESET               startup_stm32f4xx.o
    0x08000188   0x08000188   0x00000000   Code   RO          677  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO          994    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO          997    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO          999    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1001    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1002    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1009    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1004    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1006    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO          995    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000014   Code   RO            2    .text               main.o
    0x080001b4   0x080001b4   0x000000b0   Code   RO          142    .text               ano_scheduler.o
    0x08000264   0x08000264   0x000000e0   Code   RO          234    .text               user_task.o
    0x08000344   0x08000344   0x0000005e   Code   RO          281    .text               drv_bsp.o
    0x080003a2   0x080003a2   0x00000002   PAD
    0x080003a4   0x080003a4   0x000005d0   Code   RO          300    .text               protocol.o
    0x08000974   0x08000974   0x00000070   Code   RO          326    .text               stm32f4xx_it.o
    0x080009e4   0x080009e4   0x00000108   Code   RO          342    .text               drv_sys.o
    0x08000aec   0x08000aec   0x00000180   Code   RO          375    .text               drv_adc.o
    0x08000c6c   0x08000c6c   0x00000064   Code   RO          395    .text               drv_led.o
    0x08000cd0   0x08000cd0   0x0000018c   Code   RO          411    .text               drv_pwmout.o
    0x08000e5c   0x08000e5c   0x0000008c   Code   RO          427    .text               drv_timer.o
    0x08000ee8   0x08000ee8   0x00000a24   Code   RO          443    .text               drv_uart.o
    0x0800190c   0x0800190c   0x00000024   Code   RO          465    .text               startup_stm32f4xx.o
    0x08001930   0x08001930   0x00000210   Code   RO          470    .text               system_stm32f4xx.o
    0x08001b40   0x08001b40   0x000001c0   Code   RO          492    .text               misc.o
    0x08001d00   0x08001d00   0x00000fdc   Code   RO          509    .text               stm32f4xx_adc.o
    0x08002cdc   0x08002cdc   0x000002d4   Code   RO          543    .text               stm32f4xx_exti.o
    0x08002fb0   0x08002fb0   0x000008c8   Code   RO          577    .text               stm32f4xx_gpio.o
    0x08003878   0x08003878   0x00000cfc   Code   RO          594    .text               stm32f4xx_rcc.o
    0x08004574   0x08004574   0x000031ec   Code   RO          630    .text               stm32f4xx_tim.o
    0x08007760   0x08007760   0x00000e24   Code   RO          647    .text               stm32f4xx_usart.o
    0x08008584   0x08008584   0x0000000e   Code   RO          680    .text               mc_w.l(strlen.o)
    0x08008592   0x08008592   0x0000014e   Code   RO          943    .text               mf_w.l(dadd.o)
    0x080086e0   0x080086e0   0x000000e4   Code   RO          945    .text               mf_w.l(dmul.o)
    0x080087c4   0x080087c4   0x00000022   Code   RO          947    .text               mf_w.l(dflti.o)
    0x080087e6   0x080087e6   0x00000026   Code   RO          949    .text               mf_w.l(f2d.o)
    0x0800880c   0x0800880c   0x00000030   Code   RO          953    .text               mf_w.l(cdrcmple.o)
    0x0800883c   0x0800883c   0x00000038   Code   RO          955    .text               mf_w.l(d2f.o)
    0x08008874   0x08008874   0x0000002c   Code   RO         1013    .text               mc_w.l(uidiv.o)
    0x080088a0   0x080088a0   0x0000001e   Code   RO         1017    .text               mc_w.l(llshl.o)
    0x080088be   0x080088be   0x00000024   Code   RO         1019    .text               mc_w.l(llsshr.o)
    0x080088e2   0x080088e2   0x00000000   Code   RO         1028    .text               mc_w.l(iusefp.o)
    0x080088e2   0x080088e2   0x0000006e   Code   RO         1029    .text               mf_w.l(fepilogue.o)
    0x08008950   0x08008950   0x000000ba   Code   RO         1031    .text               mf_w.l(depilogue.o)
    0x08008a0a   0x08008a0a   0x000000de   Code   RO         1033    .text               mf_w.l(ddiv.o)
    0x08008ae8   0x08008ae8   0x0000002e   Code   RO         1037    .text               mf_w.l(dscalb.o)
    0x08008b16   0x08008b16   0x00000002   PAD
    0x08008b18   0x08008b18   0x00000024   Code   RO         1041    .text               mc_w.l(init.o)
    0x08008b3c   0x08008b3c   0x00000020   Code   RO         1044    .text               mc_w.l(llushr.o)
    0x08008b5c   0x08008b5c   0x000000a2   Code   RO         1047    .text               mf_w.l(dsqrt.o)
    0x08008bfe   0x08008bfe   0x00000002   PAD
    0x08008c00   0x08008c00   0x00000028   Code   RO          773    i.__0sprintf$3      mc_w.l(printf3.o)
    0x08008c28   0x08008c28   0x00000030   Code   RO          977    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08008c58   0x08008c58   0x00000c50   Code   RO          663    i.__hardfp_pow      m_wm.l(pow.o)
    0x080098a8   0x080098a8   0x000000f8   Code   RO          979    i.__kernel_poly     m_wm.l(poly.o)
    0x080099a0   0x080099a0   0x00000030   Code   RO          957    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x080099d0   0x080099d0   0x00000014   Code   RO          959    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x080099e4   0x080099e4   0x00000004   PAD
    0x080099e8   0x080099e8   0x00000020   Code   RO          960    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08009a08   0x08009a08   0x00000020   Code   RO          961    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08009a28   0x08009a28   0x00000020   Code   RO          963    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08009a48   0x08009a48   0x0000000e   Code   RO         1051    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08009a56   0x08009a56   0x00000002   Code   RO         1052    i.__scatterload_null  mc_w.l(handlers.o)
    0x08009a58   0x08009a58   0x0000000e   Code   RO         1053    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08009a66   0x08009a66   0x00000002   PAD
    0x08009a68   0x08009a68   0x0000000c   Code   RO         1023    i.__set_errno       mc_w.l(errno.o)
    0x08009a74   0x08009a74   0x000001b8   Code   RO          778    i._printf_core      mc_w.l(printf3.o)
    0x08009c2c   0x08009c2c   0x0000000a   Code   RO          780    i._sputc            mc_w.l(printf3.o)
    0x08009c36   0x08009c36   0x00000018   Code   RO          973    i.fabs              m_wm.l(fabs.o)
    0x08009c4e   0x08009c4e   0x0000006e   Code   RO          984    i.sqrt              m_wm.l(sqrt.o)
    0x08009cbc   0x08009cbc   0x00000004   PAD
    0x08009cc0   0x08009cc0   0x00000088   Data   RO          666    .constdata          m_wm.l(pow.o)
    0x08009d48   0x08009d48   0x00000008   Data   RO          981    .constdata          m_wm.l(qnan.o)
    0x08009d50   0x08009d50   0x00000048   Data   RO          493    .conststring        misc.o
    0x08009d98   0x08009d98   0x00000051   Data   RO          510    .conststring        stm32f4xx_adc.o
    0x08009de9   0x08009de9   0x00000003   PAD
    0x08009dec   0x08009dec   0x00000052   Data   RO          544    .conststring        stm32f4xx_exti.o
    0x08009e3e   0x08009e3e   0x00000002   PAD
    0x08009e40   0x08009e40   0x00000052   Data   RO          578    .conststring        stm32f4xx_gpio.o
    0x08009e92   0x08009e92   0x00000002   PAD
    0x08009e94   0x08009e94   0x00000051   Data   RO          595    .conststring        stm32f4xx_rcc.o
    0x08009ee5   0x08009ee5   0x00000003   PAD
    0x08009ee8   0x08009ee8   0x00000051   Data   RO          631    .conststring        stm32f4xx_tim.o
    0x08009f39   0x08009f39   0x00000003   PAD
    0x08009f3c   0x08009f3c   0x00000053   Data   RO          648    .conststring        stm32f4xx_usart.o
    0x08009f8f   0x08009f8f   0x00000001   PAD
    0x08009f90   0x08009f90   0x00000020   Data   RO         1049    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009fb0, Size: 0x000025f8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08009fb0   0x00000054   Data   RW          143    .data               ano_scheduler.o
    0x20000054   0x0800a004   0x00000009   Data   RW          235    .data               user_task.o
    0x2000005d   0x0800a00d   0x00000001   PAD
    0x2000005e   0x0800a00e   0x0000000d   Data   RW          302    .data               protocol.o
    0x2000006b   0x0800a01b   0x00000005   PAD
    0x20000070   0x0800a020   0x00000008   Data   RW          343    .data               drv_sys.o
    0x20000078   0x0800a028   0x00000014   Data   RW          445    .data               drv_uart.o
    0x2000008c   0x0800a03c   0x00000014   Data   RW          471    .data               system_stm32f4xx.o
    0x200000a0   0x0800a050   0x00000010   Data   RW          596    .data               stm32f4xx_rcc.o
    0x200000b0   0x0800a060   0x00000004   Data   RW         1024    .data               mc_w.l(errno.o)
    0x200000b4        -       0x00001650   Zero   RW          301    .bss                protocol.o
    0x20001704        -       0x000006f4   Zero   RW          444    .bss                drv_uart.o
    0x20001df8        -       0x00000800   Zero   RW          462    STACK               startup_stm32f4xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       176          4          0         84          0     281276   ano_scheduler.o
       384         46          0          0          0       1311   drv_adc.o
        94          0          0          0          0        571   drv_bsp.o
       100          6          0          0          0        765   drv_led.o
       396         20          0          0          0       1737   drv_pwmout.o
       264         12          0          8          0      20349   drv_sys.o
       140          4          0          0          0        931   drv_timer.o
      2596        168          0         20       1780       8947   drv_uart.o
        20          0          0          0          0       6009   main.o
       448         30         72          0          0       2301   misc.o
      1488        202          0         13       5712       5616   protocol.o
        36          8        392          0       2048        960   startup_stm32f4xx.o
      4060        104         81          0          0      13618   stm32f4xx_adc.o
       724         14         82          0          0       2979   stm32f4xx_exti.o
      2248         86         82          0          0       5681   stm32f4xx_gpio.o
       112          4          0          0          0       2278   stm32f4xx_it.o
      3324        218         81         16          0      13831   stm32f4xx_rcc.o
     12780        628         81          0          0      31472   stm32f4xx_tim.o
      3620        116         83          0          0      10352   stm32f4xx_usart.o
       528         46          0         20          0       2227   system_stm32f4xx.o
       224         36          0          9          0        985   user_task.o

    ----------------------------------------------------------------------
     33764       <USER>       <GROUP>        176       9540     414196   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0         14          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       164         44          0          0          0        620   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
       110          0          0          0          0        148   sqrt.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       490         10          0          0          0        252   printf3.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        46          0          0          0          0         80   dscalb.o
       162          0          0          0          0        100   dsqrt.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      5972        <USER>        <GROUP>          4          0       3428   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3746        340        144          0          0       1520   m_wm.l
       748         32          0          4          0        740   mc_w.l
      1464          0          0          0          0       1168   mf_w.l

    ----------------------------------------------------------------------
      5972        <USER>        <GROUP>          4          0       3428   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     39736       2124       1144        180       9540     412292   Grand Totals
     39736       2124       1144        180       9540     412292   ELF Image Totals
     39736       2124       1144        180          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                40880 (  39.92kB)
    Total RW  Size (RW Data + ZI Data)              9720 (   9.49kB)
    Total ROM Size (Code + RO Data + RW Data)      41060 (  40.10kB)

==============================================================================

