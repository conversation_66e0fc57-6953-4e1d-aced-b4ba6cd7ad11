<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>28</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>ExePath</name>
                    <state>$PROJ_DIR$\</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>$PROJ_DIR$\</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>$PROJ_DIR$\</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>To be used with the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>8.10.1.12859</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>8.10.1.12859</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\INC\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>24</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>24</version>
                    <state>39</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>4</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>24</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>34</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\..\..\..\..</state>
                    <state>$PROJ_DIR$\..\..\..\..\..\third_party\CMSIS\Include</state>
                    <state>$PROJ_DIR$\..\..\inc</state>
                    <state>$PROJ_DIR$\..</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>20</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>msp432_driverlib.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>lnk0t.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state></state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>$PROJ_DIR$\msp432p4xx_driverlib.a</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>28</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state></state>
                </option>
                <option>
                    <name>Output description</name>
                    <state></state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state></state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>8.10.1.12859</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state></state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>24</version>
                    <state>38</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>24</version>
                    <state>38</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>-</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>24</version>
                    <state>38</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>34</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>NDEBUG</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>11111110</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>20</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>lnk0t.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state></state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>msp432p401</name>
        <file>
            <name>$PROJ_DIR$\..\flash.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\sysctl.c</name>
        </file>
        <configuration>
            <name>Debug</name>
            <settings>
                <name>ICCARM</name>
                <data>
                    <version>34</version>
                    <wantNonLocal>0</wantNonLocal>
                    <debug>1</debug>
                    <option>
                        <name>CCDefines</name>
                        <state>__MSP432P401R__</state>
                    </option>
                    <option>
                        <name>CCPreprocFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPreprocComments</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPreprocLine</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCListCFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListCMnemonics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListCMessages</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListAssFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListAssSource</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEnableRemarks</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCDiagSuppress</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagRemark</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagWarning</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagError</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCObjPrefix</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCAllowList</name>
                        <version>1</version>
                        <state>00000000</state>
                    </option>
                    <option>
                        <name>CCDebugInfo</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IEndianMode</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IProcessor</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IExtraOptionsCheck</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IExtraOptions</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCLangConformance</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCSignedPlainChar</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCRequirePrototypes</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCDiagWarnAreErr</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCCompilerRuntimeInfo</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IFpuProcessor</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>OutputFile</name>
                        <state>$FILE_BNAME$.o</state>
                    </option>
                    <option>
                        <name>CCLibConfigHeader</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>PreInclude</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CompilerMisraOverride</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCIncludePath2</name>
                        <state>$PROJ_DIR$\..\..\..\..\..</state>
                        <state>$PROJ_DIR$\..\..\..\..\..\third_party\CMSIS\Include</state>
                        <state>$PROJ_DIR$\..\..\inc</state>
                        <state>$PROJ_DIR$\..</state>
                    </option>
                    <option>
                        <name>CCStdIncCheck</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCCodeSection</name>
                        <state>.text</state>
                    </option>
                    <option>
                        <name>IProcessorMode2</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCOptLevel</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCOptStrategy</name>
                        <version>0</version>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptLevelSlave</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CompilerMisraRules98</name>
                        <version>0</version>
                        <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                    </option>
                    <option>
                        <name>CompilerMisraRules04</name>
                        <version>0</version>
                        <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                    </option>
                    <option>
                        <name>CCPosIndRopi</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPosIndRwpi</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPosIndNoDynInit</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccLang</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccCDialect</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccAllowVLA</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccStaticDestr</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccCppInlineSemantics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccCmsis</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccFloatSemantics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptimizationNoSizeConstraints</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCNoLiteralPool</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptStrategySlave</name>
                        <version>0</version>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncSource</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncOutput</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncOutputBom</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCEncInput</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccExceptions2</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccRTTI2</name>
                        <state>0</state>
                    </option>
                </data>
            </settings>
        </configuration>
    </group>
    <group>
        <name>msp432p4111</name>
        <file>
            <name>$PROJ_DIR$\..\flash_a.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\lcd_f.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\sysctl_a.c</name>
        </file>
        <configuration>
            <name>Debug</name>
            <settings>
                <name>ICCARM</name>
                <data>
                    <version>34</version>
                    <wantNonLocal>0</wantNonLocal>
                    <debug>1</debug>
                    <option>
                        <name>CCDefines</name>
                        <state>__MSP432P4111__</state>
                    </option>
                    <option>
                        <name>CCPreprocFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPreprocComments</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPreprocLine</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCListCFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListCMnemonics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListCMessages</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListAssFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListAssSource</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEnableRemarks</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCDiagSuppress</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagRemark</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagWarning</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagError</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCObjPrefix</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCAllowList</name>
                        <version>1</version>
                        <state>00000000</state>
                    </option>
                    <option>
                        <name>CCDebugInfo</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IEndianMode</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IProcessor</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IExtraOptionsCheck</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IExtraOptions</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCLangConformance</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCSignedPlainChar</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCRequirePrototypes</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCDiagWarnAreErr</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCCompilerRuntimeInfo</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IFpuProcessor</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>OutputFile</name>
                        <state>$FILE_BNAME$.o</state>
                    </option>
                    <option>
                        <name>CCLibConfigHeader</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>PreInclude</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CompilerMisraOverride</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCIncludePath2</name>
                        <state>$PROJ_DIR$\..\..\..\..\..</state>
                        <state>$PROJ_DIR$\..\..\..\..\..\third_party\CMSIS\Include</state>
                        <state>$PROJ_DIR$\..\..\inc</state>
                        <state>$PROJ_DIR$\..</state>
                    </option>
                    <option>
                        <name>CCStdIncCheck</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCCodeSection</name>
                        <state>.text</state>
                    </option>
                    <option>
                        <name>IProcessorMode2</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCOptLevel</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCOptStrategy</name>
                        <version>0</version>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptLevelSlave</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CompilerMisraRules98</name>
                        <version>0</version>
                        <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                    </option>
                    <option>
                        <name>CompilerMisraRules04</name>
                        <version>0</version>
                        <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                    </option>
                    <option>
                        <name>CCPosIndRopi</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPosIndRwpi</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPosIndNoDynInit</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccLang</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccCDialect</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccAllowVLA</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccStaticDestr</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccCppInlineSemantics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccCmsis</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccFloatSemantics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptimizationNoSizeConstraints</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCNoLiteralPool</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptStrategySlave</name>
                        <version>0</version>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncSource</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncOutput</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncOutputBom</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCEncInput</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccExceptions2</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccRTTI2</name>
                        <state>0</state>
                    </option>
                </data>
            </settings>
        </configuration>
    </group>
    <group>
        <name>Shared Peripherals</name>
        <file>
            <name>$PROJ_DIR$\..\adc14.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\aes256.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\comp_e.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\cpu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\crc32.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\cs.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\dma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\fpu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\gpio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\i2c.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\interrupt.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\mpu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\pcm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\pmap.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\pss.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\ref_a.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\reset.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\rtc_c.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\spi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\systick.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\timer32.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\timer_a.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\uart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\wdt_a.c</name>
        </file>
        <configuration>
            <name>Debug</name>
            <settings>
                <name>ICCARM</name>
                <data>
                    <version>34</version>
                    <wantNonLocal>0</wantNonLocal>
                    <debug>1</debug>
                    <option>
                        <name>CCDefines</name>
                        <state>__MSP432P401R__</state>
                    </option>
                    <option>
                        <name>CCPreprocFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPreprocComments</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPreprocLine</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCListCFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListCMnemonics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListCMessages</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListAssFile</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCListAssSource</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEnableRemarks</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCDiagSuppress</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagRemark</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagWarning</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCDiagError</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCObjPrefix</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCAllowList</name>
                        <version>1</version>
                        <state>00000000</state>
                    </option>
                    <option>
                        <name>CCDebugInfo</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IEndianMode</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IProcessor</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IExtraOptionsCheck</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IExtraOptions</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CCLangConformance</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCSignedPlainChar</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCRequirePrototypes</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCDiagWarnAreErr</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCCompilerRuntimeInfo</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IFpuProcessor</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>OutputFile</name>
                        <state>$FILE_BNAME$.o</state>
                    </option>
                    <option>
                        <name>CCLibConfigHeader</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>PreInclude</name>
                        <state></state>
                    </option>
                    <option>
                        <name>CompilerMisraOverride</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCIncludePath2</name>
                        <state>$PROJ_DIR$\..\..\..\..\..</state>
                        <state>$PROJ_DIR$\..\..\..\..\..\third_party\CMSIS\Include</state>
                        <state>$PROJ_DIR$\..\..\inc</state>
                        <state>$PROJ_DIR$\..</state>
                    </option>
                    <option>
                        <name>CCStdIncCheck</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCCodeSection</name>
                        <state>.text</state>
                    </option>
                    <option>
                        <name>IProcessorMode2</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCOptLevel</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCOptStrategy</name>
                        <version>0</version>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptLevelSlave</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CompilerMisraRules98</name>
                        <version>0</version>
                        <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                    </option>
                    <option>
                        <name>CompilerMisraRules04</name>
                        <version>0</version>
                        <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                    </option>
                    <option>
                        <name>CCPosIndRopi</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPosIndRwpi</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCPosIndNoDynInit</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccLang</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccCDialect</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccAllowVLA</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccStaticDestr</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccCppInlineSemantics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccCmsis</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>IccFloatSemantics</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptimizationNoSizeConstraints</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCNoLiteralPool</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCOptStrategySlave</name>
                        <version>0</version>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncSource</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncOutput</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>CCEncOutputBom</name>
                        <state>1</state>
                    </option>
                    <option>
                        <name>CCEncInput</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccExceptions2</name>
                        <state>0</state>
                    </option>
                    <option>
                        <name>IccRTTI2</name>
                        <state>0</state>
                    </option>
                </data>
            </settings>
        </configuration>
    </group>
</project>
