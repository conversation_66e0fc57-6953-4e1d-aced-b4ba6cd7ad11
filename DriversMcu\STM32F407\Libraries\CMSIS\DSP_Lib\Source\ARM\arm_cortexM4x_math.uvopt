<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_opt.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>DSP_Lib CM4 LE</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\intermediateFiles\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>0</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <DllOpt>
        <SimDllName>SARMCM3.DLL</SimDllName>
        <SimDllArguments></SimDllArguments>
        <SimDlgDllName>DCM.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
        <TargetDllName>SARMCM3.DLL</TargetDllName>
        <TargetDllArguments></TargetDllArguments>
        <TargetDlgDllName>TCM.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>1</uSim>
        <uTrg>0</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>-1</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon></pMon>
      </DebugOpt>
      <DebugFlag>
        <trace>0</trace>
        <periodic>0</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>0</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Target>
    <TargetName>DSP_Lib CM4 LE FPU</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\intermediateFiles\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>0</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <DllOpt>
        <SimDllName>SARMCM3.DLL</SimDllName>
        <SimDllArguments></SimDllArguments>
        <SimDlgDllName>DCM.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
        <TargetDllName>SARMCM3.DLL</TargetDllName>
        <TargetDllArguments></TargetDllArguments>
        <TargetDlgDllName>TCM.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>1</uSim>
        <uTrg>0</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>-1</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon></pMon>
      </DebugOpt>
      <DebugFlag>
        <trace>0</trace>
        <periodic>0</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>0</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Target>
    <TargetName>DSP_Lib CM4 BE</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\intermediateFiles\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>0</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <DllOpt>
        <SimDllName>SARMCM3.DLL</SimDllName>
        <SimDllArguments></SimDllArguments>
        <SimDlgDllName>DCM.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
        <TargetDllName>SARMCM3.DLL</TargetDllName>
        <TargetDllArguments></TargetDllArguments>
        <TargetDlgDllName>TCM.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>1</uSim>
        <uTrg>0</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>-1</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon></pMon>
      </DebugOpt>
      <DebugFlag>
        <trace>0</trace>
        <periodic>0</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>0</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Target>
    <TargetName>DSP_Lib CM4 BE FPU</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\intermediateFiles\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <DllOpt>
        <SimDllName>SARMCM3.DLL</SimDllName>
        <SimDllArguments></SimDllArguments>
        <SimDlgDllName>DCM.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
        <TargetDllName>SARMCM3.DLL</TargetDllName>
        <TargetDllArguments></TargetDllArguments>
        <TargetDlgDllName>TCM.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>1</uSim>
        <uTrg>0</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>-1</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon></pMon>
      </DebugOpt>
      <DebugFlag>
        <trace>0</trace>
        <periodic>0</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>0</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>BasicMathFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>15</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_abs_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_abs_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_abs_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_abs_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>13</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_abs_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_abs_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_abs_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_abs_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_add_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_add_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_add_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_add_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_add_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_add_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_add_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_add_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_dot_prod_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_dot_prod_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_dot_prod_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_dot_prod_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_dot_prod_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_dot_prod_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_dot_prod_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_dot_prod_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_mult_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mult_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_mult_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_mult_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_mult_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mult_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_mult_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mult_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_negate_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_negate_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_negate_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_negate_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_negate_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_negate_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_negate_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_negate_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_offset_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_offset_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_offset_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_offset_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>23</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_offset_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_offset_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>24</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_offset_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_offset_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>25</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_scale_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_scale_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>26</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_scale_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_scale_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>27</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_scale_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_scale_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>28</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_scale_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_scale_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>29</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_shift_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_shift_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>30</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_shift_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_shift_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>31</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_shift_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_shift_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>32</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_sub_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_sub_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>33</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_sub_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_sub_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>34</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_sub_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_sub_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>35</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\BasicMathFunctions\arm_sub_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sub_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>FastMathFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>36</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_cos_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cos_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>37</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_cos_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cos_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>38</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_cos_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cos_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>39</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_sin_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>40</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_sin_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>41</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_sin_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>42</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_sqrt_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_sqrt_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>43</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FastMathFunctions\arm_sqrt_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sqrt_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>ComplexMathFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>44</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_conj_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_conj_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>45</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_conj_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_conj_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>46</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_conj_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_conj_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>47</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_dot_prod_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>48</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_dot_prod_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>49</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_dot_prod_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>50</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mag_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mag_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>51</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mag_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mag_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>52</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mag_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mag_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>53</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mag_squared_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>54</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mag_squared_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>55</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mag_squared_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>56</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mult_cmplx_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>57</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mult_cmplx_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>58</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mult_cmplx_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>59</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mult_real_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mult_real_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>60</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mult_real_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mult_real_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>61</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ComplexMathFunctions\arm_cmplx_mult_real_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cmplx_mult_real_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>FilteringFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>62</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_32x64_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>63</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_32x64_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>64</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>65</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>66</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>67</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>68</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>69</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>70</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>71</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df1_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df1_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>72</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df2T_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df2T_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>73</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_biquad_cascade_df2T_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>74</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>75</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>76</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>77</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_partial_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_partial_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>78</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_partial_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_partial_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>79</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_partial_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_partial_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>80</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_partial_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_partial_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>81</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_partial_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_partial_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>82</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_partial_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_partial_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>83</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>84</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>85</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_conv_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_conv_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>86</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_correlate_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_correlate_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>87</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_correlate_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_correlate_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>88</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_correlate_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_correlate_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>89</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_correlate_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_correlate_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>90</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_correlate_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_correlate_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>91</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_correlate_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_correlate_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>92</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>93</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>94</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>95</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>96</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>97</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>98</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>99</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_decimate_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_decimate_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>100</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>101</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>102</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>103</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>104</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_init_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_init_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>105</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>106</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>107</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_interpolate_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_interpolate_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>108</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_interpolate_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_interpolate_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>109</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_interpolate_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_interpolate_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>110</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_interpolate_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_interpolate_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>111</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_interpolate_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_interpolate_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>112</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_interpolate_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_interpolate_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>113</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_lattice_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_lattice_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>114</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_lattice_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_lattice_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>115</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_lattice_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_lattice_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>116</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_lattice_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_lattice_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>117</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_lattice_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_lattice_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>118</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_lattice_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_lattice_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>119</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>120</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>121</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>122</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>123</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>124</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_init_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_init_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>125</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>126</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>127</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>128</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>129</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_fir_sparse_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fir_sparse_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>130</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_iir_lattice_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_iir_lattice_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>131</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_iir_lattice_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_iir_lattice_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>132</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_iir_lattice_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_iir_lattice_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>133</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_iir_lattice_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_iir_lattice_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>134</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_iir_lattice_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_iir_lattice_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>135</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_iir_lattice_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_iir_lattice_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>136</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>137</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>138</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>139</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>140</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_norm_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_norm_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>141</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_norm_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_norm_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>142</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_norm_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_norm_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>143</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_norm_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_norm_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>144</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>31</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_norm_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_norm_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>145</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_norm_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_norm_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>146</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>147</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\FilteringFunctions\arm_lms_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_lms_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>MatrixFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>148</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_add_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_add_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>149</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_add_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_add_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>150</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_add_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_add_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>151</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>152</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>153</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>154</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_inverse_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_inverse_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>155</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_mult_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_mult_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>156</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_mult_fast_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_mult_fast_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>157</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_mult_fast_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_mult_fast_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>158</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_mult_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_mult_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>159</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_mult_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_mult_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>160</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_scale_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_scale_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>161</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_scale_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_scale_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>162</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_scale_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_scale_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>163</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_sub_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_sub_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>164</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_sub_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_sub_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>165</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_sub_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_sub_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>166</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_trans_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_trans_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>167</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_trans_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_trans_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>168</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\MatrixFunctions\arm_mat_trans_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mat_trans_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>TransformFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>169</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_cfft_radix4_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cfft_radix4_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>170</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_cfft_radix4_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cfft_radix4_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>171</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_cfft_radix4_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cfft_radix4_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>172</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_cfft_radix4_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cfft_radix4_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>173</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>26</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_cfft_radix4_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cfft_radix4_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>174</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_cfft_radix4_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cfft_radix4_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>175</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_dct4_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_dct4_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>176</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_dct4_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_dct4_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>177</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_dct4_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_dct4_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>178</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_dct4_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_dct4_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>179</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_dct4_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_dct4_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>180</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_dct4_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_dct4_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>181</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_rfft_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_rfft_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>182</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_rfft_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_rfft_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>183</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_rfft_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_rfft_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>184</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_rfft_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_rfft_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>185</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>36</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_rfft_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_rfft_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>186</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\TransformFunctions\arm_rfft_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_rfft_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>ControllerFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>187</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_pid_init_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_pid_init_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>188</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_pid_init_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_pid_init_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>189</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_pid_init_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_pid_init_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>190</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_pid_reset_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_pid_reset_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>191</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_pid_reset_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_pid_reset_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>192</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_pid_reset_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_pid_reset_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>193</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_sin_cos_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_cos_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>194</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\ControllerFunctions\arm_sin_cos_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_cos_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>StatisticsFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>195</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_max_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_max_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>196</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_max_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_max_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>197</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_max_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_max_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>198</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_max_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_max_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>199</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_mean_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_mean_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>200</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_mean_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_mean_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>201</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_mean_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_mean_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>202</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_mean_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_mean_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>203</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_min_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_min_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>204</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_min_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_min_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>205</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_min_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_min_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>206</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_min_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_min_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>207</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_power_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_power_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>208</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_power_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_power_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>209</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_power_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_power_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>210</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_power_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_power_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>211</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_rms_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_rms_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>212</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_rms_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_rms_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>213</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_rms_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_rms_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>214</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_std_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_std_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>215</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_std_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_std_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>216</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_std_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_std_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>217</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_var_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_var_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>218</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_var_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_var_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>219</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\StatisticsFunctions\arm_var_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_var_q31.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>SupportFunctions</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>220</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_copy_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_copy_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>221</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_copy_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_copy_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>222</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_copy_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_copy_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>223</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_copy_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_copy_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>224</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_fill_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_fill_f32.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>225</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_fill_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_fill_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>226</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_fill_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_fill_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>227</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_fill_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_fill_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>228</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_float_to_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_float_to_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>229</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_float_to_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_float_to_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>230</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_float_to_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_float_to_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>231</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q7_to_float.c</PathWithFileName>
      <FilenameWithoutPath>arm_q7_to_float.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>232</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q7_to_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_q7_to_q15.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>233</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q7_to_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_q7_to_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>234</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q15_to_float.c</PathWithFileName>
      <FilenameWithoutPath>arm_q15_to_float.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>235</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q15_to_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_q15_to_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>236</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q15_to_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_q15_to_q31.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>237</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q31_to_float.c</PathWithFileName>
      <FilenameWithoutPath>arm_q31_to_float.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>238</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q31_to_q7.c</PathWithFileName>
      <FilenameWithoutPath>arm_q31_to_q7.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>239</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SupportFunctions\arm_q31_to_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_q31_to_q15.c</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>CommonTables</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>240</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CommonTables\arm_common_tables.c</PathWithFileName>
      <FilenameWithoutPath>arm_common_tables.c</FilenameWithoutPath>
    </File>
  </Group>

</ProjectOpt>
