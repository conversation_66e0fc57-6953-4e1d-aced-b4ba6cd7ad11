/* ----------------------------------------------------------------------   
* Copyright (C) 2010 ARM Limited. All rights reserved.   
*   
* $Date:        15. July 2011  
* $Revision: 	V1.0.10  
*   
* Project: 	    CMSIS DSP Library   
* Title:	    arm_dct4_init_q31.c   
*   
* Description:	Initialization function of DCT-4 & IDCT4 Q31   
*   
* Target Processor: Cortex-M4/Cortex-M3/Cortex-M0
*  
* Version 1.0.10 2011/7/15 
*    Big Endian support added and Merged M0 and M3/M4 Source code.  
*   
* Version 1.0.3 2010/11/29  
*    Re-organized the CMSIS folders and updated documentation.   
*    
* Version 1.0.2 2010/11/11   
*    Documentation updated.    
*   
* Version 1.0.1 2010/10/05    
*    Production release and review comments incorporated.   
*   
* Version 1.0.0 2010/09/20    
*    Production release and review comments incorporated.   
* -------------------------------------------------------------------- */


#include "arm_math.h"

/**   
 * @ingroup groupTransforms   
 */

/**   
 * @addtogroup DCT4_IDCT4   
 * @{   
 */

/*   
* @brief  Weights Table   
*/

/**   
* \par   
* Weights tables are generated using the formula : <pre>weights[n] = e^(-j*n*pi/(2*N))</pre>   
* \par   
* C command to generate the table   
* <pre>   
* for(i = 0; i< N; i++)   
* {   
*   weights[2*i]= cos(i*c);   
*   weights[(2*i)+1]= -sin(i * c);   
* } </pre>   
* \par   
* where <code>N</code> is the Number of weights to be calculated and <code>c</code> is <code>pi/(2*N)</code>   
* \par   
* Convert the output to q31 format by multiplying with 2^31 and saturated if required.   
* \par   
* In the tables below the real and imaginary values are placed alternatively, hence the   
* array length is <code>2*N</code>.   
*/

static const q31_t WeightsQ31_128[256] = {
  0x7fffffff, 0x0, 0x7ffd885a, 0xfe6de2e0, 0x7ff62182, 0xfcdbd541, 0x7fe9cbc0,
  0xfb49e6a3,
  0x7fd8878e, 0xf9b82684, 0x7fc25596, 0xf826a462, 0x7fa736b4, 0xf6956fb7,
  0x7f872bf3, 0xf50497fb,
  0x7f62368f, 0xf3742ca2, 0x7f3857f6, 0xf1e43d1c, 0x7f0991c4, 0xf054d8d5,
  0x7ed5e5c6, 0xeec60f31,
  0x7e9d55fc, 0xed37ef91, 0x7e5fe493, 0xebaa894f, 0x7e1d93ea, 0xea1debbb,
  0x7dd6668f, 0xe8922622,
  0x7d8a5f40, 0xe70747c4, 0x7d3980ec, 0xe57d5fda, 0x7ce3ceb2, 0xe3f47d96,
  0x7c894bde, 0xe26cb01b,
  0x7c29fbee, 0xe0e60685, 0x7bc5e290, 0xdf608fe4, 0x7b5d039e, 0xdddc5b3b,
  0x7aef6323, 0xdc597781,
  0x7a7d055b, 0xdad7f3a2, 0x7a05eead, 0xd957de7a, 0x798a23b1, 0xd7d946d8,
  0x7909a92d, 0xd65c3b7b,
  0x78848414, 0xd4e0cb15, 0x77fab989, 0xd3670446, 0x776c4edb, 0xd1eef59e,
  0x76d94989, 0xd078ad9e,
  0x7641af3d, 0xcf043ab3, 0x75a585cf, 0xcd91ab39, 0x7504d345, 0xcc210d79,
  0x745f9dd1, 0xcab26fa9,
  0x73b5ebd1, 0xc945dfec, 0x7307c3d0, 0xc7db6c50, 0x72552c85, 0xc67322ce,
  0x719e2cd2, 0xc50d1149,
  0x70e2cbc6, 0xc3a94590, 0x7023109a, 0xc247cd5a, 0x6f5f02b2, 0xc0e8b648,
  0x6e96a99d, 0xbf8c0de3,
  0x6dca0d14, 0xbe31e19b, 0x6cf934fc, 0xbcda3ecb, 0x6c242960, 0xbb8532b0,
  0x6b4af279, 0xba32ca71,
  0x6a6d98a4, 0xb8e31319, 0x698c246c, 0xb796199b, 0x68a69e81, 0xb64beacd,
  0x67bd0fbd, 0xb5049368,
  0x66cf8120, 0xb3c0200c, 0x65ddfbd3, 0xb27e9d3c, 0x64e88926, 0xb140175b,
  0x63ef3290, 0xb0049ab3,
  0x62f201ac, 0xaecc336c, 0x61f1003f, 0xad96ed92, 0x60ec3830, 0xac64d510,
  0x5fe3b38d, 0xab35f5b5,
  0x5ed77c8a, 0xaa0a5b2e, 0x5dc79d7c, 0xa8e21106, 0x5cb420e0, 0xa7bd22ac,
  0x5b9d1154, 0xa69b9b68,
  0x5a82799a, 0xa57d8666, 0x59646498, 0xa462eeac, 0x5842dd54, 0xa34bdf20,
  0x571deefa, 0xa2386284,
  0x55f5a4d2, 0xa1288376, 0x54ca0a4b, 0xa01c4c73, 0x539b2af0, 0x9f13c7d0,
  0x5269126e, 0x9e0effc1,
  0x5133cc94, 0x9d0dfe54, 0x4ffb654d, 0x9c10cd70, 0x4ebfe8a5, 0x9b1776da,
  0x4d8162c4, 0x9a22042d,
  0x4c3fdff4, 0x99307ee0, 0x4afb6c98, 0x9842f043, 0x49b41533, 0x9759617f,
  0x4869e665, 0x9673db94,
  0x471cece7, 0x9592675c, 0x45cd358f, 0x94b50d87, 0x447acd50, 0x93dbd6a0,
  0x4325c135, 0x9306cb04,
  0x41ce1e65, 0x9235f2ec, 0x4073f21d, 0x91695663, 0x3f1749b8, 0x90a0fd4e,
  0x3db832a6, 0x8fdcef66,
  0x3c56ba70, 0x8f1d343a, 0x3af2eeb7, 0x8e61d32e, 0x398cdd32, 0x8daad37b,
  0x382493b0, 0x8cf83c30,
  0x36ba2014, 0x8c4a142f, 0x354d9057, 0x8ba0622f, 0x33def287, 0x8afb2cbb,
  0x326e54c7, 0x8a5a7a31,
  0x30fbc54d, 0x89be50c3, 0x2f875262, 0x8926b677, 0x2e110a62, 0x8893b125,
  0x2c98fbba, 0x88054677,
  0x2b1f34eb, 0x877b7bec, 0x29a3c485, 0x86f656d3, 0x2826b928, 0x8675dc4f,
  0x26a82186, 0x85fa1153,
  0x25280c5e, 0x8582faa5, 0x23a6887f, 0x85109cdd, 0x2223a4c5, 0x84a2fc62,
  0x209f701c, 0x843a1d70,
  0x1f19f97b, 0x83d60412, 0x1d934fe5, 0x8376b422, 0x1c0b826a, 0x831c314e,
  0x1a82a026, 0x82c67f14,
  0x18f8b83c, 0x8275a0c0, 0x176dd9de, 0x82299971, 0x15e21445, 0x81e26c16,
  0x145576b1, 0x81a01b6d,
  0x12c8106f, 0x8162aa04, 0x1139f0cf, 0x812a1a3a, 0xfab272b, 0x80f66e3c,
  0xe1bc2e4, 0x80c7a80a,
  0xc8bd35e, 0x809dc971, 0xafb6805, 0x8078d40d, 0x96a9049, 0x8058c94c,
  0x7d95b9e, 0x803daa6a,
  0x647d97c, 0x80277872, 0x4b6195d, 0x80163440, 0x3242abf, 0x8009de7e,
  0x1921d20, 0x800277a6,
};

static const q31_t WeightsQ31_512[1024] = {
  0x7fffffff, 0x0, 0x7fffd886, 0xff9b781d, 0x7fff6216, 0xff36f078, 0x7ffe9cb2,
  0xfed2694f,
  0x7ffd885a, 0xfe6de2e0, 0x7ffc250f, 0xfe095d69, 0x7ffa72d1, 0xfda4d929,
  0x7ff871a2, 0xfd40565c,
  0x7ff62182, 0xfcdbd541, 0x7ff38274, 0xfc775616, 0x7ff09478, 0xfc12d91a,
  0x7fed5791, 0xfbae5e89,
  0x7fe9cbc0, 0xfb49e6a3, 0x7fe5f108, 0xfae571a4, 0x7fe1c76b, 0xfa80ffcb,
  0x7fdd4eec, 0xfa1c9157,
  0x7fd8878e, 0xf9b82684, 0x7fd37153, 0xf953bf91, 0x7fce0c3e, 0xf8ef5cbb,
  0x7fc85854, 0xf88afe42,
  0x7fc25596, 0xf826a462, 0x7fbc040a, 0xf7c24f59, 0x7fb563b3, 0xf75dff66,
  0x7fae7495, 0xf6f9b4c6,
  0x7fa736b4, 0xf6956fb7, 0x7f9faa15, 0xf6313077, 0x7f97cebd, 0xf5ccf743,
  0x7f8fa4b0, 0xf568c45b,
  0x7f872bf3, 0xf50497fb, 0x7f7e648c, 0xf4a07261, 0x7f754e80, 0xf43c53cb,
  0x7f6be9d4, 0xf3d83c77,
  0x7f62368f, 0xf3742ca2, 0x7f5834b7, 0xf310248a, 0x7f4de451, 0xf2ac246e,
  0x7f434563, 0xf2482c8a,
  0x7f3857f6, 0xf1e43d1c, 0x7f2d1c0e, 0xf1805662, 0x7f2191b4, 0xf11c789a,
  0x7f15b8ee, 0xf0b8a401,
  0x7f0991c4, 0xf054d8d5, 0x7efd1c3c, 0xeff11753, 0x7ef05860, 0xef8d5fb8,
  0x7ee34636, 0xef29b243,
  0x7ed5e5c6, 0xeec60f31, 0x7ec8371a, 0xee6276bf, 0x7eba3a39, 0xedfee92b,
  0x7eabef2c, 0xed9b66b2,
  0x7e9d55fc, 0xed37ef91, 0x7e8e6eb2, 0xecd48407, 0x7e7f3957, 0xec71244f,
  0x7e6fb5f4, 0xec0dd0a8,
  0x7e5fe493, 0xebaa894f, 0x7e4fc53e, 0xeb474e81, 0x7e3f57ff, 0xeae4207a,
  0x7e2e9cdf, 0xea80ff7a,
  0x7e1d93ea, 0xea1debbb, 0x7e0c3d29, 0xe9bae57d, 0x7dfa98a8, 0xe957ecfb,
  0x7de8a670, 0xe8f50273,
  0x7dd6668f, 0xe8922622, 0x7dc3d90d, 0xe82f5844, 0x7db0fdf8, 0xe7cc9917,
  0x7d9dd55a, 0xe769e8d8,
  0x7d8a5f40, 0xe70747c4, 0x7d769bb5, 0xe6a4b616, 0x7d628ac6, 0xe642340d,
  0x7d4e2c7f, 0xe5dfc1e5,
  0x7d3980ec, 0xe57d5fda, 0x7d24881b, 0xe51b0e2a, 0x7d0f4218, 0xe4b8cd11,
  0x7cf9aef0, 0xe4569ccb,
  0x7ce3ceb2, 0xe3f47d96, 0x7ccda169, 0xe3926fad, 0x7cb72724, 0xe330734d,
  0x7ca05ff1, 0xe2ce88b3,
  0x7c894bde, 0xe26cb01b, 0x7c71eaf9, 0xe20ae9c1, 0x7c5a3d50, 0xe1a935e2,
  0x7c4242f2, 0xe14794ba,
  0x7c29fbee, 0xe0e60685, 0x7c116853, 0xe0848b7f, 0x7bf88830, 0xe02323e5,
  0x7bdf5b94, 0xdfc1cff3,
  0x7bc5e290, 0xdf608fe4, 0x7bac1d31, 0xdeff63f4, 0x7b920b89, 0xde9e4c60,
  0x7b77ada8, 0xde3d4964,
  0x7b5d039e, 0xdddc5b3b, 0x7b420d7a, 0xdd7b8220, 0x7b26cb4f, 0xdd1abe51,
  0x7b0b3d2c, 0xdcba1008,
  0x7aef6323, 0xdc597781, 0x7ad33d45, 0xdbf8f4f8, 0x7ab6cba4, 0xdb9888a8,
  0x7a9a0e50, 0xdb3832cd,
  0x7a7d055b, 0xdad7f3a2, 0x7a5fb0d8, 0xda77cb63, 0x7a4210d8, 0xda17ba4a,
  0x7a24256f, 0xd9b7c094,
  0x7a05eead, 0xd957de7a, 0x79e76ca7, 0xd8f81439, 0x79c89f6e, 0xd898620c,
  0x79a98715, 0xd838c82d,
  0x798a23b1, 0xd7d946d8, 0x796a7554, 0xd779de47, 0x794a7c12, 0xd71a8eb5,
  0x792a37fe, 0xd6bb585e,
  0x7909a92d, 0xd65c3b7b, 0x78e8cfb2, 0xd5fd3848, 0x78c7aba2, 0xd59e4eff,
  0x78a63d11, 0xd53f7fda,
  0x78848414, 0xd4e0cb15, 0x786280bf, 0xd48230e9, 0x78403329, 0xd423b191,
  0x781d9b65, 0xd3c54d47,
  0x77fab989, 0xd3670446, 0x77d78daa, 0xd308d6c7, 0x77b417df, 0xd2aac504,
  0x7790583e, 0xd24ccf39,
  0x776c4edb, 0xd1eef59e, 0x7747fbce, 0xd191386e, 0x77235f2d, 0xd13397e2,
  0x76fe790e, 0xd0d61434,
  0x76d94989, 0xd078ad9e, 0x76b3d0b4, 0xd01b6459, 0x768e0ea6, 0xcfbe389f,
  0x76680376, 0xcf612aaa,
  0x7641af3d, 0xcf043ab3, 0x761b1211, 0xcea768f2, 0x75f42c0b, 0xce4ab5a2,
  0x75ccfd42, 0xcdee20fc,
  0x75a585cf, 0xcd91ab39, 0x757dc5ca, 0xcd355491, 0x7555bd4c, 0xccd91d3d,
  0x752d6c6c, 0xcc7d0578,
  0x7504d345, 0xcc210d79, 0x74dbf1ef, 0xcbc53579, 0x74b2c884, 0xcb697db0,
  0x7489571c, 0xcb0de658,
  0x745f9dd1, 0xcab26fa9, 0x74359cbd, 0xca5719db, 0x740b53fb, 0xc9fbe527,
  0x73e0c3a3, 0xc9a0d1c5,
  0x73b5ebd1, 0xc945dfec, 0x738acc9e, 0xc8eb0fd6, 0x735f6626, 0xc89061ba,
  0x7333b883, 0xc835d5d0,
  0x7307c3d0, 0xc7db6c50, 0x72db8828, 0xc7812572, 0x72af05a7, 0xc727016d,
  0x72823c67, 0xc6cd0079,
  0x72552c85, 0xc67322ce, 0x7227d61c, 0xc61968a2, 0x71fa3949, 0xc5bfd22e,
  0x71cc5626, 0xc5665fa9,
  0x719e2cd2, 0xc50d1149, 0x716fbd68, 0xc4b3e746, 0x71410805, 0xc45ae1d7,
  0x71120cc5, 0xc4020133,
  0x70e2cbc6, 0xc3a94590, 0x70b34525, 0xc350af26, 0x708378ff, 0xc2f83e2a,
  0x70536771, 0xc29ff2d4,
  0x7023109a, 0xc247cd5a, 0x6ff27497, 0xc1efcdf3, 0x6fc19385, 0xc197f4d4,
  0x6f906d84, 0xc1404233,
  0x6f5f02b2, 0xc0e8b648, 0x6f2d532c, 0xc0915148, 0x6efb5f12, 0xc03a1368,
  0x6ec92683, 0xbfe2fcdf,
  0x6e96a99d, 0xbf8c0de3, 0x6e63e87f, 0xbf3546a8, 0x6e30e34a, 0xbedea765,
  0x6dfd9a1c, 0xbe88304f,
  0x6dca0d14, 0xbe31e19b, 0x6d963c54, 0xbddbbb7f, 0x6d6227fa, 0xbd85be30,
  0x6d2dd027, 0xbd2fe9e2,
  0x6cf934fc, 0xbcda3ecb, 0x6cc45698, 0xbc84bd1f, 0x6c8f351c, 0xbc2f6513,
  0x6c59d0a9, 0xbbda36dd,
  0x6c242960, 0xbb8532b0, 0x6bee3f62, 0xbb3058c0, 0x6bb812d1, 0xbadba943,
  0x6b81a3cd, 0xba87246d,
  0x6b4af279, 0xba32ca71, 0x6b13fef5, 0xb9de9b83, 0x6adcc964, 0xb98a97d8,
  0x6aa551e9, 0xb936bfa4,
  0x6a6d98a4, 0xb8e31319, 0x6a359db9, 0xb88f926d, 0x69fd614a, 0xb83c3dd1,
  0x69c4e37a, 0xb7e9157a,
  0x698c246c, 0xb796199b, 0x69532442, 0xb7434a67, 0x6919e320, 0xb6f0a812,
  0x68e06129, 0xb69e32cd,
  0x68a69e81, 0xb64beacd, 0x686c9b4b, 0xb5f9d043, 0x683257ab, 0xb5a7e362,
  0x67f7d3c5, 0xb556245e,
  0x67bd0fbd, 0xb5049368, 0x67820bb7, 0xb4b330b3, 0x6746c7d8, 0xb461fc70,
  0x670b4444, 0xb410f6d3,
  0x66cf8120, 0xb3c0200c, 0x66937e91, 0xb36f784f, 0x66573cbb, 0xb31effcc,
  0x661abbc5, 0xb2ceb6b5,
  0x65ddfbd3, 0xb27e9d3c, 0x65a0fd0b, 0xb22eb392, 0x6563bf92, 0xb1def9e9,
  0x6526438f, 0xb18f7071,
  0x64e88926, 0xb140175b, 0x64aa907f, 0xb0f0eeda, 0x646c59bf, 0xb0a1f71d,
  0x642de50d, 0xb0533055,
  0x63ef3290, 0xb0049ab3, 0x63b0426d, 0xafb63667, 0x637114cc, 0xaf6803a2,
  0x6331a9d4, 0xaf1a0293,
  0x62f201ac, 0xaecc336c, 0x62b21c7b, 0xae7e965b, 0x6271fa69, 0xae312b92,
  0x62319b9d, 0xade3f33e,
  0x61f1003f, 0xad96ed92, 0x61b02876, 0xad4a1aba, 0x616f146c, 0xacfd7ae8,
  0x612dc447, 0xacb10e4b,
  0x60ec3830, 0xac64d510, 0x60aa7050, 0xac18cf69, 0x60686ccf, 0xabccfd83,
  0x60262dd6, 0xab815f8d,
  0x5fe3b38d, 0xab35f5b5, 0x5fa0fe1f, 0xaaeac02c, 0x5f5e0db3, 0xaa9fbf1e,
  0x5f1ae274, 0xaa54f2ba,
  0x5ed77c8a, 0xaa0a5b2e, 0x5e93dc1f, 0xa9bff8a8, 0x5e50015d, 0xa975cb57,
  0x5e0bec6e, 0xa92bd367,
  0x5dc79d7c, 0xa8e21106, 0x5d8314b1, 0xa8988463, 0x5d3e5237, 0xa84f2daa,
  0x5cf95638, 0xa8060d08,
  0x5cb420e0, 0xa7bd22ac, 0x5c6eb258, 0xa7746ec0, 0x5c290acc, 0xa72bf174,
  0x5be32a67, 0xa6e3aaf2,
  0x5b9d1154, 0xa69b9b68, 0x5b56bfbd, 0xa653c303, 0x5b1035cf, 0xa60c21ee,
  0x5ac973b5, 0xa5c4b855,
  0x5a82799a, 0xa57d8666, 0x5a3b47ab, 0xa5368c4b, 0x59f3de12, 0xa4efca31,
  0x59ac3cfd, 0xa4a94043,
  0x59646498, 0xa462eeac, 0x591c550e, 0xa41cd599, 0x58d40e8c, 0xa3d6f534,
  0x588b9140, 0xa3914da8,
  0x5842dd54, 0xa34bdf20, 0x57f9f2f8, 0xa306a9c8, 0x57b0d256, 0xa2c1adc9,
  0x57677b9d, 0xa27ceb4f,
  0x571deefa, 0xa2386284, 0x56d42c99, 0xa1f41392, 0x568a34a9, 0xa1affea3,
  0x56400758, 0xa16c23e1,
  0x55f5a4d2, 0xa1288376, 0x55ab0d46, 0xa0e51d8c, 0x556040e2, 0xa0a1f24d,
  0x55153fd4, 0xa05f01e1,
  0x54ca0a4b, 0xa01c4c73, 0x547ea073, 0x9fd9d22a, 0x5433027d, 0x9f979331,
  0x53e73097, 0x9f558fb0,
  0x539b2af0, 0x9f13c7d0, 0x534ef1b5, 0x9ed23bb9, 0x53028518, 0x9e90eb94,
  0x52b5e546, 0x9e4fd78a,
  0x5269126e, 0x9e0effc1, 0x521c0cc2, 0x9dce6463, 0x51ced46e, 0x9d8e0597,
  0x518169a5, 0x9d4de385,
  0x5133cc94, 0x9d0dfe54, 0x50e5fd6d, 0x9cce562c, 0x5097fc5e, 0x9c8eeb34,
  0x5049c999, 0x9c4fbd93,
  0x4ffb654d, 0x9c10cd70, 0x4faccfab, 0x9bd21af3, 0x4f5e08e3, 0x9b93a641,
  0x4f0f1126, 0x9b556f81,
  0x4ebfe8a5, 0x9b1776da, 0x4e708f8f, 0x9ad9bc71, 0x4e210617, 0x9a9c406e,
  0x4dd14c6e, 0x9a5f02f5,
  0x4d8162c4, 0x9a22042d, 0x4d31494b, 0x99e5443b, 0x4ce10034, 0x99a8c345,
  0x4c9087b1, 0x996c816f,
  0x4c3fdff4, 0x99307ee0, 0x4bef092d, 0x98f4bbbc, 0x4b9e0390, 0x98b93828,
  0x4b4ccf4d, 0x987df449,
  0x4afb6c98, 0x9842f043, 0x4aa9dba2, 0x98082c3b, 0x4a581c9e, 0x97cda855,
  0x4a062fbd, 0x979364b5,
  0x49b41533, 0x9759617f, 0x4961cd33, 0x971f9ed7, 0x490f57ee, 0x96e61ce0,
  0x48bcb599, 0x96acdbbe,
  0x4869e665, 0x9673db94, 0x4816ea86, 0x963b1c86, 0x47c3c22f, 0x96029eb6,
  0x47706d93, 0x95ca6247,
  0x471cece7, 0x9592675c, 0x46c9405c, 0x955aae17, 0x46756828, 0x9523369c,
  0x4621647d, 0x94ec010b,
  0x45cd358f, 0x94b50d87, 0x4578db93, 0x947e5c33, 0x452456bd, 0x9447ed2f,
  0x44cfa740, 0x9411c09e,
  0x447acd50, 0x93dbd6a0, 0x4425c923, 0x93a62f57, 0x43d09aed, 0x9370cae4,
  0x437b42e1, 0x933ba968,
  0x4325c135, 0x9306cb04, 0x42d0161e, 0x92d22fd9, 0x427a41d0, 0x929dd806,
  0x42244481, 0x9269c3ac,
  0x41ce1e65, 0x9235f2ec, 0x4177cfb1, 0x920265e4, 0x4121589b, 0x91cf1cb6,
  0x40cab958, 0x919c1781,
  0x4073f21d, 0x91695663, 0x401d0321, 0x9136d97d, 0x3fc5ec98, 0x9104a0ee,
  0x3f6eaeb8, 0x90d2acd4,
  0x3f1749b8, 0x90a0fd4e, 0x3ebfbdcd, 0x906f927c, 0x3e680b2c, 0x903e6c7b,
  0x3e10320d, 0x900d8b69,
  0x3db832a6, 0x8fdcef66, 0x3d600d2c, 0x8fac988f, 0x3d07c1d6, 0x8f7c8701,
  0x3caf50da, 0x8f4cbadb,
  0x3c56ba70, 0x8f1d343a, 0x3bfdfecd, 0x8eedf33b, 0x3ba51e29, 0x8ebef7fb,
  0x3b4c18ba, 0x8e904298,
  0x3af2eeb7, 0x8e61d32e, 0x3a99a057, 0x8e33a9da, 0x3a402dd2, 0x8e05c6b7,
  0x39e6975e, 0x8dd829e4,
  0x398cdd32, 0x8daad37b, 0x3932ff87, 0x8d7dc399, 0x38d8fe93, 0x8d50fa59,
  0x387eda8e, 0x8d2477d8,
  0x382493b0, 0x8cf83c30, 0x37ca2a30, 0x8ccc477d, 0x376f9e46, 0x8ca099da,
  0x3714f02a, 0x8c753362,
  0x36ba2014, 0x8c4a142f, 0x365f2e3b, 0x8c1f3c5d, 0x36041ad9, 0x8bf4ac05,
  0x35a8e625, 0x8bca6343,
  0x354d9057, 0x8ba0622f, 0x34f219a8, 0x8b76a8e4, 0x34968250, 0x8b4d377c,
  0x343aca87, 0x8b240e11,
  0x33def287, 0x8afb2cbb, 0x3382fa88, 0x8ad29394, 0x3326e2c3, 0x8aaa42b4,
  0x32caab6f, 0x8a823a36,
  0x326e54c7, 0x8a5a7a31, 0x3211df04, 0x8a3302be, 0x31b54a5e, 0x8a0bd3f5,
  0x3158970e, 0x89e4edef,
  0x30fbc54d, 0x89be50c3, 0x309ed556, 0x8997fc8a, 0x3041c761, 0x8971f15a,
  0x2fe49ba7, 0x894c2f4c,
  0x2f875262, 0x8926b677, 0x2f29ebcc, 0x890186f2, 0x2ecc681e, 0x88dca0d3,
  0x2e6ec792, 0x88b80432,
  0x2e110a62, 0x8893b125, 0x2db330c7, 0x886fa7c2, 0x2d553afc, 0x884be821,
  0x2cf72939, 0x88287256,
  0x2c98fbba, 0x88054677, 0x2c3ab2b9, 0x87e2649b, 0x2bdc4e6f, 0x87bfccd7,
  0x2b7dcf17, 0x879d7f41,
  0x2b1f34eb, 0x877b7bec, 0x2ac08026, 0x8759c2ef, 0x2a61b101, 0x8738545e,
  0x2a02c7b8, 0x8717304e,
  0x29a3c485, 0x86f656d3, 0x2944a7a2, 0x86d5c802, 0x28e5714b, 0x86b583ee,
  0x288621b9, 0x86958aac,
  0x2826b928, 0x8675dc4f, 0x27c737d3, 0x865678eb, 0x27679df4, 0x86376092,
  0x2707ebc7, 0x86189359,
  0x26a82186, 0x85fa1153, 0x26483f6c, 0x85dbda91, 0x25e845b6, 0x85bdef28,
  0x2588349d, 0x85a04f28,
  0x25280c5e, 0x8582faa5, 0x24c7cd33, 0x8565f1b0, 0x24677758, 0x8549345c,
  0x24070b08, 0x852cc2bb,
  0x23a6887f, 0x85109cdd, 0x2345eff8, 0x84f4c2d4, 0x22e541af, 0x84d934b1,
  0x22847de0, 0x84bdf286,
  0x2223a4c5, 0x84a2fc62, 0x21c2b69c, 0x84885258, 0x2161b3a0, 0x846df477,
  0x21009c0c, 0x8453e2cf,
  0x209f701c, 0x843a1d70, 0x203e300d, 0x8420a46c, 0x1fdcdc1b, 0x840777d0,
  0x1f7b7481, 0x83ee97ad,
  0x1f19f97b, 0x83d60412, 0x1eb86b46, 0x83bdbd0e, 0x1e56ca1e, 0x83a5c2b0,
  0x1df5163f, 0x838e1507,
  0x1d934fe5, 0x8376b422, 0x1d31774d, 0x835fa00f, 0x1ccf8cb3, 0x8348d8dc,
  0x1c6d9053, 0x83325e97,
  0x1c0b826a, 0x831c314e, 0x1ba96335, 0x83065110, 0x1b4732ef, 0x82f0bde8,
  0x1ae4f1d6, 0x82db77e5,
  0x1a82a026, 0x82c67f14, 0x1a203e1b, 0x82b1d381, 0x19bdcbf3, 0x829d753a,
  0x195b49ea, 0x8289644b,
  0x18f8b83c, 0x8275a0c0, 0x18961728, 0x82622aa6, 0x183366e9, 0x824f0208,
  0x17d0a7bc, 0x823c26f3,
  0x176dd9de, 0x82299971, 0x170afd8d, 0x82175990, 0x16a81305, 0x82056758,
  0x16451a83, 0x81f3c2d7,
  0x15e21445, 0x81e26c16, 0x157f0086, 0x81d16321, 0x151bdf86, 0x81c0a801,
  0x14b8b17f, 0x81b03ac2,
  0x145576b1, 0x81a01b6d, 0x13f22f58, 0x81904a0c, 0x138edbb1, 0x8180c6a9,
  0x132b7bf9, 0x8171914e,
  0x12c8106f, 0x8162aa04, 0x1264994e, 0x815410d4, 0x120116d5, 0x8145c5c7,
  0x119d8941, 0x8137c8e6,
  0x1139f0cf, 0x812a1a3a, 0x10d64dbd, 0x811cb9ca, 0x1072a048, 0x810fa7a0,
  0x100ee8ad, 0x8102e3c4,
  0xfab272b, 0x80f66e3c, 0xf475bff, 0x80ea4712, 0xee38766, 0x80de6e4c,
  0xe7fa99e, 0x80d2e3f2,
  0xe1bc2e4, 0x80c7a80a, 0xdb7d376, 0x80bcba9d, 0xd53db92, 0x80b21baf,
  0xcefdb76, 0x80a7cb49,
  0xc8bd35e, 0x809dc971, 0xc27c389, 0x8094162c, 0xbc3ac35, 0x808ab180,
  0xb5f8d9f, 0x80819b74,
  0xafb6805, 0x8078d40d, 0xa973ba5, 0x80705b50, 0xa3308bd, 0x80683143,
  0x9cecf89, 0x806055eb,
  0x96a9049, 0x8058c94c, 0x9064b3a, 0x80518b6b, 0x8a2009a, 0x804a9c4d,
  0x83db0a7, 0x8043fbf6,
  0x7d95b9e, 0x803daa6a, 0x77501be, 0x8037a7ac, 0x710a345, 0x8031f3c2,
  0x6ac406f, 0x802c8ead,
  0x647d97c, 0x80277872, 0x5e36ea9, 0x8022b114, 0x57f0035, 0x801e3895,
  0x51a8e5c, 0x801a0ef8,
  0x4b6195d, 0x80163440, 0x451a177, 0x8012a86f, 0x3ed26e6, 0x800f6b88,
  0x388a9ea, 0x800c7d8c,
  0x3242abf, 0x8009de7e, 0x2bfa9a4, 0x80078e5e, 0x25b26d7, 0x80058d2f,
  0x1f6a297, 0x8003daf1,
  0x1921d20, 0x800277a6, 0x12d96b1, 0x8001634e, 0xc90f88, 0x80009dea,
  0x6487e3, 0x8000277a,
};

static const q31_t WeightsQ31_2048[4096] = {
  0x7fffffff, 0x0, 0x7ffffd88, 0xffe6de05, 0x7ffff621, 0xffcdbc0b, 0x7fffe9cb,
  0xffb49a12,
  0x7fffd886, 0xff9b781d, 0x7fffc251, 0xff82562c, 0x7fffa72c, 0xff69343f,
  0x7fff8719, 0xff501258,
  0x7fff6216, 0xff36f078, 0x7fff3824, 0xff1dcea0, 0x7fff0943, 0xff04acd0,
  0x7ffed572, 0xfeeb8b0a,
  0x7ffe9cb2, 0xfed2694f, 0x7ffe5f03, 0xfeb947a0, 0x7ffe1c65, 0xfea025fd,
  0x7ffdd4d7, 0xfe870467,
  0x7ffd885a, 0xfe6de2e0, 0x7ffd36ee, 0xfe54c169, 0x7ffce093, 0xfe3ba002,
  0x7ffc8549, 0xfe227eac,
  0x7ffc250f, 0xfe095d69, 0x7ffbbfe6, 0xfdf03c3a, 0x7ffb55ce, 0xfdd71b1e,
  0x7ffae6c7, 0xfdbdfa18,
  0x7ffa72d1, 0xfda4d929, 0x7ff9f9ec, 0xfd8bb850, 0x7ff97c18, 0xfd729790,
  0x7ff8f954, 0xfd5976e9,
  0x7ff871a2, 0xfd40565c, 0x7ff7e500, 0xfd2735ea, 0x7ff75370, 0xfd0e1594,
  0x7ff6bcf0, 0xfcf4f55c,
  0x7ff62182, 0xfcdbd541, 0x7ff58125, 0xfcc2b545, 0x7ff4dbd9, 0xfca9956a,
  0x7ff4319d, 0xfc9075af,
  0x7ff38274, 0xfc775616, 0x7ff2ce5b, 0xfc5e36a0, 0x7ff21553, 0xfc45174e,
  0x7ff1575d, 0xfc2bf821,
  0x7ff09478, 0xfc12d91a, 0x7fefcca4, 0xfbf9ba39, 0x7feeffe1, 0xfbe09b80,
  0x7fee2e30, 0xfbc77cf0,
  0x7fed5791, 0xfbae5e89, 0x7fec7c02, 0xfb95404d, 0x7feb9b85, 0xfb7c223d,
  0x7feab61a, 0xfb630459,
  0x7fe9cbc0, 0xfb49e6a3, 0x7fe8dc78, 0xfb30c91b, 0x7fe7e841, 0xfb17abc2,
  0x7fe6ef1c, 0xfafe8e9b,
  0x7fe5f108, 0xfae571a4, 0x7fe4ee06, 0xfacc54e0, 0x7fe3e616, 0xfab3384f,
  0x7fe2d938, 0xfa9a1bf3,
  0x7fe1c76b, 0xfa80ffcb, 0x7fe0b0b1, 0xfa67e3da, 0x7fdf9508, 0xfa4ec821,
  0x7fde7471, 0xfa35ac9f,
  0x7fdd4eec, 0xfa1c9157, 0x7fdc247a, 0xfa037648, 0x7fdaf519, 0xf9ea5b75,
  0x7fd9c0ca, 0xf9d140de,
  0x7fd8878e, 0xf9b82684, 0x7fd74964, 0xf99f0c68, 0x7fd6064c, 0xf985f28a,
  0x7fd4be46, 0xf96cd8ed,
  0x7fd37153, 0xf953bf91, 0x7fd21f72, 0xf93aa676, 0x7fd0c8a3, 0xf9218d9e,
  0x7fcf6ce8, 0xf908750a,
  0x7fce0c3e, 0xf8ef5cbb, 0x7fcca6a7, 0xf8d644b2, 0x7fcb3c23, 0xf8bd2cef,
  0x7fc9ccb2, 0xf8a41574,
  0x7fc85854, 0xf88afe42, 0x7fc6df08, 0xf871e759, 0x7fc560cf, 0xf858d0bb,
  0x7fc3dda9, 0xf83fba68,
  0x7fc25596, 0xf826a462, 0x7fc0c896, 0xf80d8ea9, 0x7fbf36aa, 0xf7f4793e,
  0x7fbd9fd0, 0xf7db6423,
  0x7fbc040a, 0xf7c24f59, 0x7fba6357, 0xf7a93ae0, 0x7fb8bdb8, 0xf79026b9,
  0x7fb7132b, 0xf77712e5,
  0x7fb563b3, 0xf75dff66, 0x7fb3af4e, 0xf744ec3b, 0x7fb1f5fc, 0xf72bd967,
  0x7fb037bf, 0xf712c6ea,
  0x7fae7495, 0xf6f9b4c6, 0x7facac7f, 0xf6e0a2fa, 0x7faadf7c, 0xf6c79188,
  0x7fa90d8e, 0xf6ae8071,
  0x7fa736b4, 0xf6956fb7, 0x7fa55aee, 0xf67c5f59, 0x7fa37a3c, 0xf6634f59,
  0x7fa1949e, 0xf64a3fb8,
  0x7f9faa15, 0xf6313077, 0x7f9dbaa0, 0xf6182196, 0x7f9bc640, 0xf5ff1318,
  0x7f99ccf4, 0xf5e604fc,
  0x7f97cebd, 0xf5ccf743, 0x7f95cb9a, 0xf5b3e9f0, 0x7f93c38c, 0xf59add02,
  0x7f91b694, 0xf581d07b,
  0x7f8fa4b0, 0xf568c45b, 0x7f8d8de1, 0xf54fb8a4, 0x7f8b7227, 0xf536ad56,
  0x7f895182, 0xf51da273,
  0x7f872bf3, 0xf50497fb, 0x7f850179, 0xf4eb8def, 0x7f82d214, 0xf4d28451,
  0x7f809dc5, 0xf4b97b21,
  0x7f7e648c, 0xf4a07261, 0x7f7c2668, 0xf4876a10, 0x7f79e35a, 0xf46e6231,
  0x7f779b62, 0xf4555ac5,
  0x7f754e80, 0xf43c53cb, 0x7f72fcb4, 0xf4234d45, 0x7f70a5fe, 0xf40a4735,
  0x7f6e4a5e, 0xf3f1419a,
  0x7f6be9d4, 0xf3d83c77, 0x7f698461, 0xf3bf37cb, 0x7f671a05, 0xf3a63398,
  0x7f64aabf, 0xf38d2fe0,
  0x7f62368f, 0xf3742ca2, 0x7f5fbd77, 0xf35b29e0, 0x7f5d3f75, 0xf342279b,
  0x7f5abc8a, 0xf32925d3,
  0x7f5834b7, 0xf310248a, 0x7f55a7fa, 0xf2f723c1, 0x7f531655, 0xf2de2379,
  0x7f507fc7, 0xf2c523b2,
  0x7f4de451, 0xf2ac246e, 0x7f4b43f2, 0xf29325ad, 0x7f489eaa, 0xf27a2771,
  0x7f45f47b, 0xf26129ba,
  0x7f434563, 0xf2482c8a, 0x7f409164, 0xf22f2fe1, 0x7f3dd87c, 0xf21633c0,
  0x7f3b1aad, 0xf1fd3829,
  0x7f3857f6, 0xf1e43d1c, 0x7f359057, 0xf1cb429a, 0x7f32c3d1, 0xf1b248a5,
  0x7f2ff263, 0xf1994f3d,
  0x7f2d1c0e, 0xf1805662, 0x7f2a40d2, 0xf1675e17, 0x7f2760af, 0xf14e665c,
  0x7f247ba5, 0xf1356f32,
  0x7f2191b4, 0xf11c789a, 0x7f1ea2dc, 0xf1038295, 0x7f1baf1e, 0xf0ea8d24,
  0x7f18b679, 0xf0d19848,
  0x7f15b8ee, 0xf0b8a401, 0x7f12b67c, 0xf09fb051, 0x7f0faf25, 0xf086bd39,
  0x7f0ca2e7, 0xf06dcaba,
  0x7f0991c4, 0xf054d8d5, 0x7f067bba, 0xf03be78a, 0x7f0360cb, 0xf022f6da,
  0x7f0040f6, 0xf00a06c8,
  0x7efd1c3c, 0xeff11753, 0x7ef9f29d, 0xefd8287c, 0x7ef6c418, 0xefbf3a45,
  0x7ef390ae, 0xefa64cae,
  0x7ef05860, 0xef8d5fb8, 0x7eed1b2c, 0xef747365, 0x7ee9d914, 0xef5b87b5,
  0x7ee69217, 0xef429caa,
  0x7ee34636, 0xef29b243, 0x7edff570, 0xef10c883, 0x7edc9fc6, 0xeef7df6a,
  0x7ed94538, 0xeedef6f9,
  0x7ed5e5c6, 0xeec60f31, 0x7ed28171, 0xeead2813, 0x7ecf1837, 0xee9441a0,
  0x7ecbaa1a, 0xee7b5bd9,
  0x7ec8371a, 0xee6276bf, 0x7ec4bf36, 0xee499253, 0x7ec14270, 0xee30ae96,
  0x7ebdc0c6, 0xee17cb88,
  0x7eba3a39, 0xedfee92b, 0x7eb6aeca, 0xede60780, 0x7eb31e78, 0xedcd2687,
  0x7eaf8943, 0xedb44642,
  0x7eabef2c, 0xed9b66b2, 0x7ea85033, 0xed8287d7, 0x7ea4ac58, 0xed69a9b3,
  0x7ea1039b, 0xed50cc46,
  0x7e9d55fc, 0xed37ef91, 0x7e99a37c, 0xed1f1396, 0x7e95ec1a, 0xed063856,
  0x7e922fd6, 0xeced5dd0,
  0x7e8e6eb2, 0xecd48407, 0x7e8aa8ac, 0xecbbaafb, 0x7e86ddc6, 0xeca2d2ad,
  0x7e830dff, 0xec89fb1e,
  0x7e7f3957, 0xec71244f, 0x7e7b5fce, 0xec584e41, 0x7e778166, 0xec3f78f6,
  0x7e739e1d, 0xec26a46d,
  0x7e6fb5f4, 0xec0dd0a8, 0x7e6bc8eb, 0xebf4fda8, 0x7e67d703, 0xebdc2b6e,
  0x7e63e03b, 0xebc359fb,
  0x7e5fe493, 0xebaa894f, 0x7e5be40c, 0xeb91b96c, 0x7e57dea7, 0xeb78ea52,
  0x7e53d462, 0xeb601c04,
  0x7e4fc53e, 0xeb474e81, 0x7e4bb13c, 0xeb2e81ca, 0x7e47985b, 0xeb15b5e1,
  0x7e437a9c, 0xeafceac6,
  0x7e3f57ff, 0xeae4207a, 0x7e3b3083, 0xeacb56ff, 0x7e37042a, 0xeab28e56,
  0x7e32d2f4, 0xea99c67e,
  0x7e2e9cdf, 0xea80ff7a, 0x7e2a61ed, 0xea683949, 0x7e26221f, 0xea4f73ee,
  0x7e21dd73, 0xea36af69,
  0x7e1d93ea, 0xea1debbb, 0x7e194584, 0xea0528e5, 0x7e14f242, 0xe9ec66e8,
  0x7e109a24, 0xe9d3a5c5,
  0x7e0c3d29, 0xe9bae57d, 0x7e07db52, 0xe9a22610, 0x7e0374a0, 0xe9896781,
  0x7dff0911, 0xe970a9ce,
  0x7dfa98a8, 0xe957ecfb, 0x7df62362, 0xe93f3107, 0x7df1a942, 0xe92675f4,
  0x7ded2a47, 0xe90dbbc2,
  0x7de8a670, 0xe8f50273, 0x7de41dc0, 0xe8dc4a07, 0x7ddf9034, 0xe8c39280,
  0x7ddafdce, 0xe8aadbde,
  0x7dd6668f, 0xe8922622, 0x7dd1ca75, 0xe879714d, 0x7dcd2981, 0xe860bd61,
  0x7dc883b4, 0xe8480a5d,
  0x7dc3d90d, 0xe82f5844, 0x7dbf298d, 0xe816a716, 0x7dba7534, 0xe7fdf6d4,
  0x7db5bc02, 0xe7e5477f,
  0x7db0fdf8, 0xe7cc9917, 0x7dac3b15, 0xe7b3eb9f, 0x7da77359, 0xe79b3f16,
  0x7da2a6c6, 0xe782937e,
  0x7d9dd55a, 0xe769e8d8, 0x7d98ff17, 0xe7513f25, 0x7d9423fc, 0xe7389665,
  0x7d8f4409, 0xe71fee99,
  0x7d8a5f40, 0xe70747c4, 0x7d85759f, 0xe6eea1e4, 0x7d808728, 0xe6d5fcfc,
  0x7d7b93da, 0xe6bd590d,
  0x7d769bb5, 0xe6a4b616, 0x7d719eba, 0xe68c141a, 0x7d6c9ce9, 0xe6737319,
  0x7d679642, 0xe65ad315,
  0x7d628ac6, 0xe642340d, 0x7d5d7a74, 0xe6299604, 0x7d58654d, 0xe610f8f9,
  0x7d534b50, 0xe5f85cef,
  0x7d4e2c7f, 0xe5dfc1e5, 0x7d4908d9, 0xe5c727dd, 0x7d43e05e, 0xe5ae8ed8,
  0x7d3eb30f, 0xe595f6d7,
  0x7d3980ec, 0xe57d5fda, 0x7d3449f5, 0xe564c9e3, 0x7d2f0e2b, 0xe54c34f3,
  0x7d29cd8c, 0xe533a10a,
  0x7d24881b, 0xe51b0e2a, 0x7d1f3dd6, 0xe5027c53, 0x7d19eebf, 0xe4e9eb87,
  0x7d149ad5, 0xe4d15bc6,
  0x7d0f4218, 0xe4b8cd11, 0x7d09e489, 0xe4a03f69, 0x7d048228, 0xe487b2d0,
  0x7cff1af5, 0xe46f2745,
  0x7cf9aef0, 0xe4569ccb, 0x7cf43e1a, 0xe43e1362, 0x7ceec873, 0xe4258b0a,
  0x7ce94dfb, 0xe40d03c6,
  0x7ce3ceb2, 0xe3f47d96, 0x7cde4a98, 0xe3dbf87a, 0x7cd8c1ae, 0xe3c37474,
  0x7cd333f3, 0xe3aaf184,
  0x7ccda169, 0xe3926fad, 0x7cc80a0f, 0xe379eeed, 0x7cc26de5, 0xe3616f48,
  0x7cbcccec, 0xe348f0bd,
  0x7cb72724, 0xe330734d, 0x7cb17c8d, 0xe317f6fa, 0x7cabcd28, 0xe2ff7bc3,
  0x7ca618f3, 0xe2e701ac,
  0x7ca05ff1, 0xe2ce88b3, 0x7c9aa221, 0xe2b610da, 0x7c94df83, 0xe29d9a23,
  0x7c8f1817, 0xe285248d,
  0x7c894bde, 0xe26cb01b, 0x7c837ad8, 0xe2543ccc, 0x7c7da505, 0xe23bcaa2,
  0x7c77ca65, 0xe223599e,
  0x7c71eaf9, 0xe20ae9c1, 0x7c6c06c0, 0xe1f27b0b, 0x7c661dbc, 0xe1da0d7e,
  0x7c602fec, 0xe1c1a11b,
  0x7c5a3d50, 0xe1a935e2, 0x7c5445e9, 0xe190cbd4, 0x7c4e49b7, 0xe17862f3,
  0x7c4848ba, 0xe15ffb3f,
  0x7c4242f2, 0xe14794ba, 0x7c3c3860, 0xe12f2f63, 0x7c362904, 0xe116cb3d,
  0x7c3014de, 0xe0fe6848,
  0x7c29fbee, 0xe0e60685, 0x7c23de35, 0xe0cda5f5, 0x7c1dbbb3, 0xe0b54698,
  0x7c179467, 0xe09ce871,
  0x7c116853, 0xe0848b7f, 0x7c0b3777, 0xe06c2fc4, 0x7c0501d2, 0xe053d541,
  0x7bfec765, 0xe03b7bf6,
  0x7bf88830, 0xe02323e5, 0x7bf24434, 0xe00acd0e, 0x7bebfb70, 0xdff27773,
  0x7be5ade6, 0xdfda2314,
  0x7bdf5b94, 0xdfc1cff3, 0x7bd9047c, 0xdfa97e0f, 0x7bd2a89e, 0xdf912d6b,
  0x7bcc47fa, 0xdf78de07,
  0x7bc5e290, 0xdf608fe4, 0x7bbf7860, 0xdf484302, 0x7bb9096b, 0xdf2ff764,
  0x7bb295b0, 0xdf17ad0a,
  0x7bac1d31, 0xdeff63f4, 0x7ba59fee, 0xdee71c24, 0x7b9f1de6, 0xdeced59b,
  0x7b989719, 0xdeb69059,
  0x7b920b89, 0xde9e4c60, 0x7b8b7b36, 0xde8609b1, 0x7b84e61f, 0xde6dc84b,
  0x7b7e4c45, 0xde558831,
  0x7b77ada8, 0xde3d4964, 0x7b710a49, 0xde250be3, 0x7b6a6227, 0xde0ccfb1,
  0x7b63b543, 0xddf494ce,
  0x7b5d039e, 0xdddc5b3b, 0x7b564d36, 0xddc422f8, 0x7b4f920e, 0xddabec08,
  0x7b48d225, 0xdd93b66a,
  0x7b420d7a, 0xdd7b8220, 0x7b3b4410, 0xdd634f2b, 0x7b3475e5, 0xdd4b1d8c,
  0x7b2da2fa, 0xdd32ed43,
  0x7b26cb4f, 0xdd1abe51, 0x7b1feee5, 0xdd0290b8, 0x7b190dbc, 0xdcea6478,
  0x7b1227d3, 0xdcd23993,
  0x7b0b3d2c, 0xdcba1008, 0x7b044dc7, 0xdca1e7da, 0x7afd59a4, 0xdc89c109,
  0x7af660c2, 0xdc719b96,
  0x7aef6323, 0xdc597781, 0x7ae860c7, 0xdc4154cd, 0x7ae159ae, 0xdc293379,
  0x7ada4dd8, 0xdc111388,
  0x7ad33d45, 0xdbf8f4f8, 0x7acc27f7, 0xdbe0d7cd, 0x7ac50dec, 0xdbc8bc06,
  0x7abdef25, 0xdbb0a1a4,
  0x7ab6cba4, 0xdb9888a8, 0x7aafa367, 0xdb807114, 0x7aa8766f, 0xdb685ae9,
  0x7aa144bc, 0xdb504626,
  0x7a9a0e50, 0xdb3832cd, 0x7a92d329, 0xdb2020e0, 0x7a8b9348, 0xdb08105e,
  0x7a844eae, 0xdaf00149,
  0x7a7d055b, 0xdad7f3a2, 0x7a75b74f, 0xdabfe76a, 0x7a6e648a, 0xdaa7dca1,
  0x7a670d0d, 0xda8fd349,
  0x7a5fb0d8, 0xda77cb63, 0x7a584feb, 0xda5fc4ef, 0x7a50ea47, 0xda47bfee,
  0x7a497feb, 0xda2fbc61,
  0x7a4210d8, 0xda17ba4a, 0x7a3a9d0f, 0xd9ffb9a9, 0x7a332490, 0xd9e7ba7f,
  0x7a2ba75a, 0xd9cfbccd,
  0x7a24256f, 0xd9b7c094, 0x7a1c9ece, 0xd99fc5d4, 0x7a151378, 0xd987cc90,
  0x7a0d836d, 0xd96fd4c7,
  0x7a05eead, 0xd957de7a, 0x79fe5539, 0xd93fe9ab, 0x79f6b711, 0xd927f65b,
  0x79ef1436, 0xd910048a,
  0x79e76ca7, 0xd8f81439, 0x79dfc064, 0xd8e0256a, 0x79d80f6f, 0xd8c8381d,
  0x79d059c8, 0xd8b04c52,
  0x79c89f6e, 0xd898620c, 0x79c0e062, 0xd880794b, 0x79b91ca4, 0xd868920f,
  0x79b15435, 0xd850ac5a,
  0x79a98715, 0xd838c82d, 0x79a1b545, 0xd820e589, 0x7999dec4, 0xd809046e,
  0x79920392, 0xd7f124dd,
  0x798a23b1, 0xd7d946d8, 0x79823f20, 0xd7c16a5f, 0x797a55e0, 0xd7a98f73,
  0x797267f2, 0xd791b616,
  0x796a7554, 0xd779de47, 0x79627e08, 0xd7620808, 0x795a820e, 0xd74a335b,
  0x79528167, 0xd732603f,
  0x794a7c12, 0xd71a8eb5, 0x79427210, 0xd702bec0, 0x793a6361, 0xd6eaf05f,
  0x79325006, 0xd6d32393,
  0x792a37fe, 0xd6bb585e, 0x79221b4b, 0xd6a38ec0, 0x7919f9ec, 0xd68bc6ba,
  0x7911d3e2, 0xd674004e,
  0x7909a92d, 0xd65c3b7b, 0x790179cd, 0xd6447844, 0x78f945c3, 0xd62cb6a8,
  0x78f10d0f, 0xd614f6a9,
  0x78e8cfb2, 0xd5fd3848, 0x78e08dab, 0xd5e57b85, 0x78d846fb, 0xd5cdc062,
  0x78cffba3, 0xd5b606e0,
  0x78c7aba2, 0xd59e4eff, 0x78bf56f9, 0xd58698c0, 0x78b6fda8, 0xd56ee424,
  0x78ae9fb0, 0xd557312d,
  0x78a63d11, 0xd53f7fda, 0x789dd5cb, 0xd527d02e, 0x789569df, 0xd5102228,
  0x788cf94c, 0xd4f875ca,
  0x78848414, 0xd4e0cb15, 0x787c0a36, 0xd4c92209, 0x78738bb3, 0xd4b17aa8,
  0x786b088c, 0xd499d4f2,
  0x786280bf, 0xd48230e9, 0x7859f44f, 0xd46a8e8d, 0x7851633b, 0xd452eddf,
  0x7848cd83, 0xd43b4ee0,
  0x78403329, 0xd423b191, 0x7837942b, 0xd40c15f3, 0x782ef08b, 0xd3f47c06,
  0x78264849, 0xd3dce3cd,
  0x781d9b65, 0xd3c54d47, 0x7814e9df, 0xd3adb876, 0x780c33b8, 0xd396255a,
  0x780378f1, 0xd37e93f4,
  0x77fab989, 0xd3670446, 0x77f1f581, 0xd34f764f, 0x77e92cd9, 0xd337ea12,
  0x77e05f91, 0xd3205f8f,
  0x77d78daa, 0xd308d6c7, 0x77ceb725, 0xd2f14fba, 0x77c5dc01, 0xd2d9ca6a,
  0x77bcfc3f, 0xd2c246d8,
  0x77b417df, 0xd2aac504, 0x77ab2ee2, 0xd29344f0, 0x77a24148, 0xd27bc69c,
  0x77994f11, 0xd2644a0a,
  0x7790583e, 0xd24ccf39, 0x77875cce, 0xd235562b, 0x777e5cc3, 0xd21ddee2,
  0x7775581d, 0xd206695d,
  0x776c4edb, 0xd1eef59e, 0x776340ff, 0xd1d783a6, 0x775a2e89, 0xd1c01375,
  0x77511778, 0xd1a8a50d,
  0x7747fbce, 0xd191386e, 0x773edb8b, 0xd179cd99, 0x7735b6af, 0xd1626490,
  0x772c8d3a, 0xd14afd52,
  0x77235f2d, 0xd13397e2, 0x771a2c88, 0xd11c343f, 0x7710f54c, 0xd104d26b,
  0x7707b979, 0xd0ed7267,
  0x76fe790e, 0xd0d61434, 0x76f5340e, 0xd0beb7d2, 0x76ebea77, 0xd0a75d42,
  0x76e29c4b, 0xd0900486,
  0x76d94989, 0xd078ad9e, 0x76cff232, 0xd061588b, 0x76c69647, 0xd04a054e,
  0x76bd35c7, 0xd032b3e7,
  0x76b3d0b4, 0xd01b6459, 0x76aa670d, 0xd00416a3, 0x76a0f8d2, 0xcfeccac7,
  0x76978605, 0xcfd580c6,
  0x768e0ea6, 0xcfbe389f, 0x768492b4, 0xcfa6f255, 0x767b1231, 0xcf8fade9,
  0x76718d1c, 0xcf786b5a,
  0x76680376, 0xcf612aaa, 0x765e7540, 0xcf49ebda, 0x7654e279, 0xcf32aeeb,
  0x764b4b23, 0xcf1b73de,
  0x7641af3d, 0xcf043ab3, 0x76380ec8, 0xceed036b, 0x762e69c4, 0xced5ce08,
  0x7624c031, 0xcebe9a8a,
  0x761b1211, 0xcea768f2, 0x76115f63, 0xce903942, 0x7607a828, 0xce790b79,
  0x75fdec60, 0xce61df99,
  0x75f42c0b, 0xce4ab5a2, 0x75ea672a, 0xce338d97, 0x75e09dbd, 0xce1c6777,
  0x75d6cfc5, 0xce054343,
  0x75ccfd42, 0xcdee20fc, 0x75c32634, 0xcdd700a4, 0x75b94a9c, 0xcdbfe23a,
  0x75af6a7b, 0xcda8c5c1,
  0x75a585cf, 0xcd91ab39, 0x759b9c9b, 0xcd7a92a2, 0x7591aedd, 0xcd637bfe,
  0x7587bc98, 0xcd4c674d,
  0x757dc5ca, 0xcd355491, 0x7573ca75, 0xcd1e43ca, 0x7569ca99, 0xcd0734f9,
  0x755fc635, 0xccf0281f,
  0x7555bd4c, 0xccd91d3d, 0x754bafdc, 0xccc21455, 0x75419de7, 0xccab0d65,
  0x7537876c, 0xcc940871,
  0x752d6c6c, 0xcc7d0578, 0x75234ce8, 0xcc66047b, 0x751928e0, 0xcc4f057c,
  0x750f0054, 0xcc38087b,
  0x7504d345, 0xcc210d79, 0x74faa1b3, 0xcc0a1477, 0x74f06b9e, 0xcbf31d75,
  0x74e63108, 0xcbdc2876,
  0x74dbf1ef, 0xcbc53579, 0x74d1ae55, 0xcbae447f, 0x74c7663a, 0xcb97558a,
  0x74bd199f, 0xcb80689a,
  0x74b2c884, 0xcb697db0, 0x74a872e8, 0xcb5294ce, 0x749e18cd, 0xcb3badf3,
  0x7493ba34, 0xcb24c921,
  0x7489571c, 0xcb0de658, 0x747eef85, 0xcaf7059a, 0x74748371, 0xcae026e8,
  0x746a12df, 0xcac94a42,
  0x745f9dd1, 0xcab26fa9, 0x74552446, 0xca9b971e, 0x744aa63f, 0xca84c0a3,
  0x744023bc, 0xca6dec37,
  0x74359cbd, 0xca5719db, 0x742b1144, 0xca404992, 0x74208150, 0xca297b5a,
  0x7415ece2, 0xca12af37,
  0x740b53fb, 0xc9fbe527, 0x7400b69a, 0xc9e51d2d, 0x73f614c0, 0xc9ce5748,
  0x73eb6e6e, 0xc9b7937a,
  0x73e0c3a3, 0xc9a0d1c5, 0x73d61461, 0xc98a1227, 0x73cb60a8, 0xc97354a4,
  0x73c0a878, 0xc95c993a,
  0x73b5ebd1, 0xc945dfec, 0x73ab2ab4, 0xc92f28ba, 0x73a06522, 0xc91873a5,
  0x73959b1b, 0xc901c0ae,
  0x738acc9e, 0xc8eb0fd6, 0x737ff9ae, 0xc8d4611d, 0x73752249, 0xc8bdb485,
  0x736a4671, 0xc8a70a0e,
  0x735f6626, 0xc89061ba, 0x73548168, 0xc879bb89, 0x73499838, 0xc863177b,
  0x733eaa96, 0xc84c7593,
  0x7333b883, 0xc835d5d0, 0x7328c1ff, 0xc81f3834, 0x731dc70a, 0xc8089cbf,
  0x7312c7a5, 0xc7f20373,
  0x7307c3d0, 0xc7db6c50, 0x72fcbb8c, 0xc7c4d757, 0x72f1aed9, 0xc7ae4489,
  0x72e69db7, 0xc797b3e7,
  0x72db8828, 0xc7812572, 0x72d06e2b, 0xc76a992a, 0x72c54fc1, 0xc7540f11,
  0x72ba2cea, 0xc73d8727,
  0x72af05a7, 0xc727016d, 0x72a3d9f7, 0xc7107de4, 0x7298a9dd, 0xc6f9fc8d,
  0x728d7557, 0xc6e37d69,
  0x72823c67, 0xc6cd0079, 0x7276ff0d, 0xc6b685bd, 0x726bbd48, 0xc6a00d37,
  0x7260771b, 0xc68996e7,
  0x72552c85, 0xc67322ce, 0x7249dd86, 0xc65cb0ed, 0x723e8a20, 0xc6464144,
  0x72333251, 0xc62fd3d6,
  0x7227d61c, 0xc61968a2, 0x721c7580, 0xc602ffaa, 0x7211107e, 0xc5ec98ee,
  0x7205a716, 0xc5d6346f,
  0x71fa3949, 0xc5bfd22e, 0x71eec716, 0xc5a9722c, 0x71e35080, 0xc593146a,
  0x71d7d585, 0xc57cb8e9,
  0x71cc5626, 0xc5665fa9, 0x71c0d265, 0xc55008ab, 0x71b54a41, 0xc539b3f1,
  0x71a9bdba, 0xc523617a,
  0x719e2cd2, 0xc50d1149, 0x71929789, 0xc4f6c35d, 0x7186fdde, 0xc4e077b8,
  0x717b5fd3, 0xc4ca2e5b,
  0x716fbd68, 0xc4b3e746, 0x7164169d, 0xc49da27a, 0x71586b74, 0xc4875ff9,
  0x714cbbeb, 0xc4711fc2,
  0x71410805, 0xc45ae1d7, 0x71354fc0, 0xc444a639, 0x7129931f, 0xc42e6ce8,
  0x711dd220, 0xc41835e6,
  0x71120cc5, 0xc4020133, 0x7106430e, 0xc3ebced0, 0x70fa74fc, 0xc3d59ebe,
  0x70eea28e, 0xc3bf70fd,
  0x70e2cbc6, 0xc3a94590, 0x70d6f0a4, 0xc3931c76, 0x70cb1128, 0xc37cf5b0,
  0x70bf2d53, 0xc366d140,
  0x70b34525, 0xc350af26, 0x70a7589f, 0xc33a8f62, 0x709b67c0, 0xc32471f7,
  0x708f728b, 0xc30e56e4,
  0x708378ff, 0xc2f83e2a, 0x70777b1c, 0xc2e227cb, 0x706b78e3, 0xc2cc13c7,
  0x705f7255, 0xc2b6021f,
  0x70536771, 0xc29ff2d4, 0x70475839, 0xc289e5e7, 0x703b44ad, 0xc273db58,
  0x702f2ccd, 0xc25dd329,
  0x7023109a, 0xc247cd5a, 0x7016f014, 0xc231c9ec, 0x700acb3c, 0xc21bc8e1,
  0x6ffea212, 0xc205ca38,
  0x6ff27497, 0xc1efcdf3, 0x6fe642ca, 0xc1d9d412, 0x6fda0cae, 0xc1c3dc97,
  0x6fcdd241, 0xc1ade781,
  0x6fc19385, 0xc197f4d4, 0x6fb5507a, 0xc182048d, 0x6fa90921, 0xc16c16b0,
  0x6f9cbd79, 0xc1562b3d,
  0x6f906d84, 0xc1404233, 0x6f841942, 0xc12a5b95, 0x6f77c0b3, 0xc1147764,
  0x6f6b63d8, 0xc0fe959f,
  0x6f5f02b2, 0xc0e8b648, 0x6f529d40, 0xc0d2d960, 0x6f463383, 0xc0bcfee7,
  0x6f39c57d, 0xc0a726df,
  0x6f2d532c, 0xc0915148, 0x6f20dc92, 0xc07b7e23, 0x6f1461b0, 0xc065ad70,
  0x6f07e285, 0xc04fdf32,
  0x6efb5f12, 0xc03a1368, 0x6eeed758, 0xc0244a14, 0x6ee24b57, 0xc00e8336,
  0x6ed5bb10, 0xbff8bece,
  0x6ec92683, 0xbfe2fcdf, 0x6ebc8db0, 0xbfcd3d69, 0x6eaff099, 0xbfb7806c,
  0x6ea34f3d, 0xbfa1c5ea,
  0x6e96a99d, 0xbf8c0de3, 0x6e89ffb9, 0xbf765858, 0x6e7d5193, 0xbf60a54a,
  0x6e709f2a, 0xbf4af4ba,
  0x6e63e87f, 0xbf3546a8, 0x6e572d93, 0xbf1f9b16, 0x6e4a6e66, 0xbf09f205,
  0x6e3daaf8, 0xbef44b74,
  0x6e30e34a, 0xbedea765, 0x6e24175c, 0xbec905d9, 0x6e174730, 0xbeb366d1,
  0x6e0a72c5, 0xbe9dca4e,
  0x6dfd9a1c, 0xbe88304f, 0x6df0bd35, 0xbe7298d7, 0x6de3dc11, 0xbe5d03e6,
  0x6dd6f6b1, 0xbe47717c,
  0x6dca0d14, 0xbe31e19b, 0x6dbd1f3c, 0xbe1c5444, 0x6db02d29, 0xbe06c977,
  0x6da336dc, 0xbdf14135,
  0x6d963c54, 0xbddbbb7f, 0x6d893d93, 0xbdc63856, 0x6d7c3a98, 0xbdb0b7bb,
  0x6d6f3365, 0xbd9b39ad,
  0x6d6227fa, 0xbd85be30, 0x6d551858, 0xbd704542, 0x6d48047e, 0xbd5acee5,
  0x6d3aec6e, 0xbd455b1a,
  0x6d2dd027, 0xbd2fe9e2, 0x6d20afac, 0xbd1a7b3d, 0x6d138afb, 0xbd050f2c,
  0x6d066215, 0xbcefa5b0,
  0x6cf934fc, 0xbcda3ecb, 0x6cec03af, 0xbcc4da7b, 0x6cdece2f, 0xbcaf78c4,
  0x6cd1947c, 0xbc9a19a5,
  0x6cc45698, 0xbc84bd1f, 0x6cb71482, 0xbc6f6333, 0x6ca9ce3b, 0xbc5a0be2,
  0x6c9c83c3, 0xbc44b72c,
  0x6c8f351c, 0xbc2f6513, 0x6c81e245, 0xbc1a1598, 0x6c748b3f, 0xbc04c8ba,
  0x6c67300b, 0xbbef7e7c,
  0x6c59d0a9, 0xbbda36dd, 0x6c4c6d1a, 0xbbc4f1df, 0x6c3f055d, 0xbbafaf82,
  0x6c319975, 0xbb9a6fc7,
  0x6c242960, 0xbb8532b0, 0x6c16b521, 0xbb6ff83c, 0x6c093cb6, 0xbb5ac06d,
  0x6bfbc021, 0xbb458b43,
  0x6bee3f62, 0xbb3058c0, 0x6be0ba7b, 0xbb1b28e4, 0x6bd3316a, 0xbb05fbb0,
  0x6bc5a431, 0xbaf0d125,
  0x6bb812d1, 0xbadba943, 0x6baa7d49, 0xbac6840c, 0x6b9ce39b, 0xbab16180,
  0x6b8f45c7, 0xba9c41a0,
  0x6b81a3cd, 0xba87246d, 0x6b73fdae, 0xba7209e7, 0x6b66536b, 0xba5cf210,
  0x6b58a503, 0xba47dce8,
  0x6b4af279, 0xba32ca71, 0x6b3d3bcb, 0xba1dbaaa, 0x6b2f80fb, 0xba08ad95,
  0x6b21c208, 0xb9f3a332,
  0x6b13fef5, 0xb9de9b83, 0x6b0637c1, 0xb9c99688, 0x6af86c6c, 0xb9b49442,
  0x6aea9cf8, 0xb99f94b2,
  0x6adcc964, 0xb98a97d8, 0x6acef1b2, 0xb9759db6, 0x6ac115e2, 0xb960a64c,
  0x6ab335f4, 0xb94bb19b,
  0x6aa551e9, 0xb936bfa4, 0x6a9769c1, 0xb921d067, 0x6a897d7d, 0xb90ce3e6,
  0x6a7b8d1e, 0xb8f7fa21,
  0x6a6d98a4, 0xb8e31319, 0x6a5fa010, 0xb8ce2ecf, 0x6a51a361, 0xb8b94d44,
  0x6a43a29a, 0xb8a46e78,
  0x6a359db9, 0xb88f926d, 0x6a2794c1, 0xb87ab922, 0x6a1987b0, 0xb865e299,
  0x6a0b7689, 0xb8510ed4,
  0x69fd614a, 0xb83c3dd1, 0x69ef47f6, 0xb8276f93, 0x69e12a8c, 0xb812a41a,
  0x69d3090e, 0xb7fddb67,
  0x69c4e37a, 0xb7e9157a, 0x69b6b9d3, 0xb7d45255, 0x69a88c19, 0xb7bf91f8,
  0x699a5a4c, 0xb7aad465,
  0x698c246c, 0xb796199b, 0x697dea7b, 0xb781619c, 0x696fac78, 0xb76cac69,
  0x69616a65, 0xb757fa01,
  0x69532442, 0xb7434a67, 0x6944da10, 0xb72e9d9b, 0x69368bce, 0xb719f39e,
  0x6928397e, 0xb7054c6f,
  0x6919e320, 0xb6f0a812, 0x690b88b5, 0xb6dc0685, 0x68fd2a3d, 0xb6c767ca,
  0x68eec7b9, 0xb6b2cbe2,
  0x68e06129, 0xb69e32cd, 0x68d1f68f, 0xb6899c8d, 0x68c387e9, 0xb6750921,
  0x68b5153a, 0xb660788c,
  0x68a69e81, 0xb64beacd, 0x689823bf, 0xb6375fe5, 0x6889a4f6, 0xb622d7d6,
  0x687b2224, 0xb60e529f,
  0x686c9b4b, 0xb5f9d043, 0x685e106c, 0xb5e550c1, 0x684f8186, 0xb5d0d41a,
  0x6840ee9b, 0xb5bc5a50,
  0x683257ab, 0xb5a7e362, 0x6823bcb7, 0xb5936f53, 0x68151dbe, 0xb57efe22,
  0x68067ac3, 0xb56a8fd0,
  0x67f7d3c5, 0xb556245e, 0x67e928c5, 0xb541bbcd, 0x67da79c3, 0xb52d561e,
  0x67cbc6c0, 0xb518f351,
  0x67bd0fbd, 0xb5049368, 0x67ae54ba, 0xb4f03663, 0x679f95b7, 0xb4dbdc42,
  0x6790d2b6, 0xb4c78507,
  0x67820bb7, 0xb4b330b3, 0x677340ba, 0xb49edf45, 0x676471c0, 0xb48a90c0,
  0x67559eca, 0xb4764523,
  0x6746c7d8, 0xb461fc70, 0x6737ecea, 0xb44db6a8, 0x67290e02, 0xb43973ca,
  0x671a2b20, 0xb42533d8,
  0x670b4444, 0xb410f6d3, 0x66fc596f, 0xb3fcbcbb, 0x66ed6aa1, 0xb3e88592,
  0x66de77dc, 0xb3d45157,
  0x66cf8120, 0xb3c0200c, 0x66c0866d, 0xb3abf1b2, 0x66b187c3, 0xb397c649,
  0x66a28524, 0xb3839dd3,
  0x66937e91, 0xb36f784f, 0x66847408, 0xb35b55bf, 0x6675658c, 0xb3473623,
  0x6666531d, 0xb333197c,
  0x66573cbb, 0xb31effcc, 0x66482267, 0xb30ae912, 0x66390422, 0xb2f6d550,
  0x6629e1ec, 0xb2e2c486,
  0x661abbc5, 0xb2ceb6b5, 0x660b91af, 0xb2baabde, 0x65fc63a9, 0xb2a6a402,
  0x65ed31b5, 0xb2929f21,
  0x65ddfbd3, 0xb27e9d3c, 0x65cec204, 0xb26a9e54, 0x65bf8447, 0xb256a26a,
  0x65b0429f, 0xb242a97e,
  0x65a0fd0b, 0xb22eb392, 0x6591b38c, 0xb21ac0a6, 0x65826622, 0xb206d0ba,
  0x657314cf, 0xb1f2e3d0,
  0x6563bf92, 0xb1def9e9, 0x6554666d, 0xb1cb1304, 0x6545095f, 0xb1b72f23,
  0x6535a86b, 0xb1a34e47,
  0x6526438f, 0xb18f7071, 0x6516dacd, 0xb17b95a0, 0x65076e25, 0xb167bdd7,
  0x64f7fd98, 0xb153e915,
  0x64e88926, 0xb140175b, 0x64d910d1, 0xb12c48ab, 0x64c99498, 0xb1187d05,
  0x64ba147d, 0xb104b46a,
  0x64aa907f, 0xb0f0eeda, 0x649b08a0, 0xb0dd2c56, 0x648b7ce0, 0xb0c96ce0,
  0x647bed3f, 0xb0b5b077,
  0x646c59bf, 0xb0a1f71d, 0x645cc260, 0xb08e40d2, 0x644d2722, 0xb07a8d97,
  0x643d8806, 0xb066dd6d,
  0x642de50d, 0xb0533055, 0x641e3e38, 0xb03f864f, 0x640e9386, 0xb02bdf5c,
  0x63fee4f8, 0xb0183b7d,
  0x63ef3290, 0xb0049ab3, 0x63df7c4d, 0xaff0fcfe, 0x63cfc231, 0xafdd625f,
  0x63c0043b, 0xafc9cad7,
  0x63b0426d, 0xafb63667, 0x63a07cc7, 0xafa2a50f, 0x6390b34a, 0xaf8f16d1,
  0x6380e5f6, 0xaf7b8bac,
  0x637114cc, 0xaf6803a2, 0x63613fcd, 0xaf547eb3, 0x635166f9, 0xaf40fce1,
  0x63418a50, 0xaf2d7e2b,
  0x6331a9d4, 0xaf1a0293, 0x6321c585, 0xaf068a1a, 0x6311dd64, 0xaef314c0,
  0x6301f171, 0xaedfa285,
  0x62f201ac, 0xaecc336c, 0x62e20e17, 0xaeb8c774, 0x62d216b3, 0xaea55e9e,
  0x62c21b7e, 0xae91f8eb,
  0x62b21c7b, 0xae7e965b, 0x62a219aa, 0xae6b36f0, 0x6292130c, 0xae57daab,
  0x628208a1, 0xae44818b,
  0x6271fa69, 0xae312b92, 0x6261e866, 0xae1dd8c0, 0x6251d298, 0xae0a8916,
  0x6241b8ff, 0xadf73c96,
  0x62319b9d, 0xade3f33e, 0x62217a72, 0xadd0ad12, 0x6211557e, 0xadbd6a10,
  0x62012cc2, 0xadaa2a3b,
  0x61f1003f, 0xad96ed92, 0x61e0cff5, 0xad83b416, 0x61d09be5, 0xad707dc8,
  0x61c06410, 0xad5d4aaa,
  0x61b02876, 0xad4a1aba, 0x619fe918, 0xad36edfc, 0x618fa5f7, 0xad23c46e,
  0x617f5f12, 0xad109e12,
  0x616f146c, 0xacfd7ae8, 0x615ec603, 0xacea5af2, 0x614e73da, 0xacd73e30,
  0x613e1df0, 0xacc424a3,
  0x612dc447, 0xacb10e4b, 0x611d66de, 0xac9dfb29, 0x610d05b7, 0xac8aeb3e,
  0x60fca0d2, 0xac77de8b,
  0x60ec3830, 0xac64d510, 0x60dbcbd1, 0xac51cecf, 0x60cb5bb7, 0xac3ecbc7,
  0x60bae7e1, 0xac2bcbfa,
  0x60aa7050, 0xac18cf69, 0x6099f505, 0xac05d613, 0x60897601, 0xabf2dffb,
  0x6078f344, 0xabdfed1f,
  0x60686ccf, 0xabccfd83, 0x6057e2a2, 0xabba1125, 0x604754bf, 0xaba72807,
  0x6036c325, 0xab944229,
  0x60262dd6, 0xab815f8d, 0x601594d1, 0xab6e8032, 0x6004f819, 0xab5ba41a,
  0x5ff457ad, 0xab48cb46,
  0x5fe3b38d, 0xab35f5b5, 0x5fd30bbc, 0xab23236a, 0x5fc26038, 0xab105464,
  0x5fb1b104, 0xaafd88a4,
  0x5fa0fe1f, 0xaaeac02c, 0x5f90478a, 0xaad7fafb, 0x5f7f8d46, 0xaac53912,
  0x5f6ecf53, 0xaab27a73,
  0x5f5e0db3, 0xaa9fbf1e, 0x5f4d4865, 0xaa8d0713, 0x5f3c7f6b, 0xaa7a5253,
  0x5f2bb2c5, 0xaa67a0e0,
  0x5f1ae274, 0xaa54f2ba, 0x5f0a0e77, 0xaa4247e1, 0x5ef936d1, 0xaa2fa056,
  0x5ee85b82, 0xaa1cfc1a,
  0x5ed77c8a, 0xaa0a5b2e, 0x5ec699e9, 0xa9f7bd92, 0x5eb5b3a2, 0xa9e52347,
  0x5ea4c9b3, 0xa9d28c4e,
  0x5e93dc1f, 0xa9bff8a8, 0x5e82eae5, 0xa9ad6855, 0x5e71f606, 0xa99adb56,
  0x5e60fd84, 0xa98851ac,
  0x5e50015d, 0xa975cb57, 0x5e3f0194, 0xa9634858, 0x5e2dfe29, 0xa950c8b0,
  0x5e1cf71c, 0xa93e4c5f,
  0x5e0bec6e, 0xa92bd367, 0x5dfade20, 0xa9195dc7, 0x5de9cc33, 0xa906eb82,
  0x5dd8b6a7, 0xa8f47c97,
  0x5dc79d7c, 0xa8e21106, 0x5db680b4, 0xa8cfa8d2, 0x5da5604f, 0xa8bd43fa,
  0x5d943c4e, 0xa8aae280,
  0x5d8314b1, 0xa8988463, 0x5d71e979, 0xa88629a5, 0x5d60baa7, 0xa873d246,
  0x5d4f883b, 0xa8617e48,
  0x5d3e5237, 0xa84f2daa, 0x5d2d189a, 0xa83ce06e, 0x5d1bdb65, 0xa82a9693,
  0x5d0a9a9a, 0xa818501c,
  0x5cf95638, 0xa8060d08, 0x5ce80e41, 0xa7f3cd59, 0x5cd6c2b5, 0xa7e1910f,
  0x5cc57394, 0xa7cf582a,
  0x5cb420e0, 0xa7bd22ac, 0x5ca2ca99, 0xa7aaf094, 0x5c9170bf, 0xa798c1e5,
  0x5c801354, 0xa786969e,
  0x5c6eb258, 0xa7746ec0, 0x5c5d4dcc, 0xa7624a4d, 0x5c4be5b0, 0xa7502943,
  0x5c3a7a05, 0xa73e0ba5,
  0x5c290acc, 0xa72bf174, 0x5c179806, 0xa719daae, 0x5c0621b2, 0xa707c757,
  0x5bf4a7d2, 0xa6f5b76d,
  0x5be32a67, 0xa6e3aaf2, 0x5bd1a971, 0xa6d1a1e7, 0x5bc024f0, 0xa6bf9c4b,
  0x5bae9ce7, 0xa6ad9a21,
  0x5b9d1154, 0xa69b9b68, 0x5b8b8239, 0xa689a022, 0x5b79ef96, 0xa677a84e,
  0x5b68596d, 0xa665b3ee,
  0x5b56bfbd, 0xa653c303, 0x5b452288, 0xa641d58c, 0x5b3381ce, 0xa62feb8b,
  0x5b21dd90, 0xa61e0501,
  0x5b1035cf, 0xa60c21ee, 0x5afe8a8b, 0xa5fa4252, 0x5aecdbc5, 0xa5e8662f,
  0x5adb297d, 0xa5d68d85,
  0x5ac973b5, 0xa5c4b855, 0x5ab7ba6c, 0xa5b2e6a0, 0x5aa5fda5, 0xa5a11866,
  0x5a943d5e, 0xa58f4da8,
  0x5a82799a, 0xa57d8666, 0x5a70b258, 0xa56bc2a2, 0x5a5ee79a, 0xa55a025b,
  0x5a4d1960, 0xa5484594,
  0x5a3b47ab, 0xa5368c4b, 0x5a29727b, 0xa524d683, 0x5a1799d1, 0xa513243b,
  0x5a05bdae, 0xa5017575,
  0x59f3de12, 0xa4efca31, 0x59e1faff, 0xa4de2270, 0x59d01475, 0xa4cc7e32,
  0x59be2a74, 0xa4badd78,
  0x59ac3cfd, 0xa4a94043, 0x599a4c12, 0xa497a693, 0x598857b2, 0xa486106a,
  0x59765fde, 0xa4747dc7,
  0x59646498, 0xa462eeac, 0x595265df, 0xa4516319, 0x594063b5, 0xa43fdb10,
  0x592e5e19, 0xa42e568f,
  0x591c550e, 0xa41cd599, 0x590a4893, 0xa40b582e, 0x58f838a9, 0xa3f9de4e,
  0x58e62552, 0xa3e867fa,
  0x58d40e8c, 0xa3d6f534, 0x58c1f45b, 0xa3c585fb, 0x58afd6bd, 0xa3b41a50,
  0x589db5b3, 0xa3a2b234,
  0x588b9140, 0xa3914da8, 0x58796962, 0xa37fecac, 0x58673e1b, 0xa36e8f41,
  0x58550f6c, 0xa35d3567,
  0x5842dd54, 0xa34bdf20, 0x5830a7d6, 0xa33a8c6c, 0x581e6ef1, 0xa3293d4b,
  0x580c32a7, 0xa317f1bf,
  0x57f9f2f8, 0xa306a9c8, 0x57e7afe4, 0xa2f56566, 0x57d5696d, 0xa2e4249b,
  0x57c31f92, 0xa2d2e766,
  0x57b0d256, 0xa2c1adc9, 0x579e81b8, 0xa2b077c5, 0x578c2dba, 0xa29f4559,
  0x5779d65b, 0xa28e1687,
  0x57677b9d, 0xa27ceb4f, 0x57551d80, 0xa26bc3b2, 0x5742bc06, 0xa25a9fb1,
  0x5730572e, 0xa2497f4c,
  0x571deefa, 0xa2386284, 0x570b8369, 0xa2274959, 0x56f9147e, 0xa21633cd,
  0x56e6a239, 0xa20521e0,
  0x56d42c99, 0xa1f41392, 0x56c1b3a1, 0xa1e308e4, 0x56af3750, 0xa1d201d7,
  0x569cb7a8, 0xa1c0fe6c,
  0x568a34a9, 0xa1affea3, 0x5677ae54, 0xa19f027c, 0x566524aa, 0xa18e09fa,
  0x565297ab, 0xa17d151b,
  0x56400758, 0xa16c23e1, 0x562d73b2, 0xa15b364d, 0x561adcb9, 0xa14a4c5e,
  0x5608426e, 0xa1396617,
  0x55f5a4d2, 0xa1288376, 0x55e303e6, 0xa117a47e, 0x55d05faa, 0xa106c92f,
  0x55bdb81f, 0xa0f5f189,
  0x55ab0d46, 0xa0e51d8c, 0x55985f20, 0xa0d44d3b, 0x5585adad, 0xa0c38095,
  0x5572f8ed, 0xa0b2b79b,
  0x556040e2, 0xa0a1f24d, 0x554d858d, 0xa09130ad, 0x553ac6ee, 0xa08072ba,
  0x55280505, 0xa06fb876,
  0x55153fd4, 0xa05f01e1, 0x5502775c, 0xa04e4efc, 0x54efab9c, 0xa03d9fc8,
  0x54dcdc96, 0xa02cf444,
  0x54ca0a4b, 0xa01c4c73, 0x54b734ba, 0xa00ba853, 0x54a45be6, 0x9ffb07e7,
  0x54917fce, 0x9fea6b2f,
  0x547ea073, 0x9fd9d22a, 0x546bbdd7, 0x9fc93cdb, 0x5458d7f9, 0x9fb8ab41,
  0x5445eedb, 0x9fa81d5e,
  0x5433027d, 0x9f979331, 0x542012e1, 0x9f870cbc, 0x540d2005, 0x9f7689ff,
  0x53fa29ed, 0x9f660afb,
  0x53e73097, 0x9f558fb0, 0x53d43406, 0x9f45181f, 0x53c13439, 0x9f34a449,
  0x53ae3131, 0x9f24342f,
  0x539b2af0, 0x9f13c7d0, 0x53882175, 0x9f035f2e, 0x537514c2, 0x9ef2fa49,
  0x536204d7, 0x9ee29922,
  0x534ef1b5, 0x9ed23bb9, 0x533bdb5d, 0x9ec1e210, 0x5328c1d0, 0x9eb18c26,
  0x5315a50e, 0x9ea139fd,
  0x53028518, 0x9e90eb94, 0x52ef61ee, 0x9e80a0ee, 0x52dc3b92, 0x9e705a09,
  0x52c91204, 0x9e6016e8,
  0x52b5e546, 0x9e4fd78a, 0x52a2b556, 0x9e3f9bf0, 0x528f8238, 0x9e2f641b,
  0x527c4bea, 0x9e1f300b,
  0x5269126e, 0x9e0effc1, 0x5255d5c5, 0x9dfed33e, 0x524295f0, 0x9deeaa82,
  0x522f52ee, 0x9dde858e,
  0x521c0cc2, 0x9dce6463, 0x5208c36a, 0x9dbe4701, 0x51f576ea, 0x9dae2d68,
  0x51e22740, 0x9d9e179a,
  0x51ced46e, 0x9d8e0597, 0x51bb7e75, 0x9d7df75f, 0x51a82555, 0x9d6decf4,
  0x5194c910, 0x9d5de656,
  0x518169a5, 0x9d4de385, 0x516e0715, 0x9d3de482, 0x515aa162, 0x9d2de94d,
  0x5147388c, 0x9d1df1e9,
  0x5133cc94, 0x9d0dfe54, 0x51205d7b, 0x9cfe0e8f, 0x510ceb40, 0x9cee229c,
  0x50f975e6, 0x9cde3a7b,
  0x50e5fd6d, 0x9cce562c, 0x50d281d5, 0x9cbe75b0, 0x50bf031f, 0x9cae9907,
  0x50ab814d, 0x9c9ec033,
  0x5097fc5e, 0x9c8eeb34, 0x50847454, 0x9c7f1a0a, 0x5070e92f, 0x9c6f4cb6,
  0x505d5af1, 0x9c5f8339,
  0x5049c999, 0x9c4fbd93, 0x50363529, 0x9c3ffbc5, 0x50229da1, 0x9c303dcf,
  0x500f0302, 0x9c2083b3,
  0x4ffb654d, 0x9c10cd70, 0x4fe7c483, 0x9c011b08, 0x4fd420a4, 0x9bf16c7a,
  0x4fc079b1, 0x9be1c1c8,
  0x4faccfab, 0x9bd21af3, 0x4f992293, 0x9bc277fa, 0x4f857269, 0x9bb2d8de,
  0x4f71bf2e, 0x9ba33da0,
  0x4f5e08e3, 0x9b93a641, 0x4f4a4f89, 0x9b8412c1, 0x4f369320, 0x9b748320,
  0x4f22d3aa, 0x9b64f760,
  0x4f0f1126, 0x9b556f81, 0x4efb4b96, 0x9b45eb83, 0x4ee782fb, 0x9b366b68,
  0x4ed3b755, 0x9b26ef2f,
  0x4ebfe8a5, 0x9b1776da, 0x4eac16eb, 0x9b080268, 0x4e984229, 0x9af891db,
  0x4e846a60, 0x9ae92533,
  0x4e708f8f, 0x9ad9bc71, 0x4e5cb1b9, 0x9aca5795, 0x4e48d0dd, 0x9abaf6a1,
  0x4e34ecfc, 0x9aab9993,
  0x4e210617, 0x9a9c406e, 0x4e0d1c30, 0x9a8ceb31, 0x4df92f46, 0x9a7d99de,
  0x4de53f5a, 0x9a6e4c74,
  0x4dd14c6e, 0x9a5f02f5, 0x4dbd5682, 0x9a4fbd61, 0x4da95d96, 0x9a407bb9,
  0x4d9561ac, 0x9a313dfc,
  0x4d8162c4, 0x9a22042d, 0x4d6d60df, 0x9a12ce4b, 0x4d595bfe, 0x9a039c57,
  0x4d455422, 0x99f46e51,
  0x4d31494b, 0x99e5443b, 0x4d1d3b7a, 0x99d61e14, 0x4d092ab0, 0x99c6fbde,
  0x4cf516ee, 0x99b7dd99,
  0x4ce10034, 0x99a8c345, 0x4ccce684, 0x9999ace3, 0x4cb8c9dd, 0x998a9a74,
  0x4ca4aa41, 0x997b8bf8,
  0x4c9087b1, 0x996c816f, 0x4c7c622d, 0x995d7adc, 0x4c6839b7, 0x994e783d,
  0x4c540e4e, 0x993f7993,
  0x4c3fdff4, 0x99307ee0, 0x4c2baea9, 0x99218824, 0x4c177a6e, 0x9912955f,
  0x4c034345, 0x9903a691,
  0x4bef092d, 0x98f4bbbc, 0x4bdacc28, 0x98e5d4e0, 0x4bc68c36, 0x98d6f1fe,
  0x4bb24958, 0x98c81316,
  0x4b9e0390, 0x98b93828, 0x4b89badd, 0x98aa6136, 0x4b756f40, 0x989b8e40,
  0x4b6120bb, 0x988cbf46,
  0x4b4ccf4d, 0x987df449, 0x4b387af9, 0x986f2d4a, 0x4b2423be, 0x98606a49,
  0x4b0fc99d, 0x9851ab46,
  0x4afb6c98, 0x9842f043, 0x4ae70caf, 0x98343940, 0x4ad2a9e2, 0x9825863d,
  0x4abe4433, 0x9816d73b,
  0x4aa9dba2, 0x98082c3b, 0x4a957030, 0x97f9853d, 0x4a8101de, 0x97eae242,
  0x4a6c90ad, 0x97dc4349,
  0x4a581c9e, 0x97cda855, 0x4a43a5b0, 0x97bf1165, 0x4a2f2be6, 0x97b07e7a,
  0x4a1aaf3f, 0x97a1ef94,
  0x4a062fbd, 0x979364b5, 0x49f1ad61, 0x9784dddc, 0x49dd282a, 0x97765b0a,
  0x49c8a01b, 0x9767dc41,
  0x49b41533, 0x9759617f, 0x499f8774, 0x974aeac6, 0x498af6df, 0x973c7817,
  0x49766373, 0x972e0971,
  0x4961cd33, 0x971f9ed7, 0x494d341e, 0x97113847, 0x49389836, 0x9702d5c3,
  0x4923f97b, 0x96f4774b,
  0x490f57ee, 0x96e61ce0, 0x48fab391, 0x96d7c682, 0x48e60c62, 0x96c97432,
  0x48d16265, 0x96bb25f0,
  0x48bcb599, 0x96acdbbe, 0x48a805ff, 0x969e959b, 0x48935397, 0x96905388,
  0x487e9e64, 0x96821585,
  0x4869e665, 0x9673db94, 0x48552b9b, 0x9665a5b4, 0x48406e08, 0x965773e7,
  0x482badab, 0x9649462d,
  0x4816ea86, 0x963b1c86, 0x48022499, 0x962cf6f2, 0x47ed5be6, 0x961ed574,
  0x47d8906d, 0x9610b80a,
  0x47c3c22f, 0x96029eb6, 0x47aef12c, 0x95f48977, 0x479a1d67, 0x95e67850,
  0x478546de, 0x95d86b3f,
  0x47706d93, 0x95ca6247, 0x475b9188, 0x95bc5d66, 0x4746b2bc, 0x95ae5c9f,
  0x4731d131, 0x95a05ff0,
  0x471cece7, 0x9592675c, 0x470805df, 0x958472e2, 0x46f31c1a, 0x95768283,
  0x46de2f99, 0x9568963f,
  0x46c9405c, 0x955aae17, 0x46b44e65, 0x954cca0c, 0x469f59b4, 0x953eea1e,
  0x468a624a, 0x95310e4e,
  0x46756828, 0x9523369c, 0x46606b4e, 0x95156308, 0x464b6bbe, 0x95079394,
  0x46366978, 0x94f9c83f,
  0x4621647d, 0x94ec010b, 0x460c5cce, 0x94de3df8, 0x45f7526b, 0x94d07f05,
  0x45e24556, 0x94c2c435,
  0x45cd358f, 0x94b50d87, 0x45b82318, 0x94a75afd, 0x45a30df0, 0x9499ac95,
  0x458df619, 0x948c0252,
  0x4578db93, 0x947e5c33, 0x4563be60, 0x9470ba39, 0x454e9e80, 0x94631c65,
  0x45397bf4, 0x945582b7,
  0x452456bd, 0x9447ed2f, 0x450f2edb, 0x943a5bcf, 0x44fa0450, 0x942cce96,
  0x44e4d71c, 0x941f4585,
  0x44cfa740, 0x9411c09e, 0x44ba74bd, 0x94043fdf, 0x44a53f93, 0x93f6c34a,
  0x449007c4, 0x93e94adf,
  0x447acd50, 0x93dbd6a0, 0x44659039, 0x93ce668b, 0x4450507e, 0x93c0faa3,
  0x443b0e21, 0x93b392e6,
  0x4425c923, 0x93a62f57, 0x44108184, 0x9398cff5, 0x43fb3746, 0x938b74c1,
  0x43e5ea68, 0x937e1dbb,
  0x43d09aed, 0x9370cae4, 0x43bb48d4, 0x93637c3d, 0x43a5f41e, 0x935631c5,
  0x43909ccd, 0x9348eb7e,
  0x437b42e1, 0x933ba968, 0x4365e65b, 0x932e6b84, 0x4350873c, 0x932131d1,
  0x433b2585, 0x9313fc51,
  0x4325c135, 0x9306cb04, 0x43105a50, 0x92f99deb, 0x42faf0d4, 0x92ec7505,
  0x42e584c3, 0x92df5054,
  0x42d0161e, 0x92d22fd9, 0x42baa4e6, 0x92c51392, 0x42a5311b, 0x92b7fb82,
  0x428fbabe, 0x92aae7a8,
  0x427a41d0, 0x929dd806, 0x4264c653, 0x9290cc9b, 0x424f4845, 0x9283c568,
  0x4239c7aa, 0x9276c26d,
  0x42244481, 0x9269c3ac, 0x420ebecb, 0x925cc924, 0x41f93689, 0x924fd2d7,
  0x41e3abbc, 0x9242e0c4,
  0x41ce1e65, 0x9235f2ec, 0x41b88e84, 0x9229094f, 0x41a2fc1a, 0x921c23ef,
  0x418d6729, 0x920f42cb,
  0x4177cfb1, 0x920265e4, 0x416235b2, 0x91f58d3b, 0x414c992f, 0x91e8b8d0,
  0x4136fa27, 0x91dbe8a4,
  0x4121589b, 0x91cf1cb6, 0x410bb48c, 0x91c25508, 0x40f60dfb, 0x91b5919a,
  0x40e064ea, 0x91a8d26d,
  0x40cab958, 0x919c1781, 0x40b50b46, 0x918f60d6, 0x409f5ab6, 0x9182ae6d,
  0x4089a7a8, 0x91760047,
  0x4073f21d, 0x91695663, 0x405e3a16, 0x915cb0c3, 0x40487f94, 0x91500f67,
  0x4032c297, 0x91437250,
  0x401d0321, 0x9136d97d, 0x40074132, 0x912a44f0, 0x3ff17cca, 0x911db4a9,
  0x3fdbb5ec, 0x911128a8,
  0x3fc5ec98, 0x9104a0ee, 0x3fb020ce, 0x90f81d7b, 0x3f9a5290, 0x90eb9e50,
  0x3f8481dd, 0x90df236e,
  0x3f6eaeb8, 0x90d2acd4, 0x3f58d921, 0x90c63a83, 0x3f430119, 0x90b9cc7d,
  0x3f2d26a0, 0x90ad62c0,
  0x3f1749b8, 0x90a0fd4e, 0x3f016a61, 0x90949c28, 0x3eeb889c, 0x90883f4d,
  0x3ed5a46b, 0x907be6be,
  0x3ebfbdcd, 0x906f927c, 0x3ea9d4c3, 0x90634287, 0x3e93e950, 0x9056f6df,
  0x3e7dfb73, 0x904aaf86,
  0x3e680b2c, 0x903e6c7b, 0x3e52187f, 0x90322dbf, 0x3e3c2369, 0x9025f352,
  0x3e262bee, 0x9019bd36,
  0x3e10320d, 0x900d8b69, 0x3dfa35c8, 0x90015dee, 0x3de4371f, 0x8ff534c4,
  0x3dce3614, 0x8fe90fec,
  0x3db832a6, 0x8fdcef66, 0x3da22cd7, 0x8fd0d333, 0x3d8c24a8, 0x8fc4bb53,
  0x3d761a19, 0x8fb8a7c7,
  0x3d600d2c, 0x8fac988f, 0x3d49fde1, 0x8fa08dab, 0x3d33ec39, 0x8f94871d,
  0x3d1dd835, 0x8f8884e4,
  0x3d07c1d6, 0x8f7c8701, 0x3cf1a91c, 0x8f708d75, 0x3cdb8e09, 0x8f649840,
  0x3cc5709e, 0x8f58a761,
  0x3caf50da, 0x8f4cbadb, 0x3c992ec0, 0x8f40d2ad, 0x3c830a50, 0x8f34eed8,
  0x3c6ce38a, 0x8f290f5c,
  0x3c56ba70, 0x8f1d343a, 0x3c408f03, 0x8f115d72, 0x3c2a6142, 0x8f058b04,
  0x3c143130, 0x8ef9bcf2,
  0x3bfdfecd, 0x8eedf33b, 0x3be7ca1a, 0x8ee22de0, 0x3bd19318, 0x8ed66ce1,
  0x3bbb59c7, 0x8ecab040,
  0x3ba51e29, 0x8ebef7fb, 0x3b8ee03e, 0x8eb34415, 0x3b78a007, 0x8ea7948c,
  0x3b625d86, 0x8e9be963,
  0x3b4c18ba, 0x8e904298, 0x3b35d1a5, 0x8e84a02d, 0x3b1f8848, 0x8e790222,
  0x3b093ca3, 0x8e6d6877,
  0x3af2eeb7, 0x8e61d32e, 0x3adc9e86, 0x8e564246, 0x3ac64c0f, 0x8e4ab5bf,
  0x3aaff755, 0x8e3f2d9b,
  0x3a99a057, 0x8e33a9da, 0x3a834717, 0x8e282a7b, 0x3a6ceb96, 0x8e1caf80,
  0x3a568dd4, 0x8e1138ea,
  0x3a402dd2, 0x8e05c6b7, 0x3a29cb91, 0x8dfa58ea, 0x3a136712, 0x8deeef82,
  0x39fd0056, 0x8de38a80,
  0x39e6975e, 0x8dd829e4, 0x39d02c2a, 0x8dcccdaf, 0x39b9bebc, 0x8dc175e0,
  0x39a34f13, 0x8db6227a,
  0x398cdd32, 0x8daad37b, 0x39766919, 0x8d9f88e5, 0x395ff2c9, 0x8d9442b8,
  0x39497a43, 0x8d8900f3,
  0x3932ff87, 0x8d7dc399, 0x391c8297, 0x8d728aa9, 0x39060373, 0x8d675623,
  0x38ef821c, 0x8d5c2609,
  0x38d8fe93, 0x8d50fa59, 0x38c278d9, 0x8d45d316, 0x38abf0ef, 0x8d3ab03f,
  0x389566d6, 0x8d2f91d5,
  0x387eda8e, 0x8d2477d8, 0x38684c19, 0x8d196249, 0x3851bb77, 0x8d0e5127,
  0x383b28a9, 0x8d034474,
  0x382493b0, 0x8cf83c30, 0x380dfc8d, 0x8ced385b, 0x37f76341, 0x8ce238f6,
  0x37e0c7cc, 0x8cd73e01,
  0x37ca2a30, 0x8ccc477d, 0x37b38a6d, 0x8cc1556a, 0x379ce885, 0x8cb667c8,
  0x37864477, 0x8cab7e98,
  0x376f9e46, 0x8ca099da, 0x3758f5f2, 0x8c95b98f, 0x37424b7b, 0x8c8addb7,
  0x372b9ee3, 0x8c800652,
  0x3714f02a, 0x8c753362, 0x36fe3f52, 0x8c6a64e5, 0x36e78c5b, 0x8c5f9ade,
  0x36d0d746, 0x8c54d54c,
  0x36ba2014, 0x8c4a142f, 0x36a366c6, 0x8c3f5788, 0x368cab5c, 0x8c349f58,
  0x3675edd9, 0x8c29eb9f,
  0x365f2e3b, 0x8c1f3c5d, 0x36486c86, 0x8c149192, 0x3631a8b8, 0x8c09eb40,
  0x361ae2d3, 0x8bff4966,
  0x36041ad9, 0x8bf4ac05, 0x35ed50c9, 0x8bea131e, 0x35d684a6, 0x8bdf7eb0,
  0x35bfb66e, 0x8bd4eebc,
  0x35a8e625, 0x8bca6343, 0x359213c9, 0x8bbfdc44, 0x357b3f5d, 0x8bb559c1,
  0x356468e2, 0x8baadbba,
  0x354d9057, 0x8ba0622f, 0x3536b5be, 0x8b95ed21, 0x351fd918, 0x8b8b7c8f,
  0x3508fa66, 0x8b81107b,
  0x34f219a8, 0x8b76a8e4, 0x34db36df, 0x8b6c45cc, 0x34c4520d, 0x8b61e733,
  0x34ad6b32, 0x8b578d18,
  0x34968250, 0x8b4d377c, 0x347f9766, 0x8b42e661, 0x3468aa76, 0x8b3899c6,
  0x3451bb81, 0x8b2e51ab,
  0x343aca87, 0x8b240e11, 0x3423d78a, 0x8b19cef8, 0x340ce28b, 0x8b0f9462,
  0x33f5eb89, 0x8b055e4d,
  0x33def287, 0x8afb2cbb, 0x33c7f785, 0x8af0ffac, 0x33b0fa84, 0x8ae6d720,
  0x3399fb85, 0x8adcb318,
  0x3382fa88, 0x8ad29394, 0x336bf78f, 0x8ac87894, 0x3354f29b, 0x8abe6219,
  0x333debab, 0x8ab45024,
  0x3326e2c3, 0x8aaa42b4, 0x330fd7e1, 0x8aa039cb, 0x32f8cb07, 0x8a963567,
  0x32e1bc36, 0x8a8c358b,
  0x32caab6f, 0x8a823a36, 0x32b398b3, 0x8a784368, 0x329c8402, 0x8a6e5123,
  0x32856d5e, 0x8a646365,
  0x326e54c7, 0x8a5a7a31, 0x32573a3f, 0x8a509585, 0x32401dc6, 0x8a46b564,
  0x3228ff5c, 0x8a3cd9cc,
  0x3211df04, 0x8a3302be, 0x31fabcbd, 0x8a29303b, 0x31e39889, 0x8a1f6243,
  0x31cc7269, 0x8a1598d6,
  0x31b54a5e, 0x8a0bd3f5, 0x319e2067, 0x8a0213a0, 0x3186f487, 0x89f857d8,
  0x316fc6be, 0x89eea09d,
  0x3158970e, 0x89e4edef, 0x31416576, 0x89db3fcf, 0x312a31f8, 0x89d1963c,
  0x3112fc95, 0x89c7f138,
  0x30fbc54d, 0x89be50c3, 0x30e48c22, 0x89b4b4dd, 0x30cd5115, 0x89ab1d87,
  0x30b61426, 0x89a18ac0,
  0x309ed556, 0x8997fc8a, 0x308794a6, 0x898e72e4, 0x30705217, 0x8984edcf,
  0x30590dab, 0x897b6d4c,
  0x3041c761, 0x8971f15a, 0x302a7f3a, 0x896879fb, 0x30133539, 0x895f072e,
  0x2ffbe95d, 0x895598f3,
  0x2fe49ba7, 0x894c2f4c, 0x2fcd4c19, 0x8942ca39, 0x2fb5fab2, 0x893969b9,
  0x2f9ea775, 0x89300dce,
  0x2f875262, 0x8926b677, 0x2f6ffb7a, 0x891d63b5, 0x2f58a2be, 0x89141589,
  0x2f41482e, 0x890acbf2,
  0x2f29ebcc, 0x890186f2, 0x2f128d99, 0x88f84687, 0x2efb2d95, 0x88ef0ab4,
  0x2ee3cbc1, 0x88e5d378,
  0x2ecc681e, 0x88dca0d3, 0x2eb502ae, 0x88d372c6, 0x2e9d9b70, 0x88ca4951,
  0x2e863267, 0x88c12475,
  0x2e6ec792, 0x88b80432, 0x2e575af3, 0x88aee888, 0x2e3fec8b, 0x88a5d177,
  0x2e287c5a, 0x889cbf01,
  0x2e110a62, 0x8893b125, 0x2df996a3, 0x888aa7e3, 0x2de2211e, 0x8881a33d,
  0x2dcaa9d5, 0x8878a332,
  0x2db330c7, 0x886fa7c2, 0x2d9bb5f6, 0x8866b0ef, 0x2d843964, 0x885dbeb8,
  0x2d6cbb10, 0x8854d11e,
  0x2d553afc, 0x884be821, 0x2d3db928, 0x884303c1, 0x2d263596, 0x883a23ff,
  0x2d0eb046, 0x883148db,
  0x2cf72939, 0x88287256, 0x2cdfa071, 0x881fa06f, 0x2cc815ee, 0x8816d327,
  0x2cb089b1, 0x880e0a7f,
  0x2c98fbba, 0x88054677, 0x2c816c0c, 0x87fc870f, 0x2c69daa6, 0x87f3cc48,
  0x2c52478a, 0x87eb1621,
  0x2c3ab2b9, 0x87e2649b, 0x2c231c33, 0x87d9b7b7, 0x2c0b83fa, 0x87d10f75,
  0x2bf3ea0d, 0x87c86bd5,
  0x2bdc4e6f, 0x87bfccd7, 0x2bc4b120, 0x87b7327d, 0x2bad1221, 0x87ae9cc5,
  0x2b957173, 0x87a60bb1,
  0x2b7dcf17, 0x879d7f41, 0x2b662b0e, 0x8794f774, 0x2b4e8558, 0x878c744d,
  0x2b36ddf7, 0x8783f5ca,
  0x2b1f34eb, 0x877b7bec, 0x2b078a36, 0x877306b4, 0x2aefddd8, 0x876a9621,
  0x2ad82fd2, 0x87622a35,
  0x2ac08026, 0x8759c2ef, 0x2aa8ced3, 0x87516050, 0x2a911bdc, 0x87490258,
  0x2a796740, 0x8740a907,
  0x2a61b101, 0x8738545e, 0x2a49f920, 0x8730045d, 0x2a323f9e, 0x8727b905,
  0x2a1a847b, 0x871f7255,
  0x2a02c7b8, 0x8717304e, 0x29eb0957, 0x870ef2f1, 0x29d34958, 0x8706ba3d,
  0x29bb87bc, 0x86fe8633,
  0x29a3c485, 0x86f656d3, 0x298bffb2, 0x86ee2c1e, 0x29743946, 0x86e60614,
  0x295c7140, 0x86dde4b5,
  0x2944a7a2, 0x86d5c802, 0x292cdc6d, 0x86cdaffa, 0x29150fa1, 0x86c59c9f,
  0x28fd4140, 0x86bd8df0,
  0x28e5714b, 0x86b583ee, 0x28cd9fc1, 0x86ad7e99, 0x28b5cca5, 0x86a57df2,
  0x289df7f8, 0x869d81f8,
  0x288621b9, 0x86958aac, 0x286e49ea, 0x868d980e, 0x2856708d, 0x8685aa20,
  0x283e95a1, 0x867dc0e0,
  0x2826b928, 0x8675dc4f, 0x280edb23, 0x866dfc6e, 0x27f6fb92, 0x8666213c,
  0x27df1a77, 0x865e4abb,
  0x27c737d3, 0x865678eb, 0x27af53a6, 0x864eabcb, 0x27976df1, 0x8646e35c,
  0x277f86b5, 0x863f1f9e,
  0x27679df4, 0x86376092, 0x274fb3ae, 0x862fa638, 0x2737c7e3, 0x8627f091,
  0x271fda96, 0x86203f9c,
  0x2707ebc7, 0x86189359, 0x26effb76, 0x8610ebca, 0x26d809a5, 0x860948ef,
  0x26c01655, 0x8601aac7,
  0x26a82186, 0x85fa1153, 0x26902b39, 0x85f27c93, 0x26783370, 0x85eaec88,
  0x26603a2c, 0x85e36132,
  0x26483f6c, 0x85dbda91, 0x26304333, 0x85d458a6, 0x26184581, 0x85ccdb70,
  0x26004657, 0x85c562f1,
  0x25e845b6, 0x85bdef28, 0x25d0439f, 0x85b68015, 0x25b84012, 0x85af15b9,
  0x25a03b11, 0x85a7b015,
  0x2588349d, 0x85a04f28, 0x25702cb7, 0x8598f2f3, 0x2558235f, 0x85919b76,
  0x25401896, 0x858a48b1,
  0x25280c5e, 0x8582faa5, 0x250ffeb7, 0x857bb152, 0x24f7efa2, 0x85746cb8,
  0x24dfdf20, 0x856d2cd7,
  0x24c7cd33, 0x8565f1b0, 0x24afb9da, 0x855ebb44, 0x2497a517, 0x85578991,
  0x247f8eec, 0x85505c99,
  0x24677758, 0x8549345c, 0x244f5e5c, 0x854210db, 0x243743fa, 0x853af214,
  0x241f2833, 0x8533d809,
  0x24070b08, 0x852cc2bb, 0x23eeec78, 0x8525b228, 0x23d6cc87, 0x851ea652,
  0x23beab33, 0x85179f39,
  0x23a6887f, 0x85109cdd, 0x238e646a, 0x85099f3e, 0x23763ef7, 0x8502a65c,
  0x235e1826, 0x84fbb239,
  0x2345eff8, 0x84f4c2d4, 0x232dc66d, 0x84edd82d, 0x23159b88, 0x84e6f244,
  0x22fd6f48, 0x84e0111b,
  0x22e541af, 0x84d934b1, 0x22cd12bd, 0x84d25d06, 0x22b4e274, 0x84cb8a1b,
  0x229cb0d5, 0x84c4bbf0,
  0x22847de0, 0x84bdf286, 0x226c4996, 0x84b72ddb, 0x225413f8, 0x84b06df2,
  0x223bdd08, 0x84a9b2ca,
  0x2223a4c5, 0x84a2fc62, 0x220b6b32, 0x849c4abd, 0x21f3304f, 0x84959dd9,
  0x21daf41d, 0x848ef5b7,
  0x21c2b69c, 0x84885258, 0x21aa77cf, 0x8481b3bb, 0x219237b5, 0x847b19e1,
  0x2179f64f, 0x847484ca,
  0x2161b3a0, 0x846df477, 0x21496fa7, 0x846768e7, 0x21312a65, 0x8460e21a,
  0x2118e3dc, 0x845a6012,
  0x21009c0c, 0x8453e2cf, 0x20e852f6, 0x844d6a50, 0x20d0089c, 0x8446f695,
  0x20b7bcfe, 0x844087a0,
  0x209f701c, 0x843a1d70, 0x208721f9, 0x8433b806, 0x206ed295, 0x842d5762,
  0x205681f1, 0x8426fb84,
  0x203e300d, 0x8420a46c, 0x2025dcec, 0x841a521a, 0x200d888d, 0x84140490,
  0x1ff532f2, 0x840dbbcc,
  0x1fdcdc1b, 0x840777d0, 0x1fc4840a, 0x8401389b, 0x1fac2abf, 0x83fafe2e,
  0x1f93d03c, 0x83f4c889,
  0x1f7b7481, 0x83ee97ad, 0x1f63178f, 0x83e86b99, 0x1f4ab968, 0x83e2444d,
  0x1f325a0b, 0x83dc21cb,
  0x1f19f97b, 0x83d60412, 0x1f0197b8, 0x83cfeb22, 0x1ee934c3, 0x83c9d6fc,
  0x1ed0d09d, 0x83c3c7a0,
  0x1eb86b46, 0x83bdbd0e, 0x1ea004c1, 0x83b7b746, 0x1e879d0d, 0x83b1b649,
  0x1e6f342c, 0x83abba17,
  0x1e56ca1e, 0x83a5c2b0, 0x1e3e5ee5, 0x839fd014, 0x1e25f282, 0x8399e244,
  0x1e0d84f5, 0x8393f940,
  0x1df5163f, 0x838e1507, 0x1ddca662, 0x8388359b, 0x1dc4355e, 0x83825afb,
  0x1dabc334, 0x837c8528,
  0x1d934fe5, 0x8376b422, 0x1d7adb73, 0x8370e7e9, 0x1d6265dd, 0x836b207d,
  0x1d49ef26, 0x83655ddf,
  0x1d31774d, 0x835fa00f, 0x1d18fe54, 0x8359e70d, 0x1d00843d, 0x835432d8,
  0x1ce80906, 0x834e8373,
  0x1ccf8cb3, 0x8348d8dc, 0x1cb70f43, 0x83433314, 0x1c9e90b8, 0x833d921b,
  0x1c861113, 0x8337f5f1,
  0x1c6d9053, 0x83325e97, 0x1c550e7c, 0x832ccc0d, 0x1c3c8b8c, 0x83273e52,
  0x1c240786, 0x8321b568,
  0x1c0b826a, 0x831c314e, 0x1bf2fc3a, 0x8316b205, 0x1bda74f6, 0x8311378d,
  0x1bc1ec9e, 0x830bc1e6,
  0x1ba96335, 0x83065110, 0x1b90d8bb, 0x8300e50b, 0x1b784d30, 0x82fb7dd8,
  0x1b5fc097, 0x82f61b77,
  0x1b4732ef, 0x82f0bde8, 0x1b2ea43a, 0x82eb652b, 0x1b161479, 0x82e61141,
  0x1afd83ad, 0x82e0c22a,
  0x1ae4f1d6, 0x82db77e5, 0x1acc5ef6, 0x82d63274, 0x1ab3cb0d, 0x82d0f1d5,
  0x1a9b361d, 0x82cbb60b,
  0x1a82a026, 0x82c67f14, 0x1a6a0929, 0x82c14cf1, 0x1a517128, 0x82bc1fa2,
  0x1a38d823, 0x82b6f727,
  0x1a203e1b, 0x82b1d381, 0x1a07a311, 0x82acb4b0, 0x19ef0707, 0x82a79ab3,
  0x19d669fc, 0x82a2858c,
  0x19bdcbf3, 0x829d753a, 0x19a52ceb, 0x829869be, 0x198c8ce7, 0x82936317,
  0x1973ebe6, 0x828e6146,
  0x195b49ea, 0x8289644b, 0x1942a6f3, 0x82846c26, 0x192a0304, 0x827f78d8,
  0x19115e1c, 0x827a8a61,
  0x18f8b83c, 0x8275a0c0, 0x18e01167, 0x8270bbf7, 0x18c7699b, 0x826bdc04,
  0x18aec0db, 0x826700e9,
  0x18961728, 0x82622aa6, 0x187d6c82, 0x825d593a, 0x1864c0ea, 0x82588ca7,
  0x184c1461, 0x8253c4eb,
  0x183366e9, 0x824f0208, 0x181ab881, 0x824a43fe, 0x1802092c, 0x82458acc,
  0x17e958ea, 0x8240d673,
  0x17d0a7bc, 0x823c26f3, 0x17b7f5a3, 0x82377c4c, 0x179f429f, 0x8232d67f,
  0x17868eb3, 0x822e358b,
  0x176dd9de, 0x82299971, 0x17552422, 0x82250232, 0x173c6d80, 0x82206fcc,
  0x1723b5f9, 0x821be240,
  0x170afd8d, 0x82175990, 0x16f2443e, 0x8212d5b9, 0x16d98a0c, 0x820e56be,
  0x16c0cef9, 0x8209dc9e,
  0x16a81305, 0x82056758, 0x168f5632, 0x8200f6ef, 0x1676987f, 0x81fc8b60,
  0x165dd9f0, 0x81f824ae,
  0x16451a83, 0x81f3c2d7, 0x162c5a3b, 0x81ef65dc, 0x16139918, 0x81eb0dbe,
  0x15fad71b, 0x81e6ba7c,
  0x15e21445, 0x81e26c16, 0x15c95097, 0x81de228d, 0x15b08c12, 0x81d9dde1,
  0x1597c6b7, 0x81d59e13,
  0x157f0086, 0x81d16321, 0x15663982, 0x81cd2d0c, 0x154d71aa, 0x81c8fbd6,
  0x1534a901, 0x81c4cf7d,
  0x151bdf86, 0x81c0a801, 0x1503153a, 0x81bc8564, 0x14ea4a1f, 0x81b867a5,
  0x14d17e36, 0x81b44ec4,
  0x14b8b17f, 0x81b03ac2, 0x149fe3fc, 0x81ac2b9e, 0x148715ae, 0x81a82159,
  0x146e4694, 0x81a41bf4,
  0x145576b1, 0x81a01b6d, 0x143ca605, 0x819c1fc5, 0x1423d492, 0x819828fd,
  0x140b0258, 0x81943715,
  0x13f22f58, 0x81904a0c, 0x13d95b93, 0x818c61e3, 0x13c0870a, 0x81887e9a,
  0x13a7b1bf, 0x8184a032,
  0x138edbb1, 0x8180c6a9, 0x137604e2, 0x817cf201, 0x135d2d53, 0x8179223a,
  0x13445505, 0x81755754,
  0x132b7bf9, 0x8171914e, 0x1312a230, 0x816dd02a, 0x12f9c7aa, 0x816a13e6,
  0x12e0ec6a, 0x81665c84,
  0x12c8106f, 0x8162aa04, 0x12af33ba, 0x815efc65, 0x1296564d, 0x815b53a8,
  0x127d7829, 0x8157afcd,
  0x1264994e, 0x815410d4, 0x124bb9be, 0x815076bd, 0x1232d979, 0x814ce188,
  0x1219f880, 0x81495136,
  0x120116d5, 0x8145c5c7, 0x11e83478, 0x81423f3a, 0x11cf516a, 0x813ebd90,
  0x11b66dad, 0x813b40ca,
  0x119d8941, 0x8137c8e6, 0x1184a427, 0x813455e6, 0x116bbe60, 0x8130e7c9,
  0x1152d7ed, 0x812d7e8f,
  0x1139f0cf, 0x812a1a3a, 0x11210907, 0x8126bac8, 0x11082096, 0x8123603a,
  0x10ef377d, 0x81200a90,
  0x10d64dbd, 0x811cb9ca, 0x10bd6356, 0x81196de9, 0x10a4784b, 0x811626ec,
  0x108b8c9b, 0x8112e4d4,
  0x1072a048, 0x810fa7a0, 0x1059b352, 0x810c6f52, 0x1040c5bb, 0x81093be8,
  0x1027d784, 0x81060d63,
  0x100ee8ad, 0x8102e3c4, 0xff5f938, 0x80ffbf0a, 0xfdd0926, 0x80fc9f35,
  0xfc41876, 0x80f98446,
  0xfab272b, 0x80f66e3c, 0xf923546, 0x80f35d19, 0xf7942c7, 0x80f050db,
  0xf604faf, 0x80ed4984,
  0xf475bff, 0x80ea4712, 0xf2e67b8, 0x80e74987, 0xf1572dc, 0x80e450e2,
  0xefc7d6b, 0x80e15d24,
  0xee38766, 0x80de6e4c, 0xeca90ce, 0x80db845b, 0xeb199a4, 0x80d89f51,
  0xe98a1e9, 0x80d5bf2e,
  0xe7fa99e, 0x80d2e3f2, 0xe66b0c3, 0x80d00d9d, 0xe4db75b, 0x80cd3c2f,
  0xe34bd66, 0x80ca6fa9,
  0xe1bc2e4, 0x80c7a80a, 0xe02c7d7, 0x80c4e553, 0xde9cc40, 0x80c22784,
  0xdd0d01f, 0x80bf6e9c,
  0xdb7d376, 0x80bcba9d, 0xd9ed646, 0x80ba0b85, 0xd85d88f, 0x80b76156,
  0xd6cda53, 0x80b4bc0e,
  0xd53db92, 0x80b21baf, 0xd3adc4e, 0x80af8039, 0xd21dc87, 0x80ace9ab,
  0xd08dc3f, 0x80aa5806,
  0xcefdb76, 0x80a7cb49, 0xcd6da2d, 0x80a54376, 0xcbdd865, 0x80a2c08b,
  0xca4d620, 0x80a04289,
  0xc8bd35e, 0x809dc971, 0xc72d020, 0x809b5541, 0xc59cc68, 0x8098e5fb,
  0xc40c835, 0x80967b9f,
  0xc27c389, 0x8094162c, 0xc0ebe66, 0x8091b5a2, 0xbf5b8cb, 0x808f5a02,
  0xbdcb2bb, 0x808d034c,
  0xbc3ac35, 0x808ab180, 0xbaaa53b, 0x8088649e, 0xb919dcf, 0x80861ca6,
  0xb7895f0, 0x8083d998,
  0xb5f8d9f, 0x80819b74, 0xb4684df, 0x807f623b, 0xb2d7baf, 0x807d2dec,
  0xb147211, 0x807afe87,
  0xafb6805, 0x8078d40d, 0xae25d8d, 0x8076ae7e, 0xac952aa, 0x80748dd9,
  0xab0475c, 0x8072721f,
  0xa973ba5, 0x80705b50, 0xa7e2f85, 0x806e496c, 0xa6522fe, 0x806c3c74,
  0xa4c1610, 0x806a3466,
  0xa3308bd, 0x80683143, 0xa19fb04, 0x8066330c, 0xa00ece8, 0x806439c0,
  0x9e7de6a, 0x80624560,
  0x9cecf89, 0x806055eb, 0x9b5c048, 0x805e6b62, 0x99cb0a7, 0x805c85c4,
  0x983a0a7, 0x805aa512,
  0x96a9049, 0x8058c94c, 0x9517f8f, 0x8056f272, 0x9386e78, 0x80552084,
  0x91f5d06, 0x80535381,
  0x9064b3a, 0x80518b6b, 0x8ed3916, 0x804fc841, 0x8d42699, 0x804e0a04,
  0x8bb13c5, 0x804c50b2,
  0x8a2009a, 0x804a9c4d, 0x888ed1b, 0x8048ecd5, 0x86fd947, 0x80474248,
  0x856c520, 0x80459ca9,
  0x83db0a7, 0x8043fbf6, 0x8249bdd, 0x80426030, 0x80b86c2, 0x8040c956,
  0x7f27157, 0x803f376a,
  0x7d95b9e, 0x803daa6a, 0x7c04598, 0x803c2257, 0x7a72f45, 0x803a9f31,
  0x78e18a7, 0x803920f8,
  0x77501be, 0x8037a7ac, 0x75bea8c, 0x8036334e, 0x742d311, 0x8034c3dd,
  0x729bb4e, 0x80335959,
  0x710a345, 0x8031f3c2, 0x6f78af6, 0x80309318, 0x6de7262, 0x802f375d,
  0x6c5598a, 0x802de08e,
  0x6ac406f, 0x802c8ead, 0x6932713, 0x802b41ba, 0x67a0d76, 0x8029f9b4,
  0x660f398, 0x8028b69c,
  0x647d97c, 0x80277872, 0x62ebf22, 0x80263f36, 0x615a48b, 0x80250ae7,
  0x5fc89b8, 0x8023db86,
  0x5e36ea9, 0x8022b114, 0x5ca5361, 0x80218b8f, 0x5b137df, 0x80206af8,
  0x5981c26, 0x801f4f4f,
  0x57f0035, 0x801e3895, 0x565e40d, 0x801d26c8, 0x54cc7b1, 0x801c19ea,
  0x533ab20, 0x801b11fa,
  0x51a8e5c, 0x801a0ef8, 0x5017165, 0x801910e4, 0x4e8543e, 0x801817bf,
  0x4cf36e5, 0x80172388,
  0x4b6195d, 0x80163440, 0x49cfba7, 0x801549e6, 0x483ddc3, 0x8014647b,
  0x46abfb3, 0x801383fe,
  0x451a177, 0x8012a86f, 0x4388310, 0x8011d1d0, 0x41f6480, 0x8011001f,
  0x40645c7, 0x8010335c,
  0x3ed26e6, 0x800f6b88, 0x3d407df, 0x800ea8a3, 0x3bae8b2, 0x800deaad,
  0x3a1c960, 0x800d31a5,
  0x388a9ea, 0x800c7d8c, 0x36f8a51, 0x800bce63, 0x3566a96, 0x800b2427,
  0x33d4abb, 0x800a7edb,
  0x3242abf, 0x8009de7e, 0x30b0aa4, 0x80094310, 0x2f1ea6c, 0x8008ac90,
  0x2d8ca16, 0x80081b00,
  0x2bfa9a4, 0x80078e5e, 0x2a68917, 0x800706ac, 0x28d6870, 0x800683e8,
  0x27447b0, 0x80060614,
  0x25b26d7, 0x80058d2f, 0x24205e8, 0x80051939, 0x228e4e2, 0x8004aa32,
  0x20fc3c6, 0x8004401a,
  0x1f6a297, 0x8003daf1, 0x1dd8154, 0x80037ab7, 0x1c45ffe, 0x80031f6d,
  0x1ab3e97, 0x8002c912,
  0x1921d20, 0x800277a6, 0x178fb99, 0x80022b29, 0x15fda03, 0x8001e39b,
  0x146b860, 0x8001a0fd,
  0x12d96b1, 0x8001634e, 0x11474f6, 0x80012a8e, 0xfb5330, 0x8000f6bd,
  0xe23160, 0x8000c7dc,
  0xc90f88, 0x80009dea, 0xafeda8, 0x800078e7, 0x96cbc1, 0x800058d4, 0x7da9d4,
  0x80003daf,
  0x6487e3, 0x8000277a, 0x4b65ee, 0x80001635, 0x3243f5, 0x800009df, 0x1921fb,
  0x80000278,
};

/**   
* \par   
* cosFactor tables are generated using the formula : <pre>cos_factors[n] = 2 * cos((2n+1)*pi/(4*N))</pre>   
* \par   
* C command to generate the table   
* <pre>   
* for(i = 0; i< N; i++)   
* {   
*   cos_factors[i]= 2 * cos((2*i+1)*c/2);   
* } </pre>   
* \par   
* where <code>N</code> is the number of factors to generate and <code>c</code> is <code>pi/(2*N)</code>   
* \par   
* Then converted to q31 format by multiplying with 2^31 and saturated if required.   
*/


static const q31_t cos_factorsQ31_128[128] = {
  0x7fff6216, 0x7ffa72d1, 0x7ff09478, 0x7fe1c76b, 0x7fce0c3e, 0x7fb563b3,
  0x7f97cebd, 0x7f754e80,
  0x7f4de451, 0x7f2191b4, 0x7ef05860, 0x7eba3a39, 0x7e7f3957, 0x7e3f57ff,
  0x7dfa98a8, 0x7db0fdf8,
  0x7d628ac6, 0x7d0f4218, 0x7cb72724, 0x7c5a3d50, 0x7bf88830, 0x7b920b89,
  0x7b26cb4f, 0x7ab6cba4,
  0x7a4210d8, 0x79c89f6e, 0x794a7c12, 0x78c7aba2, 0x78403329, 0x77b417df,
  0x77235f2d, 0x768e0ea6,
  0x75f42c0b, 0x7555bd4c, 0x74b2c884, 0x740b53fb, 0x735f6626, 0x72af05a7,
  0x71fa3949, 0x71410805,
  0x708378ff, 0x6fc19385, 0x6efb5f12, 0x6e30e34a, 0x6d6227fa, 0x6c8f351c,
  0x6bb812d1, 0x6adcc964,
  0x69fd614a, 0x6919e320, 0x683257ab, 0x6746c7d8, 0x66573cbb, 0x6563bf92,
  0x646c59bf, 0x637114cc,
  0x6271fa69, 0x616f146c, 0x60686ccf, 0x5f5e0db3, 0x5e50015d, 0x5d3e5237,
  0x5c290acc, 0x5b1035cf,
  0x59f3de12, 0x58d40e8c, 0x57b0d256, 0x568a34a9, 0x556040e2, 0x5433027d,
  0x53028518, 0x51ced46e,
  0x5097fc5e, 0x4f5e08e3, 0x4e210617, 0x4ce10034, 0x4b9e0390, 0x4a581c9e,
  0x490f57ee, 0x47c3c22f,
  0x46756828, 0x452456bd, 0x43d09aed, 0x427a41d0, 0x4121589b, 0x3fc5ec98,
  0x3e680b2c, 0x3d07c1d6,
  0x3ba51e29, 0x3a402dd2, 0x38d8fe93, 0x376f9e46, 0x36041ad9, 0x34968250,
  0x3326e2c3, 0x31b54a5e,
  0x3041c761, 0x2ecc681e, 0x2d553afc, 0x2bdc4e6f, 0x2a61b101, 0x28e5714b,
  0x27679df4, 0x25e845b6,
  0x24677758, 0x22e541af, 0x2161b3a0, 0x1fdcdc1b, 0x1e56ca1e, 0x1ccf8cb3,
  0x1b4732ef, 0x19bdcbf3,
  0x183366e9, 0x16a81305, 0x151bdf86, 0x138edbb1, 0x120116d5, 0x1072a048,
  0xee38766, 0xd53db92,
  0xbc3ac35, 0xa3308bd, 0x8a2009a, 0x710a345, 0x57f0035, 0x3ed26e6, 0x25b26d7,
  0xc90f88,
};

static const q31_t cos_factorsQ31_512[512] = {
  0x7ffff621, 0x7fffa72c, 0x7fff0943, 0x7ffe1c65, 0x7ffce093, 0x7ffb55ce,
  0x7ff97c18, 0x7ff75370,
  0x7ff4dbd9, 0x7ff21553, 0x7feeffe1, 0x7feb9b85, 0x7fe7e841, 0x7fe3e616,
  0x7fdf9508, 0x7fdaf519,
  0x7fd6064c, 0x7fd0c8a3, 0x7fcb3c23, 0x7fc560cf, 0x7fbf36aa, 0x7fb8bdb8,
  0x7fb1f5fc, 0x7faadf7c,
  0x7fa37a3c, 0x7f9bc640, 0x7f93c38c, 0x7f8b7227, 0x7f82d214, 0x7f79e35a,
  0x7f70a5fe, 0x7f671a05,
  0x7f5d3f75, 0x7f531655, 0x7f489eaa, 0x7f3dd87c, 0x7f32c3d1, 0x7f2760af,
  0x7f1baf1e, 0x7f0faf25,
  0x7f0360cb, 0x7ef6c418, 0x7ee9d914, 0x7edc9fc6, 0x7ecf1837, 0x7ec14270,
  0x7eb31e78, 0x7ea4ac58,
  0x7e95ec1a, 0x7e86ddc6, 0x7e778166, 0x7e67d703, 0x7e57dea7, 0x7e47985b,
  0x7e37042a, 0x7e26221f,
  0x7e14f242, 0x7e0374a0, 0x7df1a942, 0x7ddf9034, 0x7dcd2981, 0x7dba7534,
  0x7da77359, 0x7d9423fc,
  0x7d808728, 0x7d6c9ce9, 0x7d58654d, 0x7d43e05e, 0x7d2f0e2b, 0x7d19eebf,
  0x7d048228, 0x7ceec873,
  0x7cd8c1ae, 0x7cc26de5, 0x7cabcd28, 0x7c94df83, 0x7c7da505, 0x7c661dbc,
  0x7c4e49b7, 0x7c362904,
  0x7c1dbbb3, 0x7c0501d2, 0x7bebfb70, 0x7bd2a89e, 0x7bb9096b, 0x7b9f1de6,
  0x7b84e61f, 0x7b6a6227,
  0x7b4f920e, 0x7b3475e5, 0x7b190dbc, 0x7afd59a4, 0x7ae159ae, 0x7ac50dec,
  0x7aa8766f, 0x7a8b9348,
  0x7a6e648a, 0x7a50ea47, 0x7a332490, 0x7a151378, 0x79f6b711, 0x79d80f6f,
  0x79b91ca4, 0x7999dec4,
  0x797a55e0, 0x795a820e, 0x793a6361, 0x7919f9ec, 0x78f945c3, 0x78d846fb,
  0x78b6fda8, 0x789569df,
  0x78738bb3, 0x7851633b, 0x782ef08b, 0x780c33b8, 0x77e92cd9, 0x77c5dc01,
  0x77a24148, 0x777e5cc3,
  0x775a2e89, 0x7735b6af, 0x7710f54c, 0x76ebea77, 0x76c69647, 0x76a0f8d2,
  0x767b1231, 0x7654e279,
  0x762e69c4, 0x7607a828, 0x75e09dbd, 0x75b94a9c, 0x7591aedd, 0x7569ca99,
  0x75419de7, 0x751928e0,
  0x74f06b9e, 0x74c7663a, 0x749e18cd, 0x74748371, 0x744aa63f, 0x74208150,
  0x73f614c0, 0x73cb60a8,
  0x73a06522, 0x73752249, 0x73499838, 0x731dc70a, 0x72f1aed9, 0x72c54fc1,
  0x7298a9dd, 0x726bbd48,
  0x723e8a20, 0x7211107e, 0x71e35080, 0x71b54a41, 0x7186fdde, 0x71586b74,
  0x7129931f, 0x70fa74fc,
  0x70cb1128, 0x709b67c0, 0x706b78e3, 0x703b44ad, 0x700acb3c, 0x6fda0cae,
  0x6fa90921, 0x6f77c0b3,
  0x6f463383, 0x6f1461b0, 0x6ee24b57, 0x6eaff099, 0x6e7d5193, 0x6e4a6e66,
  0x6e174730, 0x6de3dc11,
  0x6db02d29, 0x6d7c3a98, 0x6d48047e, 0x6d138afb, 0x6cdece2f, 0x6ca9ce3b,
  0x6c748b3f, 0x6c3f055d,
  0x6c093cb6, 0x6bd3316a, 0x6b9ce39b, 0x6b66536b, 0x6b2f80fb, 0x6af86c6c,
  0x6ac115e2, 0x6a897d7d,
  0x6a51a361, 0x6a1987b0, 0x69e12a8c, 0x69a88c19, 0x696fac78, 0x69368bce,
  0x68fd2a3d, 0x68c387e9,
  0x6889a4f6, 0x684f8186, 0x68151dbe, 0x67da79c3, 0x679f95b7, 0x676471c0,
  0x67290e02, 0x66ed6aa1,
  0x66b187c3, 0x6675658c, 0x66390422, 0x65fc63a9, 0x65bf8447, 0x65826622,
  0x6545095f, 0x65076e25,
  0x64c99498, 0x648b7ce0, 0x644d2722, 0x640e9386, 0x63cfc231, 0x6390b34a,
  0x635166f9, 0x6311dd64,
  0x62d216b3, 0x6292130c, 0x6251d298, 0x6211557e, 0x61d09be5, 0x618fa5f7,
  0x614e73da, 0x610d05b7,
  0x60cb5bb7, 0x60897601, 0x604754bf, 0x6004f819, 0x5fc26038, 0x5f7f8d46,
  0x5f3c7f6b, 0x5ef936d1,
  0x5eb5b3a2, 0x5e71f606, 0x5e2dfe29, 0x5de9cc33, 0x5da5604f, 0x5d60baa7,
  0x5d1bdb65, 0x5cd6c2b5,
  0x5c9170bf, 0x5c4be5b0, 0x5c0621b2, 0x5bc024f0, 0x5b79ef96, 0x5b3381ce,
  0x5aecdbc5, 0x5aa5fda5,
  0x5a5ee79a, 0x5a1799d1, 0x59d01475, 0x598857b2, 0x594063b5, 0x58f838a9,
  0x58afd6bd, 0x58673e1b,
  0x581e6ef1, 0x57d5696d, 0x578c2dba, 0x5742bc06, 0x56f9147e, 0x56af3750,
  0x566524aa, 0x561adcb9,
  0x55d05faa, 0x5585adad, 0x553ac6ee, 0x54efab9c, 0x54a45be6, 0x5458d7f9,
  0x540d2005, 0x53c13439,
  0x537514c2, 0x5328c1d0, 0x52dc3b92, 0x528f8238, 0x524295f0, 0x51f576ea,
  0x51a82555, 0x515aa162,
  0x510ceb40, 0x50bf031f, 0x5070e92f, 0x50229da1, 0x4fd420a4, 0x4f857269,
  0x4f369320, 0x4ee782fb,
  0x4e984229, 0x4e48d0dd, 0x4df92f46, 0x4da95d96, 0x4d595bfe, 0x4d092ab0,
  0x4cb8c9dd, 0x4c6839b7,
  0x4c177a6e, 0x4bc68c36, 0x4b756f40, 0x4b2423be, 0x4ad2a9e2, 0x4a8101de,
  0x4a2f2be6, 0x49dd282a,
  0x498af6df, 0x49389836, 0x48e60c62, 0x48935397, 0x48406e08, 0x47ed5be6,
  0x479a1d67, 0x4746b2bc,
  0x46f31c1a, 0x469f59b4, 0x464b6bbe, 0x45f7526b, 0x45a30df0, 0x454e9e80,
  0x44fa0450, 0x44a53f93,
  0x4450507e, 0x43fb3746, 0x43a5f41e, 0x4350873c, 0x42faf0d4, 0x42a5311b,
  0x424f4845, 0x41f93689,
  0x41a2fc1a, 0x414c992f, 0x40f60dfb, 0x409f5ab6, 0x40487f94, 0x3ff17cca,
  0x3f9a5290, 0x3f430119,
  0x3eeb889c, 0x3e93e950, 0x3e3c2369, 0x3de4371f, 0x3d8c24a8, 0x3d33ec39,
  0x3cdb8e09, 0x3c830a50,
  0x3c2a6142, 0x3bd19318, 0x3b78a007, 0x3b1f8848, 0x3ac64c0f, 0x3a6ceb96,
  0x3a136712, 0x39b9bebc,
  0x395ff2c9, 0x39060373, 0x38abf0ef, 0x3851bb77, 0x37f76341, 0x379ce885,
  0x37424b7b, 0x36e78c5b,
  0x368cab5c, 0x3631a8b8, 0x35d684a6, 0x357b3f5d, 0x351fd918, 0x34c4520d,
  0x3468aa76, 0x340ce28b,
  0x33b0fa84, 0x3354f29b, 0x32f8cb07, 0x329c8402, 0x32401dc6, 0x31e39889,
  0x3186f487, 0x312a31f8,
  0x30cd5115, 0x30705217, 0x30133539, 0x2fb5fab2, 0x2f58a2be, 0x2efb2d95,
  0x2e9d9b70, 0x2e3fec8b,
  0x2de2211e, 0x2d843964, 0x2d263596, 0x2cc815ee, 0x2c69daa6, 0x2c0b83fa,
  0x2bad1221, 0x2b4e8558,
  0x2aefddd8, 0x2a911bdc, 0x2a323f9e, 0x29d34958, 0x29743946, 0x29150fa1,
  0x28b5cca5, 0x2856708d,
  0x27f6fb92, 0x27976df1, 0x2737c7e3, 0x26d809a5, 0x26783370, 0x26184581,
  0x25b84012, 0x2558235f,
  0x24f7efa2, 0x2497a517, 0x243743fa, 0x23d6cc87, 0x23763ef7, 0x23159b88,
  0x22b4e274, 0x225413f8,
  0x21f3304f, 0x219237b5, 0x21312a65, 0x20d0089c, 0x206ed295, 0x200d888d,
  0x1fac2abf, 0x1f4ab968,
  0x1ee934c3, 0x1e879d0d, 0x1e25f282, 0x1dc4355e, 0x1d6265dd, 0x1d00843d,
  0x1c9e90b8, 0x1c3c8b8c,
  0x1bda74f6, 0x1b784d30, 0x1b161479, 0x1ab3cb0d, 0x1a517128, 0x19ef0707,
  0x198c8ce7, 0x192a0304,
  0x18c7699b, 0x1864c0ea, 0x1802092c, 0x179f429f, 0x173c6d80, 0x16d98a0c,
  0x1676987f, 0x16139918,
  0x15b08c12, 0x154d71aa, 0x14ea4a1f, 0x148715ae, 0x1423d492, 0x13c0870a,
  0x135d2d53, 0x12f9c7aa,
  0x1296564d, 0x1232d979, 0x11cf516a, 0x116bbe60, 0x11082096, 0x10a4784b,
  0x1040c5bb, 0xfdd0926,
  0xf7942c7, 0xf1572dc, 0xeb199a4, 0xe4db75b, 0xde9cc40, 0xd85d88f, 0xd21dc87,
  0xcbdd865,
  0xc59cc68, 0xbf5b8cb, 0xb919dcf, 0xb2d7baf, 0xac952aa, 0xa6522fe, 0xa00ece8,
  0x99cb0a7,
  0x9386e78, 0x8d42699, 0x86fd947, 0x80b86c2, 0x7a72f45, 0x742d311, 0x6de7262,
  0x67a0d76,
  0x615a48b, 0x5b137df, 0x54cc7b1, 0x4e8543e, 0x483ddc3, 0x41f6480, 0x3bae8b2,
  0x3566a96,
  0x2f1ea6c, 0x28d6870, 0x228e4e2, 0x1c45ffe, 0x15fda03, 0xfb5330, 0x96cbc1,
  0x3243f5,
};

static const q31_t cos_factorsQ31_2048[2048] = {
  0x7fffff62, 0x7ffffa73, 0x7ffff094, 0x7fffe1c6, 0x7fffce09, 0x7fffb55c,
  0x7fff97c1, 0x7fff7536,
  0x7fff4dbb, 0x7fff2151, 0x7ffeeff8, 0x7ffeb9b0, 0x7ffe7e79, 0x7ffe3e52,
  0x7ffdf93c, 0x7ffdaf37,
  0x7ffd6042, 0x7ffd0c5f, 0x7ffcb38c, 0x7ffc55ca, 0x7ffbf319, 0x7ffb8b78,
  0x7ffb1ee9, 0x7ffaad6a,
  0x7ffa36fc, 0x7ff9bba0, 0x7ff93b54, 0x7ff8b619, 0x7ff82bef, 0x7ff79cd6,
  0x7ff708ce, 0x7ff66fd7,
  0x7ff5d1f1, 0x7ff52f1d, 0x7ff48759, 0x7ff3daa6, 0x7ff32905, 0x7ff27275,
  0x7ff1b6f6, 0x7ff0f688,
  0x7ff0312c, 0x7fef66e1, 0x7fee97a7, 0x7fedc37e, 0x7fecea67, 0x7fec0c62,
  0x7feb296d, 0x7fea418b,
  0x7fe954ba, 0x7fe862fa, 0x7fe76c4c, 0x7fe670b0, 0x7fe57025, 0x7fe46aac,
  0x7fe36045, 0x7fe250ef,
  0x7fe13cac, 0x7fe0237a, 0x7fdf055a, 0x7fdde24d, 0x7fdcba51, 0x7fdb8d67,
  0x7fda5b8f, 0x7fd924ca,
  0x7fd7e917, 0x7fd6a875, 0x7fd562e7, 0x7fd4186a, 0x7fd2c900, 0x7fd174a8,
  0x7fd01b63, 0x7fcebd31,
  0x7fcd5a11, 0x7fcbf203, 0x7fca8508, 0x7fc91320, 0x7fc79c4b, 0x7fc62089,
  0x7fc49fda, 0x7fc31a3d,
  0x7fc18fb4, 0x7fc0003e, 0x7fbe6bdb, 0x7fbcd28b, 0x7fbb344e, 0x7fb99125,
  0x7fb7e90f, 0x7fb63c0d,
  0x7fb48a1e, 0x7fb2d343, 0x7fb1177b, 0x7faf56c7, 0x7fad9127, 0x7fabc69b,
  0x7fa9f723, 0x7fa822bf,
  0x7fa6496e, 0x7fa46b32, 0x7fa2880b, 0x7fa09ff7, 0x7f9eb2f8, 0x7f9cc10d,
  0x7f9aca37, 0x7f98ce76,
  0x7f96cdc9, 0x7f94c831, 0x7f92bdad, 0x7f90ae3f, 0x7f8e99e6, 0x7f8c80a1,
  0x7f8a6272, 0x7f883f58,
  0x7f861753, 0x7f83ea64, 0x7f81b88a, 0x7f7f81c6, 0x7f7d4617, 0x7f7b057e,
  0x7f78bffb, 0x7f76758e,
  0x7f742637, 0x7f71d1f6, 0x7f6f78cb, 0x7f6d1ab6, 0x7f6ab7b8, 0x7f684fd0,
  0x7f65e2ff, 0x7f637144,
  0x7f60faa0, 0x7f5e7f13, 0x7f5bfe9d, 0x7f59793e, 0x7f56eef5, 0x7f545fc5,
  0x7f51cbab, 0x7f4f32a9,
  0x7f4c94be, 0x7f49f1eb, 0x7f474a30, 0x7f449d8c, 0x7f41ec01, 0x7f3f358d,
  0x7f3c7a31, 0x7f39b9ee,
  0x7f36f4c3, 0x7f342ab1, 0x7f315bb7, 0x7f2e87d6, 0x7f2baf0d, 0x7f28d15d,
  0x7f25eec7, 0x7f230749,
  0x7f201ae5, 0x7f1d299a, 0x7f1a3368, 0x7f173850, 0x7f143852, 0x7f11336d,
  0x7f0e29a3, 0x7f0b1af2,
  0x7f08075c, 0x7f04eedf, 0x7f01d17d, 0x7efeaf36, 0x7efb8809, 0x7ef85bf7,
  0x7ef52b00, 0x7ef1f524,
  0x7eeeba62, 0x7eeb7abc, 0x7ee83632, 0x7ee4ecc3, 0x7ee19e6f, 0x7ede4b38,
  0x7edaf31c, 0x7ed7961c,
  0x7ed43438, 0x7ed0cd70, 0x7ecd61c5, 0x7ec9f137, 0x7ec67bc5, 0x7ec3016f,
  0x7ebf8237, 0x7ebbfe1c,
  0x7eb8751e, 0x7eb4e73d, 0x7eb1547a, 0x7eadbcd4, 0x7eaa204c, 0x7ea67ee2,
  0x7ea2d896, 0x7e9f2d68,
  0x7e9b7d58, 0x7e97c867, 0x7e940e94, 0x7e904fe0, 0x7e8c8c4b, 0x7e88c3d5,
  0x7e84f67e, 0x7e812447,
  0x7e7d4d2f, 0x7e797136, 0x7e75905d, 0x7e71aaa4, 0x7e6dc00c, 0x7e69d093,
  0x7e65dc3b, 0x7e61e303,
  0x7e5de4ec, 0x7e59e1f5, 0x7e55da20, 0x7e51cd6c, 0x7e4dbbd9, 0x7e49a567,
  0x7e458a17, 0x7e4169e9,
  0x7e3d44dd, 0x7e391af3, 0x7e34ec2b, 0x7e30b885, 0x7e2c8002, 0x7e2842a2,
  0x7e240064, 0x7e1fb94a,
  0x7e1b6d53, 0x7e171c7f, 0x7e12c6ce, 0x7e0e6c42, 0x7e0a0cd9, 0x7e05a894,
  0x7e013f74, 0x7dfcd178,
  0x7df85ea0, 0x7df3e6ee, 0x7def6a60, 0x7deae8f7, 0x7de662b3, 0x7de1d795,
  0x7ddd479d, 0x7dd8b2ca,
  0x7dd4191d, 0x7dcf7a96, 0x7dcad736, 0x7dc62efc, 0x7dc181e8, 0x7dbccffc,
  0x7db81936, 0x7db35d98,
  0x7dae9d21, 0x7da9d7d2, 0x7da50dab, 0x7da03eab, 0x7d9b6ad3, 0x7d969224,
  0x7d91b49e, 0x7d8cd240,
  0x7d87eb0a, 0x7d82fefe, 0x7d7e0e1c, 0x7d791862, 0x7d741dd2, 0x7d6f1e6c,
  0x7d6a1a31, 0x7d65111f,
  0x7d600338, 0x7d5af07b, 0x7d55d8e9, 0x7d50bc82, 0x7d4b9b46, 0x7d467536,
  0x7d414a51, 0x7d3c1a98,
  0x7d36e60b, 0x7d31acaa, 0x7d2c6e76, 0x7d272b6e, 0x7d21e393, 0x7d1c96e5,
  0x7d174564, 0x7d11ef11,
  0x7d0c93eb, 0x7d0733f3, 0x7d01cf29, 0x7cfc658d, 0x7cf6f720, 0x7cf183e1,
  0x7cec0bd1, 0x7ce68ef0,
  0x7ce10d3f, 0x7cdb86bd, 0x7cd5fb6a, 0x7cd06b48, 0x7ccad656, 0x7cc53c94,
  0x7cbf9e03, 0x7cb9faa2,
  0x7cb45272, 0x7caea574, 0x7ca8f3a7, 0x7ca33d0c, 0x7c9d81a3, 0x7c97c16b,
  0x7c91fc66, 0x7c8c3294,
  0x7c8663f4, 0x7c809088, 0x7c7ab84e, 0x7c74db48, 0x7c6ef976, 0x7c6912d7,
  0x7c63276d, 0x7c5d3737,
  0x7c574236, 0x7c514869, 0x7c4b49d2, 0x7c45466f, 0x7c3f3e42, 0x7c39314b,
  0x7c331f8a, 0x7c2d08ff,
  0x7c26edab, 0x7c20cd8d, 0x7c1aa8a6, 0x7c147ef6, 0x7c0e507e, 0x7c081d3d,
  0x7c01e534, 0x7bfba863,
  0x7bf566cb, 0x7bef206b, 0x7be8d544, 0x7be28556, 0x7bdc30a1, 0x7bd5d726,
  0x7bcf78e5, 0x7bc915dd,
  0x7bc2ae10, 0x7bbc417e, 0x7bb5d026, 0x7baf5a09, 0x7ba8df28, 0x7ba25f82,
  0x7b9bdb18, 0x7b9551ea,
  0x7b8ec3f8, 0x7b883143, 0x7b8199ca, 0x7b7afd8f, 0x7b745c91, 0x7b6db6d0,
  0x7b670c4d, 0x7b605d09,
  0x7b59a902, 0x7b52f03a, 0x7b4c32b1, 0x7b457068, 0x7b3ea95d, 0x7b37dd92,
  0x7b310d07, 0x7b2a37bc,
  0x7b235db2, 0x7b1c7ee8, 0x7b159b5f, 0x7b0eb318, 0x7b07c612, 0x7b00d44d,
  0x7af9ddcb, 0x7af2e28b,
  0x7aebe28d, 0x7ae4ddd2, 0x7addd45b, 0x7ad6c626, 0x7acfb336, 0x7ac89b89,
  0x7ac17f20, 0x7aba5dfc,
  0x7ab3381d, 0x7aac0d82, 0x7aa4de2d, 0x7a9daa1d, 0x7a967153, 0x7a8f33d0,
  0x7a87f192, 0x7a80aa9c,
  0x7a795eec, 0x7a720e84, 0x7a6ab963, 0x7a635f8a, 0x7a5c00f9, 0x7a549db0,
  0x7a4d35b0, 0x7a45c8f9,
  0x7a3e578b, 0x7a36e166, 0x7a2f668c, 0x7a27e6fb, 0x7a2062b5, 0x7a18d9b9,
  0x7a114c09, 0x7a09b9a4,
  0x7a02228a, 0x79fa86bc, 0x79f2e63a, 0x79eb4105, 0x79e3971c, 0x79dbe880,
  0x79d43532, 0x79cc7d31,
  0x79c4c07e, 0x79bcff19, 0x79b53903, 0x79ad6e3c, 0x79a59ec3, 0x799dca9a,
  0x7995f1c1, 0x798e1438,
  0x798631ff, 0x797e4b16, 0x79765f7f, 0x796e6f39, 0x79667a44, 0x795e80a1,
  0x79568250, 0x794e7f52,
  0x794677a6, 0x793e6b4e, 0x79365a49, 0x792e4497, 0x79262a3a, 0x791e0b31,
  0x7915e77c, 0x790dbf1d,
  0x79059212, 0x78fd605d, 0x78f529fe, 0x78eceef6, 0x78e4af44, 0x78dc6ae8,
  0x78d421e4, 0x78cbd437,
  0x78c381e2, 0x78bb2ae5, 0x78b2cf41, 0x78aa6ef5, 0x78a20a03, 0x7899a06a,
  0x7891322a, 0x7888bf45,
  0x788047ba, 0x7877cb89, 0x786f4ab4, 0x7866c53a, 0x785e3b1c, 0x7855ac5a,
  0x784d18f4, 0x784480ea,
  0x783be43e, 0x783342ef, 0x782a9cfe, 0x7821f26b, 0x78194336, 0x78108f60,
  0x7807d6e9, 0x77ff19d1,
  0x77f65819, 0x77ed91c0, 0x77e4c6c9, 0x77dbf732, 0x77d322fc, 0x77ca4a27,
  0x77c16cb4, 0x77b88aa3,
  0x77afa3f5, 0x77a6b8a9, 0x779dc8c0, 0x7794d43b, 0x778bdb19, 0x7782dd5c,
  0x7779db03, 0x7770d40f,
  0x7767c880, 0x775eb857, 0x7755a394, 0x774c8a36, 0x77436c40, 0x773a49b0,
  0x77312287, 0x7727f6c6,
  0x771ec66e, 0x7715917d, 0x770c57f5, 0x770319d6, 0x76f9d721, 0x76f08fd5,
  0x76e743f4, 0x76ddf37c,
  0x76d49e70, 0x76cb44cf, 0x76c1e699, 0x76b883d0, 0x76af1c72, 0x76a5b082,
  0x769c3ffe, 0x7692cae8,
  0x7689513f, 0x767fd304, 0x76765038, 0x766cc8db, 0x76633ced, 0x7659ac6f,
  0x76501760, 0x76467dc2,
  0x763cdf94, 0x76333cd8, 0x7629958c, 0x761fe9b3, 0x7616394c, 0x760c8457,
  0x7602cad5, 0x75f90cc7,
  0x75ef4a2c, 0x75e58305, 0x75dbb753, 0x75d1e715, 0x75c8124d, 0x75be38fa,
  0x75b45b1d, 0x75aa78b6,
  0x75a091c6, 0x7596a64d, 0x758cb64c, 0x7582c1c2, 0x7578c8b0, 0x756ecb18,
  0x7564c8f8, 0x755ac251,
  0x7550b725, 0x7546a772, 0x753c933a, 0x75327a7d, 0x75285d3b, 0x751e3b75,
  0x7514152b, 0x7509ea5d,
  0x74ffbb0d, 0x74f58739, 0x74eb4ee3, 0x74e1120c, 0x74d6d0b2, 0x74cc8ad8,
  0x74c2407d, 0x74b7f1a1,
  0x74ad9e46, 0x74a3466b, 0x7498ea11, 0x748e8938, 0x748423e0, 0x7479ba0b,
  0x746f4bb8, 0x7464d8e8,
  0x745a619b, 0x744fe5d2, 0x7445658d, 0x743ae0cc, 0x74305790, 0x7425c9da,
  0x741b37a9, 0x7410a0fe,
  0x740605d9, 0x73fb663c, 0x73f0c226, 0x73e61997, 0x73db6c91, 0x73d0bb13,
  0x73c6051f, 0x73bb4ab3,
  0x73b08bd1, 0x73a5c87a, 0x739b00ad, 0x7390346b, 0x738563b5, 0x737a8e8a,
  0x736fb4ec, 0x7364d6da,
  0x7359f456, 0x734f0d5f, 0x734421f6, 0x7339321b, 0x732e3dcf, 0x73234512,
  0x731847e5, 0x730d4648,
  0x7302403c, 0x72f735c0, 0x72ec26d6, 0x72e1137d, 0x72d5fbb7, 0x72cadf83,
  0x72bfbee3, 0x72b499d6,
  0x72a9705c, 0x729e4277, 0x72931027, 0x7287d96c, 0x727c9e47, 0x72715eb8,
  0x72661abf, 0x725ad25d,
  0x724f8593, 0x72443460, 0x7238dec5, 0x722d84c4, 0x7222265b, 0x7216c38c,
  0x720b5c57, 0x71fff0bc,
  0x71f480bc, 0x71e90c57, 0x71dd938f, 0x71d21662, 0x71c694d2, 0x71bb0edf,
  0x71af848a, 0x71a3f5d2,
  0x719862b9, 0x718ccb3f, 0x71812f65, 0x71758f29, 0x7169ea8f, 0x715e4194,
  0x7152943b, 0x7146e284,
  0x713b2c6e, 0x712f71fb, 0x7123b32b, 0x7117effe, 0x710c2875, 0x71005c90,
  0x70f48c50, 0x70e8b7b5,
  0x70dcdec0, 0x70d10171, 0x70c51fc8, 0x70b939c7, 0x70ad4f6d, 0x70a160ba,
  0x70956db1, 0x70897650,
  0x707d7a98, 0x70717a8a, 0x70657626, 0x70596d6d, 0x704d6060, 0x70414efd,
  0x70353947, 0x70291f3e,
  0x701d00e1, 0x7010de32, 0x7004b731, 0x6ff88bde, 0x6fec5c3b, 0x6fe02846,
  0x6fd3f001, 0x6fc7b36d,
  0x6fbb728a, 0x6faf2d57, 0x6fa2e3d7, 0x6f969608, 0x6f8a43ed, 0x6f7ded84,
  0x6f7192cf, 0x6f6533ce,
  0x6f58d082, 0x6f4c68eb, 0x6f3ffd09, 0x6f338cde, 0x6f271868, 0x6f1a9faa,
  0x6f0e22a3, 0x6f01a155,
  0x6ef51bbe, 0x6ee891e1, 0x6edc03bc, 0x6ecf7152, 0x6ec2daa2, 0x6eb63fad,
  0x6ea9a073, 0x6e9cfcf5,
  0x6e905534, 0x6e83a92f, 0x6e76f8e7, 0x6e6a445d, 0x6e5d8b91, 0x6e50ce84,
  0x6e440d37, 0x6e3747a9,
  0x6e2a7ddb, 0x6e1dafce, 0x6e10dd82, 0x6e0406f8, 0x6df72c30, 0x6dea4d2b,
  0x6ddd69e9, 0x6dd0826a,
  0x6dc396b0, 0x6db6a6ba, 0x6da9b28a, 0x6d9cba1f, 0x6d8fbd7a, 0x6d82bc9d,
  0x6d75b786, 0x6d68ae37,
  0x6d5ba0b0, 0x6d4e8ef2, 0x6d4178fd, 0x6d345ed1, 0x6d274070, 0x6d1a1dda,
  0x6d0cf70f, 0x6cffcc0f,
  0x6cf29cdc, 0x6ce56975, 0x6cd831dc, 0x6ccaf610, 0x6cbdb613, 0x6cb071e4,
  0x6ca32985, 0x6c95dcf6,
  0x6c888c36, 0x6c7b3748, 0x6c6dde2b, 0x6c6080e0, 0x6c531f67, 0x6c45b9c1,
  0x6c384fef, 0x6c2ae1f0,
  0x6c1d6fc6, 0x6c0ff971, 0x6c027ef1, 0x6bf50047, 0x6be77d74, 0x6bd9f677,
  0x6bcc6b53, 0x6bbedc06,
  0x6bb14892, 0x6ba3b0f7, 0x6b961536, 0x6b88754f, 0x6b7ad142, 0x6b6d2911,
  0x6b5f7cbc, 0x6b51cc42,
  0x6b4417a6, 0x6b365ee7, 0x6b28a206, 0x6b1ae103, 0x6b0d1bdf, 0x6aff529a,
  0x6af18536, 0x6ae3b3b2,
  0x6ad5de0f, 0x6ac8044e, 0x6aba266e, 0x6aac4472, 0x6a9e5e58, 0x6a907423,
  0x6a8285d1, 0x6a749365,
  0x6a669cdd, 0x6a58a23c, 0x6a4aa381, 0x6a3ca0ad, 0x6a2e99c0, 0x6a208ebb,
  0x6a127f9f, 0x6a046c6c,
  0x69f65523, 0x69e839c4, 0x69da1a50, 0x69cbf6c7, 0x69bdcf29, 0x69afa378,
  0x69a173b5, 0x69933fde,
  0x698507f6, 0x6976cbfc, 0x69688bf1, 0x695a47d6, 0x694bffab, 0x693db371,
  0x692f6328, 0x69210ed1,
  0x6912b66c, 0x690459fb, 0x68f5f97d, 0x68e794f3, 0x68d92c5d, 0x68cabfbd,
  0x68bc4f13, 0x68adda5f,
  0x689f61a1, 0x6890e4dc, 0x6882640e, 0x6873df38, 0x6865565c, 0x6856c979,
  0x68483891, 0x6839a3a4,
  0x682b0ab1, 0x681c6dbb, 0x680dccc1, 0x67ff27c4, 0x67f07ec5, 0x67e1d1c4,
  0x67d320c1, 0x67c46bbe,
  0x67b5b2bb, 0x67a6f5b8, 0x679834b6, 0x67896fb6, 0x677aa6b8, 0x676bd9bd,
  0x675d08c4, 0x674e33d0,
  0x673f5ae0, 0x67307df5, 0x67219d10, 0x6712b831, 0x6703cf58, 0x66f4e287,
  0x66e5f1be, 0x66d6fcfd,
  0x66c80445, 0x66b90797, 0x66aa06f3, 0x669b0259, 0x668bf9cb, 0x667ced49,
  0x666ddcd3, 0x665ec86b,
  0x664fb010, 0x664093c3, 0x66317385, 0x66224f56, 0x66132738, 0x6603fb2a,
  0x65f4cb2d, 0x65e59742,
  0x65d65f69, 0x65c723a3, 0x65b7e3f1, 0x65a8a052, 0x659958c9, 0x658a0d54,
  0x657abdf6, 0x656b6aae,
  0x655c137d, 0x654cb863, 0x653d5962, 0x652df679, 0x651e8faa, 0x650f24f5,
  0x64ffb65b, 0x64f043dc,
  0x64e0cd78, 0x64d15331, 0x64c1d507, 0x64b252fa, 0x64a2cd0c, 0x6493433c,
  0x6483b58c, 0x647423fb,
  0x64648e8c, 0x6454f53d, 0x64455810, 0x6435b706, 0x6426121e, 0x6416695a,
  0x6406bcba, 0x63f70c3f,
  0x63e757ea, 0x63d79fba, 0x63c7e3b1, 0x63b823cf, 0x63a86015, 0x63989884,
  0x6388cd1b, 0x6378fddc,
  0x63692ac7, 0x635953dd, 0x6349791f, 0x63399a8d, 0x6329b827, 0x6319d1ef,
  0x6309e7e4, 0x62f9fa09,
  0x62ea085c, 0x62da12df, 0x62ca1992, 0x62ba1c77, 0x62aa1b8d, 0x629a16d5,
  0x628a0e50, 0x627a01fe,
  0x6269f1e1, 0x6259ddf8, 0x6249c645, 0x6239aac7, 0x62298b81, 0x62196871,
  0x62094199, 0x61f916f9,
  0x61e8e893, 0x61d8b666, 0x61c88074, 0x61b846bc, 0x61a80940, 0x6197c800,
  0x618782fd, 0x61773a37,
  0x6166edb0, 0x61569d67, 0x6146495d, 0x6135f193, 0x6125960a, 0x611536c2,
  0x6104d3bc, 0x60f46cf9,
  0x60e40278, 0x60d3943b, 0x60c32243, 0x60b2ac8f, 0x60a23322, 0x6091b5fa,
  0x60813519, 0x6070b080,
  0x6060282f, 0x604f9c27, 0x603f0c69, 0x602e78f4, 0x601de1ca, 0x600d46ec,
  0x5ffca859, 0x5fec0613,
  0x5fdb601b, 0x5fcab670, 0x5fba0914, 0x5fa95807, 0x5f98a34a, 0x5f87eade,
  0x5f772ec2, 0x5f666ef9,
  0x5f55ab82, 0x5f44e45e, 0x5f34198e, 0x5f234b12, 0x5f1278eb, 0x5f01a31a,
  0x5ef0c99f, 0x5edfec7b,
  0x5ecf0baf, 0x5ebe273b, 0x5ead3f1f, 0x5e9c535e, 0x5e8b63f7, 0x5e7a70ea,
  0x5e697a39, 0x5e587fe5,
  0x5e4781ed, 0x5e368053, 0x5e257b17, 0x5e147239, 0x5e0365bb, 0x5df2559e,
  0x5de141e1, 0x5dd02a85,
  0x5dbf0f8c, 0x5dadf0f5, 0x5d9ccec2, 0x5d8ba8f3, 0x5d7a7f88, 0x5d695283,
  0x5d5821e4, 0x5d46edac,
  0x5d35b5db, 0x5d247a72, 0x5d133b72, 0x5d01f8dc, 0x5cf0b2af, 0x5cdf68ed,
  0x5cce1b97, 0x5cbccaac,
  0x5cab762f, 0x5c9a1e1e, 0x5c88c27c, 0x5c776348, 0x5c660084, 0x5c549a30,
  0x5c43304d, 0x5c31c2db,
  0x5c2051db, 0x5c0edd4e, 0x5bfd6534, 0x5bebe98e, 0x5bda6a5d, 0x5bc8e7a2,
  0x5bb7615d, 0x5ba5d78e,
  0x5b944a37, 0x5b82b958, 0x5b7124f2, 0x5b5f8d06, 0x5b4df193, 0x5b3c529c,
  0x5b2ab020, 0x5b190a20,
  0x5b07609d, 0x5af5b398, 0x5ae40311, 0x5ad24f09, 0x5ac09781, 0x5aaedc78,
  0x5a9d1df1, 0x5a8b5bec,
  0x5a799669, 0x5a67cd69, 0x5a5600ec, 0x5a4430f5, 0x5a325d82, 0x5a208695,
  0x5a0eac2e, 0x59fcce4f,
  0x59eaecf8, 0x59d90829, 0x59c71fe3, 0x59b53427, 0x59a344f6, 0x59915250,
  0x597f5c36, 0x596d62a9,
  0x595b65aa, 0x59496538, 0x59376155, 0x59255a02, 0x59134f3e, 0x5901410c,
  0x58ef2f6b, 0x58dd1a5d,
  0x58cb01e1, 0x58b8e5f9, 0x58a6c6a5, 0x5894a3e7, 0x58827dbe, 0x5870542c,
  0x585e2730, 0x584bf6cd,
  0x5839c302, 0x58278bd1, 0x58155139, 0x5803133c, 0x57f0d1da, 0x57de8d15,
  0x57cc44ec, 0x57b9f960,
  0x57a7aa73, 0x57955825, 0x57830276, 0x5770a968, 0x575e4cfa, 0x574bed2f,
  0x57398a05, 0x5727237f,
  0x5714b99d, 0x57024c5f, 0x56efdbc7, 0x56dd67d4, 0x56caf088, 0x56b875e4,
  0x56a5f7e7, 0x56937694,
  0x5680f1ea, 0x566e69ea, 0x565bde95, 0x56494fec, 0x5636bdef, 0x5624289f,
  0x56118ffe, 0x55fef40a,
  0x55ec54c6, 0x55d9b232, 0x55c70c4f, 0x55b4631d, 0x55a1b69d, 0x558f06d0,
  0x557c53b6, 0x55699d51,
  0x5556e3a1, 0x554426a7, 0x55316663, 0x551ea2d6, 0x550bdc01, 0x54f911e5,
  0x54e64482, 0x54d373d9,
  0x54c09feb, 0x54adc8b8, 0x549aee42, 0x54881089, 0x54752f8d, 0x54624b50,
  0x544f63d2, 0x543c7914,
  0x54298b17, 0x541699db, 0x5403a561, 0x53f0adaa, 0x53ddb2b6, 0x53cab486,
  0x53b7b31c, 0x53a4ae77,
  0x5391a699, 0x537e9b82, 0x536b8d33, 0x53587bad, 0x534566f0, 0x53324efd,
  0x531f33d5, 0x530c1579,
  0x52f8f3e9, 0x52e5cf27, 0x52d2a732, 0x52bf7c0b, 0x52ac4db4, 0x52991c2d,
  0x5285e777, 0x5272af92,
  0x525f7480, 0x524c3640, 0x5238f4d4, 0x5225b03d, 0x5212687b, 0x51ff1d8f,
  0x51ebcf7a, 0x51d87e3c,
  0x51c529d7, 0x51b1d24a, 0x519e7797, 0x518b19bf, 0x5177b8c2, 0x516454a0,
  0x5150ed5c, 0x513d82f4,
  0x512a156b, 0x5116a4c1, 0x510330f7, 0x50efba0d, 0x50dc4005, 0x50c8c2de,
  0x50b5429a, 0x50a1bf39,
  0x508e38bd, 0x507aaf25, 0x50672273, 0x505392a8, 0x503fffc4, 0x502c69c8,
  0x5018d0b4, 0x5005348a,
  0x4ff1954b, 0x4fddf2f6, 0x4fca4d8d, 0x4fb6a510, 0x4fa2f981, 0x4f8f4ae0,
  0x4f7b992d, 0x4f67e46a,
  0x4f542c98, 0x4f4071b6, 0x4f2cb3c7, 0x4f18f2c9, 0x4f052ec0, 0x4ef167aa,
  0x4edd9d89, 0x4ec9d05e,
  0x4eb60029, 0x4ea22ceb, 0x4e8e56a5, 0x4e7a7d58, 0x4e66a105, 0x4e52c1ab,
  0x4e3edf4d, 0x4e2af9ea,
  0x4e171184, 0x4e03261b, 0x4def37b0, 0x4ddb4644, 0x4dc751d8, 0x4db35a6c,
  0x4d9f6001, 0x4d8b6298,
  0x4d776231, 0x4d635ece, 0x4d4f5870, 0x4d3b4f16, 0x4d2742c2, 0x4d133374,
  0x4cff212e, 0x4ceb0bf0,
  0x4cd6f3bb, 0x4cc2d88f, 0x4caeba6e, 0x4c9a9958, 0x4c86754e, 0x4c724e50,
  0x4c5e2460, 0x4c49f77f,
  0x4c35c7ac, 0x4c2194e9, 0x4c0d5f37, 0x4bf92697, 0x4be4eb08, 0x4bd0ac8d,
  0x4bbc6b25, 0x4ba826d1,
  0x4b93df93, 0x4b7f956b, 0x4b6b485a, 0x4b56f861, 0x4b42a580, 0x4b2e4fb8,
  0x4b19f70a, 0x4b059b77,
  0x4af13d00, 0x4adcdba5, 0x4ac87767, 0x4ab41046, 0x4a9fa645, 0x4a8b3963,
  0x4a76c9a2, 0x4a625701,
  0x4a4de182, 0x4a396926, 0x4a24edee, 0x4a106fda, 0x49fbeeea, 0x49e76b21,
  0x49d2e47e, 0x49be5b02,
  0x49a9ceaf, 0x49953f84, 0x4980ad84, 0x496c18ae, 0x49578103, 0x4942e684,
  0x492e4933, 0x4919a90f,
  0x4905061a, 0x48f06054, 0x48dbb7be, 0x48c70c59, 0x48b25e25, 0x489dad25,
  0x4888f957, 0x487442be,
  0x485f8959, 0x484acd2a, 0x48360e32, 0x48214c71, 0x480c87e8, 0x47f7c099,
  0x47e2f682, 0x47ce29a7,
  0x47b95a06, 0x47a487a2, 0x478fb27b, 0x477ada91, 0x4765ffe6, 0x4751227a,
  0x473c424e, 0x47275f63,
  0x471279ba, 0x46fd9154, 0x46e8a631, 0x46d3b852, 0x46bec7b8, 0x46a9d464,
  0x4694de56, 0x467fe590,
  0x466aea12, 0x4655ebdd, 0x4640eaf2, 0x462be751, 0x4616e0fc, 0x4601d7f3,
  0x45eccc37, 0x45d7bdc9,
  0x45c2acaa, 0x45ad98da, 0x4598825a, 0x4583692c, 0x456e4d4f, 0x45592ec6,
  0x45440d90, 0x452ee9ae,
  0x4519c321, 0x450499eb, 0x44ef6e0b, 0x44da3f83, 0x44c50e53, 0x44afda7d,
  0x449aa400, 0x44856adf,
  0x44702f19, 0x445af0b0, 0x4445afa4, 0x44306bf6, 0x441b25a8, 0x4405dcb9,
  0x43f0912b, 0x43db42fe,
  0x43c5f234, 0x43b09ecc, 0x439b48c9, 0x4385f02a, 0x437094f1, 0x435b371f,
  0x4345d6b3, 0x433073b0,
  0x431b0e15, 0x4305a5e5, 0x42f03b1e, 0x42dacdc3, 0x42c55dd4, 0x42afeb53,
  0x429a763f, 0x4284fe99,
  0x426f8463, 0x425a079e, 0x42448849, 0x422f0667, 0x421981f7, 0x4203fafb,
  0x41ee7174, 0x41d8e561,
  0x41c356c5, 0x41adc5a0, 0x419831f3, 0x41829bbe, 0x416d0302, 0x415767c1,
  0x4141c9fb, 0x412c29b1,
  0x411686e4, 0x4100e194, 0x40eb39c3, 0x40d58f71, 0x40bfe29f, 0x40aa334e,
  0x4094817f, 0x407ecd32,
  0x40691669, 0x40535d24, 0x403da165, 0x4027e32b, 0x40122278, 0x3ffc5f4d,
  0x3fe699aa, 0x3fd0d191,
  0x3fbb0702, 0x3fa539fd, 0x3f8f6a85, 0x3f799899, 0x3f63c43b, 0x3f4ded6b,
  0x3f38142a, 0x3f22387a,
  0x3f0c5a5a, 0x3ef679cc, 0x3ee096d1, 0x3ecab169, 0x3eb4c995, 0x3e9edf57,
  0x3e88f2ae, 0x3e73039d,
  0x3e5d1222, 0x3e471e41, 0x3e3127f9, 0x3e1b2f4a, 0x3e053437, 0x3def36c0,
  0x3dd936e6, 0x3dc334a9,
  0x3dad300b, 0x3d97290b, 0x3d811fac, 0x3d6b13ee, 0x3d5505d2, 0x3d3ef559,
  0x3d28e282, 0x3d12cd51,
  0x3cfcb5c4, 0x3ce69bde, 0x3cd07f9f, 0x3cba6107, 0x3ca44018, 0x3c8e1cd3,
  0x3c77f737, 0x3c61cf48,
  0x3c4ba504, 0x3c35786d, 0x3c1f4983, 0x3c091849, 0x3bf2e4be, 0x3bdcaee3,
  0x3bc676b9, 0x3bb03c42,
  0x3b99ff7d, 0x3b83c06c, 0x3b6d7f10, 0x3b573b69, 0x3b40f579, 0x3b2aad3f,
  0x3b1462be, 0x3afe15f6,
  0x3ae7c6e7, 0x3ad17593, 0x3abb21fb, 0x3aa4cc1e, 0x3a8e7400, 0x3a78199f,
  0x3a61bcfd, 0x3a4b5e1b,
  0x3a34fcf9, 0x3a1e9999, 0x3a0833fc, 0x39f1cc21, 0x39db620b, 0x39c4f5ba,
  0x39ae872f, 0x3998166a,
  0x3981a36d, 0x396b2e38, 0x3954b6cd, 0x393e3d2c, 0x3927c155, 0x3911434b,
  0x38fac30e, 0x38e4409e,
  0x38cdbbfc, 0x38b7352a, 0x38a0ac29, 0x388a20f8, 0x38739399, 0x385d040d,
  0x38467255, 0x382fde72,
  0x38194864, 0x3802b02c, 0x37ec15cb, 0x37d57943, 0x37beda93, 0x37a839be,
  0x379196c3, 0x377af1a3,
  0x37644a60, 0x374da0fa, 0x3736f573, 0x372047ca, 0x37099802, 0x36f2e61a,
  0x36dc3214, 0x36c57bf0,
  0x36aec3b0, 0x36980954, 0x36814cde, 0x366a8e4d, 0x3653cda3, 0x363d0ae2,
  0x36264609, 0x360f7f19,
  0x35f8b614, 0x35e1eafa, 0x35cb1dcc, 0x35b44e8c, 0x359d7d39, 0x3586a9d5,
  0x356fd461, 0x3558fcde,
  0x3542234c, 0x352b47ad, 0x35146a00, 0x34fd8a48, 0x34e6a885, 0x34cfc4b7,
  0x34b8dee1, 0x34a1f702,
  0x348b0d1c, 0x3474212f, 0x345d333c, 0x34464345, 0x342f5149, 0x34185d4b,
  0x3401674a, 0x33ea6f48,
  0x33d37546, 0x33bc7944, 0x33a57b44, 0x338e7b46, 0x3377794b, 0x33607554,
  0x33496f62, 0x33326776,
  0x331b5d91, 0x330451b3, 0x32ed43de, 0x32d63412, 0x32bf2250, 0x32a80e99,
  0x3290f8ef, 0x3279e151,
  0x3262c7c1, 0x324bac40, 0x32348ecf, 0x321d6f6e, 0x32064e1e, 0x31ef2ae1,
  0x31d805b7, 0x31c0dea1,
  0x31a9b5a0, 0x31928ab4, 0x317b5de0, 0x31642f23, 0x314cfe7f, 0x3135cbf4,
  0x311e9783, 0x3107612e,
  0x30f028f4, 0x30d8eed8, 0x30c1b2da, 0x30aa74fa, 0x3093353a, 0x307bf39b,
  0x3064b01d, 0x304d6ac1,
  0x30362389, 0x301eda75, 0x30078f86, 0x2ff042bd, 0x2fd8f41b, 0x2fc1a3a0,
  0x2faa514f, 0x2f92fd26,
  0x2f7ba729, 0x2f644f56, 0x2f4cf5b0, 0x2f359a37, 0x2f1e3ced, 0x2f06ddd1,
  0x2eef7ce5, 0x2ed81a29,
  0x2ec0b5a0, 0x2ea94f49, 0x2e91e725, 0x2e7a7d36, 0x2e63117c, 0x2e4ba3f8,
  0x2e3434ac, 0x2e1cc397,
  0x2e0550bb, 0x2deddc19, 0x2dd665b2, 0x2dbeed86, 0x2da77397, 0x2d8ff7e5,
  0x2d787a72, 0x2d60fb3e,
  0x2d497a4a, 0x2d31f797, 0x2d1a7325, 0x2d02ecf7, 0x2ceb650d, 0x2cd3db67,
  0x2cbc5006, 0x2ca4c2ed,
  0x2c8d341a, 0x2c75a390, 0x2c5e114f, 0x2c467d58, 0x2c2ee7ad, 0x2c17504d,
  0x2bffb73a, 0x2be81c74,
  0x2bd07ffe, 0x2bb8e1d7, 0x2ba14200, 0x2b89a07b, 0x2b71fd48, 0x2b5a5868,
  0x2b42b1dd, 0x2b2b09a6,
  0x2b135fc6, 0x2afbb43c, 0x2ae4070a, 0x2acc5831, 0x2ab4a7b1, 0x2a9cf58c,
  0x2a8541c3, 0x2a6d8c55,
  0x2a55d545, 0x2a3e1c93, 0x2a266240, 0x2a0ea64d, 0x29f6e8bb, 0x29df298b,
  0x29c768be, 0x29afa654,
  0x2997e24f, 0x29801caf, 0x29685576, 0x29508ca4, 0x2938c23a, 0x2920f63a,
  0x290928a3, 0x28f15978,
  0x28d988b8, 0x28c1b666, 0x28a9e281, 0x28920d0a, 0x287a3604, 0x28625d6d,
  0x284a8349, 0x2832a796,
  0x281aca57, 0x2802eb8c, 0x27eb0b36, 0x27d32956, 0x27bb45ed, 0x27a360fc,
  0x278b7a84, 0x27739285,
  0x275ba901, 0x2743bdf9, 0x272bd16d, 0x2713e35f, 0x26fbf3ce, 0x26e402bd,
  0x26cc102d, 0x26b41c1d,
  0x269c268f, 0x26842f84, 0x266c36fe, 0x26543cfb, 0x263c417f, 0x26244489,
  0x260c461b, 0x25f44635,
  0x25dc44d9, 0x25c44207, 0x25ac3dc0, 0x25943806, 0x257c30d8, 0x25642839,
  0x254c1e28, 0x253412a8,
  0x251c05b8, 0x2503f75a, 0x24ebe78f, 0x24d3d657, 0x24bbc3b4, 0x24a3afa6,
  0x248b9a2f, 0x2473834f,
  0x245b6b07, 0x24435158, 0x242b3644, 0x241319ca, 0x23fafbec, 0x23e2dcac,
  0x23cabc09, 0x23b29a05,
  0x239a76a0, 0x238251dd, 0x236a2bba, 0x2352043b, 0x2339db5e, 0x2321b126,
  0x23098593, 0x22f158a7,
  0x22d92a61, 0x22c0fac4, 0x22a8c9cf, 0x22909785, 0x227863e5, 0x22602ef1,
  0x2247f8aa, 0x222fc111,
  0x22178826, 0x21ff4dea, 0x21e71260, 0x21ced586, 0x21b6975f, 0x219e57eb,
  0x2186172b, 0x216dd521,
  0x215591cc, 0x213d4d2f, 0x21250749, 0x210cc01d, 0x20f477aa, 0x20dc2df2,
  0x20c3e2f5, 0x20ab96b5,
  0x20934933, 0x207afa6f, 0x2062aa6b, 0x204a5927, 0x203206a4, 0x2019b2e4,
  0x20015de7, 0x1fe907ae,
  0x1fd0b03a, 0x1fb8578b, 0x1f9ffda4, 0x1f87a285, 0x1f6f462f, 0x1f56e8a2,
  0x1f3e89e0, 0x1f2629ea,
  0x1f0dc8c0, 0x1ef56664, 0x1edd02d6, 0x1ec49e17, 0x1eac3829, 0x1e93d10c,
  0x1e7b68c2, 0x1e62ff4a,
  0x1e4a94a7, 0x1e3228d9, 0x1e19bbe0, 0x1e014dbf, 0x1de8de75, 0x1dd06e04,
  0x1db7fc6d, 0x1d9f89b1,
  0x1d8715d0, 0x1d6ea0cc, 0x1d562aa6, 0x1d3db35e, 0x1d253af5, 0x1d0cc16c,
  0x1cf446c5, 0x1cdbcb00,
  0x1cc34e1f, 0x1caad021, 0x1c925109, 0x1c79d0d6, 0x1c614f8b, 0x1c48cd27,
  0x1c3049ac, 0x1c17c51b,
  0x1bff3f75, 0x1be6b8ba, 0x1bce30ec, 0x1bb5a80c, 0x1b9d1e1a, 0x1b849317,
  0x1b6c0705, 0x1b5379e5,
  0x1b3aebb6, 0x1b225c7b, 0x1b09cc34, 0x1af13ae3, 0x1ad8a887, 0x1ac01522,
  0x1aa780b6, 0x1a8eeb42,
  0x1a7654c8, 0x1a5dbd49, 0x1a4524c6, 0x1a2c8b3f, 0x1a13f0b6, 0x19fb552c,
  0x19e2b8a2, 0x19ca1b17,
  0x19b17c8f, 0x1998dd09, 0x19803c86, 0x19679b07, 0x194ef88e, 0x1936551b,
  0x191db0af, 0x19050b4b,
  0x18ec64f0, 0x18d3bda0, 0x18bb155a, 0x18a26c20, 0x1889c1f3, 0x187116d4,
  0x18586ac3, 0x183fbdc3,
  0x18270fd3, 0x180e60f4, 0x17f5b129, 0x17dd0070, 0x17c44ecd, 0x17ab9c3e,
  0x1792e8c6, 0x177a3466,
  0x17617f1d, 0x1748c8ee, 0x173011d9, 0x171759df, 0x16fea102, 0x16e5e741,
  0x16cd2c9f, 0x16b4711b,
  0x169bb4b7, 0x1682f774, 0x166a3953, 0x16517a55, 0x1638ba7a, 0x161ff9c4,
  0x16073834, 0x15ee75cb,
  0x15d5b288, 0x15bcee6f, 0x15a4297f, 0x158b63b9, 0x15729d1f, 0x1559d5b1,
  0x15410d70, 0x1528445d,
  0x150f7a7a, 0x14f6afc7, 0x14dde445, 0x14c517f4, 0x14ac4ad7, 0x14937cee,
  0x147aae3a, 0x1461debc,
  0x14490e74, 0x14303d65, 0x14176b8e, 0x13fe98f1, 0x13e5c58e, 0x13ccf167,
  0x13b41c7d, 0x139b46d0,
  0x13827062, 0x13699933, 0x1350c144, 0x1337e897, 0x131f0f2c, 0x13063505,
  0x12ed5a21, 0x12d47e83,
  0x12bba22b, 0x12a2c51b, 0x1289e752, 0x127108d2, 0x1258299c, 0x123f49b2,
  0x12266913, 0x120d87c1,
  0x11f4a5bd, 0x11dbc307, 0x11c2dfa2, 0x11a9fb8d, 0x119116c9, 0x11783159,
  0x115f4b3c, 0x11466473,
  0x112d7d00, 0x111494e4, 0x10fbac1e, 0x10e2c2b2, 0x10c9d89e, 0x10b0ede5,
  0x10980287, 0x107f1686,
  0x106629e1, 0x104d3c9b, 0x10344eb4, 0x101b602d, 0x10027107, 0xfe98143,
  0xfd090e1, 0xfb79fe4,
  0xf9eae4c, 0xf85bc19, 0xf6cc94e, 0xf53d5ea, 0xf3ae1ee, 0xf21ed5d, 0xf08f836,
  0xef0027b,
  0xed70c2c, 0xebe154b, 0xea51dd8, 0xe8c25d5, 0xe732d42, 0xe5a3421, 0xe413a72,
  0xe284036,
  0xe0f456f, 0xdf64a1c, 0xddd4e40, 0xdc451dc, 0xdab54ef, 0xd92577b, 0xd795982,
  0xd605b03,
  0xd475c00, 0xd2e5c7b, 0xd155c73, 0xcfc5bea, 0xce35ae1, 0xcca5959, 0xcb15752,
  0xc9854cf,
  0xc7f51cf, 0xc664e53, 0xc4d4a5d, 0xc3445ee, 0xc1b4107, 0xc023ba7, 0xbe935d2,
  0xbd02f87,
  0xbb728c7, 0xb9e2193, 0xb8519ed, 0xb6c11d5, 0xb53094d, 0xb3a0055, 0xb20f6ee,
  0xb07ed19,
  0xaeee2d7, 0xad5d829, 0xabccd11, 0xaa3c18e, 0xa8ab5a2, 0xa71a94f, 0xa589c94,
  0xa3f8f73,
  0xa2681ed, 0xa0d7403, 0x9f465b5, 0x9db5706, 0x9c247f5, 0x9a93884, 0x99028b3,
  0x9771884,
  0x95e07f8, 0x944f70f, 0x92be5ca, 0x912d42c, 0x8f9c233, 0x8e0afe2, 0x8c79d3a,
  0x8ae8a3a,
  0x89576e5, 0x87c633c, 0x8634f3e, 0x84a3aee, 0x831264c, 0x8181159, 0x7fefc16,
  0x7e5e685,
  0x7ccd0a5, 0x7b3ba78, 0x79aa400, 0x7818d3c, 0x768762e, 0x74f5ed7, 0x7364738,
  0x71d2f52,
  0x7041726, 0x6eafeb4, 0x6d1e5fe, 0x6b8cd05, 0x69fb3c9, 0x6869a4c, 0x66d808f,
  0x6546692,
  0x63b4c57, 0x62231de, 0x6091729, 0x5effc38, 0x5d6e10c, 0x5bdc5a7, 0x5a4aa09,
  0x58b8e34,
  0x5727228, 0x55955e6, 0x540396f, 0x5271cc4, 0x50dffe7, 0x4f4e2d8, 0x4dbc597,
  0x4c2a827,
  0x4a98a88, 0x4906cbb, 0x4774ec1, 0x45e309a, 0x4451249, 0x42bf3cd, 0x412d528,
  0x3f9b65b,
  0x3e09767, 0x3c7784d, 0x3ae590d, 0x39539a9, 0x37c1a22, 0x362fa78, 0x349daac,
  0x330bac1,
  0x3179ab5, 0x2fe7a8c, 0x2e55a44, 0x2cc39e1, 0x2b31961, 0x299f8c7, 0x280d813,
  0x267b747,
  0x24e9662, 0x2357567, 0x21c5457, 0x2033331, 0x1ea11f7, 0x1d0f0ab, 0x1b7cf4d,
  0x19eaddd,
  0x1858c5e, 0x16c6ad0, 0x1534934, 0x13a278a, 0x12105d5, 0x107e414, 0xeec249,
  0xd5a075,
  0xbc7e99, 0xa35cb5, 0x8a3acb, 0x7118dc, 0x57f6e9, 0x3ed4f2, 0x25b2f8,
  0xc90fe,

};

/**   
 * @brief  Initialization function for the Q31 DCT4/IDCT4.  
 * @param[in,out] *S         points to an instance of Q31 DCT4/IDCT4 structure.  
 * @param[in]     *S_RFFT    points to an instance of Q31 RFFT/RIFFT structure  
 * @param[in]     *S_CFFT    points to an instance of Q31 CFFT/CIFFT structure  
 * @param[in]     N          length of the DCT4.  
 * @param[in]     Nby2       half of the length of the DCT4.  
 * @param[in]     normalize  normalizing factor.  
 * @return		  arm_status function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>N</code> is not a supported transform length.  
 * \par Normalizing factor:   
 * The normalizing factor is <code>sqrt(2/N)</code>, which depends on the size of transform <code>N</code>.   
 * Normalizing factors in 1.31 format are mentioned in the table below for different DCT sizes:   
 * \image html dct4NormalizingQ31Table.gif   
 */

arm_status arm_dct4_init_q31(
  arm_dct4_instance_q31 * S,
  arm_rfft_instance_q31 * S_RFFT,
  arm_cfft_radix4_instance_q31 * S_CFFT,
  uint16_t N,
  uint16_t Nby2,
  q31_t normalize)
{
  /*  Initialise the default arm status */
  arm_status status = ARM_MATH_SUCCESS;

  /* Initializing the pointer array with the weight table base addresses of different lengths */
  q31_t *twiddlePtr[3] = { (q31_t *) WeightsQ31_128, (q31_t *) WeightsQ31_512,
    (q31_t *) WeightsQ31_2048
  };

  /* Initializing the pointer array with the cos factor table base addresses of different lengths */
  q31_t *pCosFactor[3] =
    { (q31_t *) cos_factorsQ31_128, (q31_t *) cos_factorsQ31_512,
    (q31_t *) cos_factorsQ31_2048
  };

  /* Initialize the DCT4 length */
  S->N = N;

  /* Initialize the half of DCT4 length */
  S->Nby2 = Nby2;

  /* Initialize the DCT4 Normalizing factor */
  S->normalize = normalize;

  /* Initialize Real FFT Instance */
  S->pRfft = S_RFFT;

  /* Initialize Complex FFT Instance */
  S->pCfft = S_CFFT;

  switch (N)
  {
    /* Initialize the table modifier values */
  case 2048u:
    S->pTwiddle = twiddlePtr[2];
    S->pCosFactor = pCosFactor[2];
    break;
  case 512u:
    S->pTwiddle = twiddlePtr[1];
    S->pCosFactor = pCosFactor[1];
    break;
  case 128u:
    S->pTwiddle = twiddlePtr[0];
    S->pCosFactor = pCosFactor[0];
    break;
  default:
    status = ARM_MATH_ARGUMENT_ERROR;
  }

  /* Initialize the RFFT/RIFFT Function */
  arm_rfft_init_q31(S->pRfft, S->pCfft, S->N, 0, 1);

  /* return the status of DCT4 Init function */
  return (status);
}

/**   
   * @} end of DCT4_IDCT4 group   
   */
