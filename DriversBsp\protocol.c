#include "SysConfig.h"
#include "stm32f4xx.h"                  // Device header
#include <stdio.h>
#include <string.h>


// ========== 协议示例 ==========
/*
示例协议帧：

1. 普通数据: AA FF 01 12 34 EA
   - AA FF: 帧头
   - 01: CMD_NORMAL_DATA 命令
   - 12 34: 2字节数据
   - EA: 帧尾

2. 坐标数据: AA FF 02 78 56 34 12 EA  
   - AA FF: 帧头
   - 02: CMD_COORDINATE 命令
   - 78 56 34 12: 4字节坐标数据 (x=5678, y=1234)
   - EA: 帧尾

3. 单字节: AA FF 03 AB EA
   - AA FF: 帧头
   - 03: CMD_SINGLE_BYTE 命令
   - AB: 1字节数据
   - EA: 帧尾
*/


// 串口屏命令结束符
#define NEXTION_END_CMD    "\xFF\xFF\xFF"
// 协议定义
#define FRAME_HEAD_1    0xAA    // 帧头1
#define FRAME_HEAD_2    0xFF    // 帧头2 (固定)
#define FRAME_TAIL      0xEA    // 帧尾

// 命令定义 (cmd字节)
#define CMD_NORMAL_DATA     0x01    // 三个禁飞区位置
#define CMD_COORDINATE      0x02    // 起飞
#define CMD_SINGLE_BYTE     0x03    // 位置 种类 象：01 虎：02 狼：03 猴：04 孔雀：05  数量
#define CMD_STRING_DATA     0x04    // 
// 可以继续添加更多命令...

// 全局变量

Point point;     // 动态分配的点数组
Send send;
u16 current_max_count = 0;     // 当前数组大小
u8 fly = 0,get_now = 0,all_get =0;
protocol_rx_t g_protocol;

#define MAX_POSSIBLE_POINTS 1000
Point static_point_array[MAX_POSSIBLE_POINTS];

#define MAX_POSITIONS 100
#define MAX_ANIMALS 6
int position_animal_count[MAX_POSITIONS][MAX_ANIMALS];
uint8_t position_completed[62];  
int animal_total_count[6];
/**
 * @brief 根据命令字节获取数据长度
 * @param cmd 命令字节
 * @return 对应的数据长度
 */
uint8_t get_data_length_by_cmd(uint8_t cmd)
{
    switch(cmd)
    {
        case CMD_NORMAL_DATA:   return 3;   // 普通数据 - 2字节
        case CMD_COORDINATE:    return 1;   // 坐标数据 - 4字节
        case CMD_SINGLE_BYTE:   return 3;   // 单字节数据 - 1字节
        case CMD_STRING_DATA:   return 2;   // 字符串数据 - 8字节
        // 添加更多命令的数据长度定义...
        
        default: return 0;  // 未知命令，无数据
    }
}

/**
 * @brief 重置协议状态
 */
void protocol_reset(void)
{
    g_protocol.state = WAIT_HEAD1;
    g_protocol.data_cnt = 0;
    g_protocol.data_len = 0;
    g_protocol.cmd = 0;
}

/**
 * @brief 处理完整的协议帧
 */
void protocol_process_frame(void)
{
    switch(g_protocol.cmd)
    {
        case CMD_NORMAL_DATA:  // 普通数据命令 - 6字节数据
        {
            if(g_protocol.data_cnt >= 3)
            {
							point.barrier1 = g_protocol.data_buf[0];
							point.barrier2 = g_protocol.data_buf[1];
							point.barrier3 = g_protocol.data_buf[2];
							
							send_01_flag = 1;
							time_1s = 1;
            }
            break;
        }
        
        case CMD_COORDINATE:  // 坐标数据命令 - 8字节数据  
        {
            if(g_protocol.data_cnt >= 1)
            {
								fly = g_protocol.data_buf[0];
            }
            break;
        }
        
        case CMD_SINGLE_BYTE:  
        {
            if(g_protocol.data_cnt >= 3)
            {
							send.position = g_protocol.data_buf[0];
							send.animal = g_protocol.data_buf[1];
							send.num = g_protocol.data_buf[2];
							
							if(send.position!=send.last_position)
							{
								
            // 只有当上一个位置还没被统计过时才累加
            if(!position_completed[send.last_position])
            {
								animal_total_count[1] += position_animal_count[send.last_position][1];
								animal_total_count[2] += position_animal_count[send.last_position][2];
								animal_total_count[3] += position_animal_count[send.last_position][3];
								animal_total_count[4] += position_animal_count[send.last_position][4];
								animal_total_count[5] += position_animal_count[send.last_position][5];
							position_completed[send.last_position] = 1;
						}
							}
							position_animal_count[send.position][send.animal] = send.num;
							send.last_position = send.position;  // 更新last_position
							get_now = 1;
							all_get = 1;
							
            }
            break;
        }
        
        case CMD_STRING_DATA:  // 字符串数据命令
        {

            if(g_protocol.data_cnt >= 2)
            {
								animal_total_count[1] += position_animal_count[send.last_position][1];
								animal_total_count[2] += position_animal_count[send.last_position][2];
								animal_total_count[3] += position_animal_count[send.last_position][3];
								animal_total_count[4] += position_animal_count[send.last_position][4];
								animal_total_count[5] += position_animal_count[send.last_position][5];
								all_get = 1;
            }

            break;
        }
        
        default:
        {
            // 未知命令
            // printf("未知命令: %02X\n", g_protocol.cmd);
            break;
        }
    }
}

/**
 * @brief 协议接收处理函数 - 核心函数
 * @param data 接收到的字节
 */
void protocol_receive_byte(uint8_t data)
{
    switch(g_protocol.state)
    {
        case WAIT_HEAD1:    // 等待帧头1
        {
            if(data == FRAME_HEAD_1)  // 收到 0xAA
            {
                g_protocol.state = WAIT_HEAD2;
            }
            // 其他字节忽略，继续等待
            break;
        }
        
        case WAIT_HEAD2:    // 等待帧头2
        {
            if(data == FRAME_HEAD_2)  // 收到 0xFF
            {
                g_protocol.state = WAIT_CMD;
            }
            else
            {
                // 无效的帧头2，重新开始
                protocol_reset();
            }
            break;
        }
        
        case WAIT_CMD:      // 等待命令字节
        {
            g_protocol.cmd = data;
            g_protocol.data_len = get_data_length_by_cmd(data);
            g_protocol.data_cnt = 0;
            
            if(g_protocol.data_len > 0)
            {
                g_protocol.state = RECV_DATA;
            }
            else
            {
                // 无数据的命令，直接等待帧尾
                g_protocol.state = WAIT_TAIL;
            }
            break;
        }
        
        case RECV_DATA:     // 接收数据
        {
            // 保存数据到缓冲区
            g_protocol.data_buf[g_protocol.data_cnt] = data;
            g_protocol.data_cnt++;
            
            // 检查是否收集够了数据
            if(g_protocol.data_cnt >= g_protocol.data_len)
            {
                g_protocol.state = WAIT_TAIL;
            }
            
            // 防止缓冲区溢出
            if(g_protocol.data_cnt >= sizeof(g_protocol.data_buf))
            {
                protocol_reset();
            }
            break;
        }
        
        case WAIT_TAIL:     // 等待帧尾
        {
            if(data == FRAME_TAIL)  // 收到 0xEA
            {
                // 协议帧接收完成，处理数据
                protocol_process_frame();
            }
            
            // 无论帧尾对不对，都重新开始等待下一帧
            protocol_reset();
						
            break;
        }
        
        default:
        {
            protocol_reset();
            break;
        }
    }
}











u8 decimal_to_bcd(u8 decimal_num)
{
    u8 tens = decimal_num / 10;      // 十位数字
    u8 units = decimal_num % 10;     // 个位数字
    
    // 增加范围检查，确保是合法的BCD值（0-99）
    if(tens > 9 || units > 9)
        return 0xFF; // 非法值返回错误标志
        
    return (tens << 4) | units;      // 十位作为高4位,个位作为低4位
}


void usart_send01(u8 data1,u8 data2,u8 data3)   //int 四个字节
{
  static u8 shuju[100];
	shuju[0] = 0xAA;
	shuju[1] = 0xFF;
	
	shuju[2] = 01;//通过BYTE拆分成四个单字节发送
	
	shuju[3] = data1;
	
	shuju[4] = data2;
	
	shuju[5] = data3;
	
	shuju[6] = 0xEA;
  DrvUart1SendBuf(shuju,7);
}

void usart_send02(void)   //int 四个字节
{
  static u8 shuju[100];
	shuju[0] = 0xAA;
	shuju[1] = 0xFF;
	
	shuju[2] = 0x02;//通过BYTE拆分成四个单字节发送
	
	shuju[3] = 0x01;
	
	
	shuju[4] = 0xEA;
  DrvUart1SendBuf(shuju,5);
}



// STM32发送到串口屏  位置 id 信息（使用USART2）
void Send_pandian(uint16_t location,uint16_t id,uint16_t num)
{
    char cmd[100];

	// 数据类型标识：1表示发送的是 实时显示
    sprintf(cmd, "sys5=%d%s", 1, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
		MyDelayMs(10);

    // 发送x
    sprintf(cmd, "sys6=%d%s", (int)location, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
		MyDelayMs(10);

    // 发送y
    sprintf(cmd, "sys7=%d%s", (int)id, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
		MyDelayMs(10);

	      // 发送y
    sprintf(cmd, "sys8=%d%s", (int)num, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
		MyDelayMs(10);

    // 触发处理 
    sprintf(cmd, "receive_flag=%d%s", 1, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));

}


void Send_zong(uint16_t animal1,uint16_t animal2,uint16_t animal3,uint16_t animal4,uint16_t animal5)
{
    char cmd[100];
    
	// 数据类型标识：1表示发送的是 实时显示
    sprintf(cmd, "sy5=%d%s", 2, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
    MyDelayMs(10);
    
    // 发送x
    sprintf(cmd, "sy6=%d%s", (int)animal1, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
    //MyDelayMs(10);

    // 发送y
    sprintf(cmd, "sy7=%d%s", (int)animal2, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
    MyDelayMs(10);
	  
	      // 发送y
    sprintf(cmd, "sy8=%d%s", (int)animal3, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
    //MyDelayMs(10);
		
    sprintf(cmd, "sys10=%d%s", (int)animal4, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
    MyDelayMs(10);
		
    sprintf(cmd, "sys11=%d%s", (int)animal5, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
    MyDelayMs(10);
    // 触发处理 
    sprintf(cmd, "receive_flag=%d%s", 1, NEXTION_END_CMD);
    DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));

}




		void no_animal(void)
		{		
				char cmd[100];
				sprintf(cmd, "receive_flag=%d%s", 2, NEXTION_END_CMD);
				DrvUart5SendBuf((unsigned char*)cmd, strlen(cmd));
				MyDelayMs(1);

		}



