Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to drv_bsp.o(i.All_Init) for All_Init
    main.o(i.main) refers to ano_scheduler.o(i.Scheduler_Setup) for Scheduler_Setup
    main.o(i.main) refers to ano_scheduler.o(i.Scheduler_Run) for Scheduler_Run
    ano_scheduler.o(i.Loop_50Hz) refers to user_task.o(i.UserTask_OneKeyCmd) for UserTask_OneKeyCmd
    ano_scheduler.o(i.Scheduler_Run) refers to drv_sys.o(i.GetSysRunTimeMs) for GetSysRunTimeMs
    ano_scheduler.o(i.Scheduler_Run) refers to ano_scheduler.o(.data) for sched_tasks
    ano_scheduler.o(i.Scheduler_Setup) refers to ano_scheduler.o(.data) for sched_tasks
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_1000Hz) for Loop_1000Hz
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_500Hz) for Loop_500Hz
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_200Hz) for Loop_200Hz
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_100Hz) for Loop_100Hz
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_50Hz) for Loop_50Hz
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_20Hz) for Loop_20Hz
    ano_scheduler.o(.data) refers to ano_scheduler.o(i.Loop_2Hz) for Loop_2Hz
    user_task.o(i.UserTask_OneKeyCmd) refers to lx_fc_fun.o(i.OneKey_Takeoff) for OneKey_Takeoff
    user_task.o(i.UserTask_OneKeyCmd) refers to lx_fc_fun.o(i.OneKey_Land) for OneKey_Land
    user_task.o(i.UserTask_OneKeyCmd) refers to drv_bsp.o(.bss) for rc_in
    user_task.o(i.UserTask_OneKeyCmd) refers to user_task.o(.data) for one_key_takeoff_f
    ano_dt_lx.o(i.ANO_DT_Init) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to ano_dt_lx.o(i.CK_Back) for CK_Back
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to ano_dt_lx.o(i.PAR_Back) for PAR_Back
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to ano_lx.o(.bss) for pwm_to_esc
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to drv_led.o(.data) for led
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to lx_fc_state.o(.bss) for fc_sta
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to ano_lx.o(.data) for fc_vel
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Prepare) refers to ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl) for ANO_DT_LX_Data_Receive_Anl
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Prepare) refers to ano_dt_lx.o(.data) for rxstate
    ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Prepare) refers to ano_dt_lx.o(.bss) for DT_RxBuffer
    ano_dt_lx.o(i.ANO_DT_LX_Send_Data) refers to drv_uart.o(i.DrvUart5SendBuf) for DrvUart5SendBuf
    ano_dt_lx.o(i.ANO_LX_Data_Exchange_Task) refers to ano_dt_lx.o(i.CK_Back_Check) for CK_Back_Check
    ano_dt_lx.o(i.ANO_LX_Data_Exchange_Task) refers to ano_dt_lx.o(i.Check_To_Send) for Check_To_Send
    ano_dt_lx.o(i.Add_Send_Data) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.Add_Send_Data) refers to ano_lx.o(.data) for fc_bat
    ano_dt_lx.o(i.Add_Send_Data) refers to lx_fc_ext_sensor.o(.bss) for ext_sens
    ano_dt_lx.o(i.Add_Send_Data) refers to drv_bsp.o(.bss) for rc_in
    ano_dt_lx.o(i.Add_Send_Data) refers to ano_lx.o(.bss) for rt_tar
    ano_dt_lx.o(i.CK_Back) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.CK_Back_Check) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.CK_Back_Check) refers to ano_dt_lx.o(.data) for time_dly
    ano_dt_lx.o(i.CMD_Send) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.Check_To_Send) refers to ano_dt_lx.o(i.Frame_Send) for Frame_Send
    ano_dt_lx.o(i.Check_To_Send) refers to ano_dt_lx.o(.bss) for dt
    ano_dt_lx.o(i.Frame_Send) refers to ano_dt_lx.o(i.Add_Send_Data) for Add_Send_Data
    ano_dt_lx.o(i.Frame_Send) refers to ano_dt_lx.o(i.ANO_DT_LX_Send_Data) for ANO_DT_LX_Send_Data
    ano_dt_lx.o(i.Frame_Send) refers to ano_dt_lx.o(.bss) for send_buffer
    ano_dt_lx.o(i.PAR_Back) refers to ano_dt_lx.o(.bss) for dt
    ano_lx.o(i.ANO_LX_Task) refers to drv_bsp.o(i.DrvRcInputTask) for DrvRcInputTask
    ano_lx.o(i.ANO_LX_Task) refers to ano_lx.o(i.RC_Data_Task) for RC_Data_Task
    ano_lx.o(i.ANO_LX_Task) refers to lx_fc_state.o(i.LX_FC_State_Task) for LX_FC_State_Task
    ano_lx.o(i.ANO_LX_Task) refers to drv_anoof.o(i.AnoOF_Check_State) for AnoOF_Check_State
    ano_lx.o(i.ANO_LX_Task) refers to ano_lx.o(i.Bat_Voltage_Data_Handle) for Bat_Voltage_Data_Handle
    ano_lx.o(i.ANO_LX_Task) refers to drv_uart.o(i.DrvUartDataCheck) for DrvUartDataCheck
    ano_lx.o(i.ANO_LX_Task) refers to drv_ubloxgps.o(i.GPS_Data_Prepare_Task) for GPS_Data_Prepare_Task
    ano_lx.o(i.ANO_LX_Task) refers to lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task) for LX_FC_EXT_Sensor_Task
    ano_lx.o(i.ANO_LX_Task) refers to ano_dt_lx.o(i.ANO_LX_Data_Exchange_Task) for ANO_LX_Data_Exchange_Task
    ano_lx.o(i.ANO_LX_Task) refers to ano_lx.o(i.ESC_Output) for ESC_Output
    ano_lx.o(i.ANO_LX_Task) refers to drv_led.o(i.LED_1ms_DRV) for LED_1ms_DRV
    ano_lx.o(i.ANO_LX_Task) refers to ano_lx.o(.data) for tmp_cnt
    ano_lx.o(i.Bat_Voltage_Data_Handle) refers to drv_adc.o(i.Drv_AdcGetBatVot) for Drv_AdcGetBatVot
    ano_lx.o(i.Bat_Voltage_Data_Handle) refers to ano_lx.o(.data) for fc_bat
    ano_lx.o(i.ESC_Output) refers to drv_pwmout.o(i.DrvMotorPWMSet) for DrvMotorPWMSet
    ano_lx.o(i.ESC_Output) refers to ano_lx.o(.bss) for pwm_to_esc
    ano_lx.o(i.ESC_Output) refers to ano_lx.o(.data) for esc_calibrated
    ano_lx.o(i.RC_Data_Task) refers to lx_fc_fun.o(i.LX_Change_Mode) for LX_Change_Mode
    ano_lx.o(i.RC_Data_Task) refers to lx_fc_fun.o(i.OneKey_Return_Home) for OneKey_Return_Home
    ano_lx.o(i.RC_Data_Task) refers to ano_math.o(i.my_deadzone) for my_deadzone
    ano_lx.o(i.RC_Data_Task) refers to drv_bsp.o(.bss) for rc_in
    ano_lx.o(i.RC_Data_Task) refers to ano_lx.o(.data) for mod_f
    ano_lx.o(i.RC_Data_Task) refers to lx_fc_state.o(.data) for sti_fun
    ano_lx.o(i.RC_Data_Task) refers to ano_lx.o(.bss) for rt_tar
    ano_lx.o(i.RC_Data_Task) refers to ano_dt_lx.o(.bss) for dt
    ano_lx.o(i.RC_Data_Task) refers to lx_fc_state.o(.bss) for fc_sta
    lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle) refers to lx_fc_ext_sensor.o(.data) for dT_ms
    lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle) refers to drv_anoof.o(.bss) for ano_of
    lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle) refers to lx_fc_ext_sensor.o(.bss) for ext_sens
    lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task) refers to lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle) for General_Velocity_Data_Handle
    lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task) refers to drv_anoof.o(.bss) for ano_of
    lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task) refers to lx_fc_ext_sensor.o(.data) for of_alt_update_cnt
    lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task) refers to lx_fc_ext_sensor.o(.bss) for ext_sens
    lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.ACC_Calibrate) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.ACC_Calibrate) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.FC_Lock) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.FC_Lock) refers to lx_fc_state.o(.bss) for fc_sta
    lx_fc_fun.o(i.FC_Lock) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.FC_Unlock) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.FC_Unlock) refers to lx_fc_state.o(.bss) for fc_sta
    lx_fc_fun.o(i.FC_Unlock) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.GYR_Calibrate) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.GYR_Calibrate) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.Horizontal_Calibrate) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.Horizontal_Calibrate) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.Horizontal_Move) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.Horizontal_Move) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.LX_Change_Mode) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.LX_Change_Mode) refers to lx_fc_fun.o(.data) for old_mode
    lx_fc_fun.o(i.LX_Change_Mode) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.LX_Change_Mode) refers to lx_fc_state.o(.bss) for fc_sta
    lx_fc_fun.o(i.Mag_Calibrate) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.Mag_Calibrate) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.OneKey_Land) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.OneKey_Land) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.OneKey_Return_Home) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.OneKey_Return_Home) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_fun.o(i.OneKey_Takeoff) refers to ano_dt_lx.o(i.CMD_Send) for CMD_Send
    lx_fc_fun.o(i.OneKey_Takeoff) refers to ano_dt_lx.o(.bss) for dt
    lx_fc_state.o(i.LX_Cali_Trig_Check) refers to lx_fc_fun.o(i.Horizontal_Calibrate) for Horizontal_Calibrate
    lx_fc_state.o(i.LX_Cali_Trig_Check) refers to lx_fc_fun.o(i.Mag_Calibrate) for Mag_Calibrate
    lx_fc_state.o(i.LX_Cali_Trig_Check) refers to lx_fc_state.o(.bss) for fc_sta
    lx_fc_state.o(i.LX_Cali_Trig_Check) refers to drv_bsp.o(.bss) for rc_in
    lx_fc_state.o(i.LX_Cali_Trig_Check) refers to lx_fc_state.o(.data) for cali_f
    lx_fc_state.o(i.LX_FC_State_Task) refers to lx_fc_state.o(i.LX_Unlock_Lock_Check) for LX_Unlock_Lock_Check
    lx_fc_state.o(i.LX_FC_State_Task) refers to lx_fc_state.o(i.LX_Cali_Trig_Check) for LX_Cali_Trig_Check
    lx_fc_state.o(i.LX_FC_State_Task) refers to drv_bsp.o(.bss) for rc_in
    lx_fc_state.o(i.LX_Unlock_Lock_Check) refers to lx_fc_fun.o(i.FC_Unlock) for FC_Unlock
    lx_fc_state.o(i.LX_Unlock_Lock_Check) refers to lx_fc_fun.o(i.FC_Lock) for FC_Lock
    lx_fc_state.o(i.LX_Unlock_Lock_Check) refers to drv_bsp.o(.bss) for rc_in
    lx_fc_state.o(i.LX_Unlock_Lock_Check) refers to lx_fc_state.o(.data) for sti_fun
    lx_fc_state.o(i.LX_Unlock_Lock_Check) refers to lx_fc_state.o(.bss) for fc_sta
    drv_bsp.o(i.All_Init) refers to drv_sys.o(i.DrvSysInit) for DrvSysInit
    drv_bsp.o(i.All_Init) refers to drv_sys.o(i.MyDelayMs) for MyDelayMs
    drv_bsp.o(i.All_Init) refers to drv_led.o(i.DvrLedInit) for DvrLedInit
    drv_bsp.o(i.All_Init) refers to drv_pwmout.o(i.DrvPwmOutInit) for DrvPwmOutInit
    drv_bsp.o(i.All_Init) refers to drv_uart.o(i.DrvUart2Init) for DrvUart2Init
    drv_bsp.o(i.All_Init) refers to drv_uart.o(i.DrvUart3Init) for DrvUart3Init
    drv_bsp.o(i.All_Init) refers to drv_uart.o(i.DrvUart4Init) for DrvUart4Init
    drv_bsp.o(i.All_Init) refers to drv_uart.o(i.DrvUart5Init) for DrvUart5Init
    drv_bsp.o(i.All_Init) refers to drv_bsp.o(i.DrvRcInputInit) for DrvRcInputInit
    drv_bsp.o(i.All_Init) refers to drv_adc.o(i.DrvAdcInit) for DrvAdcInit
    drv_bsp.o(i.All_Init) refers to ano_dt_lx.o(i.ANO_DT_Init) for ANO_DT_Init
    drv_bsp.o(i.All_Init) refers to drv_ubloxgps.o(i.Init_GPS) for Init_GPS
    drv_bsp.o(i.All_Init) refers to drv_timer.o(i.DrvTimerFcInit) for DrvTimerFcInit
    drv_bsp.o(i.DrvPpmGetOneCh) refers to drv_bsp.o(.data) for ch_sta
    drv_bsp.o(i.DrvPpmGetOneCh) refers to drv_bsp.o(.bss) for rc_in
    drv_bsp.o(i.DrvRcInputInit) refers to drv_rcin.o(i.DrvRcPpmInit) for DrvRcPpmInit
    drv_bsp.o(i.DrvRcInputInit) refers to drv_bsp.o(.bss) for rc_in
    drv_bsp.o(i.DrvRcInputTask) refers to drv_bsp.o(i.rcSignalCheck) for rcSignalCheck
    drv_bsp.o(i.DrvRcInputTask) refers to drv_bsp.o(.bss) for rc_in
    drv_bsp.o(i.DrvRcInputTask) refers to drv_bsp.o(.data) for failsafe
    drv_bsp.o(i.DrvSbusGetOneByte) refers to drv_sys.o(i.GetSysRunTimeUs) for GetSysRunTimeUs
    drv_bsp.o(i.DrvSbusGetOneByte) refers to drv_bsp.o(.data) for sbus_time
    drv_bsp.o(i.DrvSbusGetOneByte) refers to drv_bsp.o(.bss) for datatmp
    drv_bsp.o(i.rcSignalCheck) refers to drv_rcin.o(i.DrvRcSbusInit) for DrvRcSbusInit
    drv_bsp.o(i.rcSignalCheck) refers to drv_rcin.o(i.DrvRcPpmInit) for DrvRcPpmInit
    drv_bsp.o(i.rcSignalCheck) refers to drv_bsp.o(.data) for time_dly
    drv_bsp.o(i.rcSignalCheck) refers to drv_bsp.o(.bss) for rc_in
    ano_math.o(i.To_180_degrees_db) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ano_math.o(i.To_180_degrees_db) refers to dadd.o(.text) for __aeabi_dsub
    ano_math.o(i.To_180_degrees_db) refers to cdcmple.o(.text) for __aeabi_cdcmple
    ano_math.o(i.fast_atan2) refers to ano_math.o(i.my_abs) for my_abs
    ano_math.o(i.fast_atan2) refers to ano_math.o(.constdata) for fast_atan_table
    ano_math.o(i.length_limit) refers to ano_math.o(i.my_sqrt) for my_sqrt
    ano_math.o(i.mx_sin) refers to cdcmple.o(.text) for __aeabi_cdcmple
    ano_math.o(i.mx_sin) refers to dmul.o(.text) for __aeabi_dmul
    ano_math.o(i.mx_sin) refers to dadd.o(.text) for __aeabi_dadd
    ano_math.o(i.my_atan) refers to ano_math.o(i.fast_atan2) for fast_atan2
    ano_math.o(i.my_cos) refers to dadd.o(.text) for __aeabi_dadd
    ano_math.o(i.my_cos) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ano_math.o(i.my_cos) refers to dflti.o(.text) for __aeabi_i2d
    ano_math.o(i.my_cos) refers to ano_math.o(i.my_sin) for my_sin
    ano_math.o(i.my_cos) refers to dmul.o(.text) for __aeabi_dmul
    ano_math.o(i.my_cos) refers to d2f.o(.text) for __aeabi_d2f
    ano_math.o(i.my_sin) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ano_math.o(i.my_sin) refers to dadd.o(.text) for __aeabi_dsub
    ano_math.o(i.my_sin) refers to dflti.o(.text) for __aeabi_i2d
    ano_math.o(i.my_sin) refers to ano_math.o(i.mx_sin) for mx_sin
    ano_math.o(i.my_sin) refers to dmul.o(.text) for __aeabi_dmul
    ano_math.o(i.my_sqrt) refers to ano_math.o(i.my_sqrt_reciprocal) for my_sqrt_reciprocal
    ano_math.o(i.rot_vec_2) refers to ano_math.o(i.my_sqrt) for my_sqrt
    drv_anoof.o(i.AnoOF_Check_State) refers to drv_anoof.o(.bss) for check_time_ms
    drv_anoof.o(i.AnoOF_DataAnl) refers to drv_anoof.o(.bss) for ano_of
    drv_anoof.o(i.AnoOF_GetOneByte) refers to drv_anoof.o(i.AnoOF_DataAnl) for AnoOF_DataAnl
    drv_anoof.o(i.AnoOF_GetOneByte) refers to drv_anoof.o(.data) for rxstate
    drv_anoof.o(i.AnoOF_GetOneByte) refers to drv_anoof.o(.bss) for _datatemp
    drv_ubloxgps.o(i.GPS_Data_Prepare_Task) refers to drv_ubloxgps.o(.data) for pvt_receive_updata
    drv_ubloxgps.o(i.GPS_Data_Prepare_Task) refers to drv_ubloxgps.o(.bss) for ubx
    drv_ubloxgps.o(i.GPS_Data_Prepare_Task) refers to lx_fc_ext_sensor.o(.bss) for ext_sens
    drv_ubloxgps.o(i.GPS_Data_Prepare_Task) refers to ano_dt_lx.o(.bss) for dt
    drv_ubloxgps.o(i.GPS_Rate_H) refers to drv_uart.o(i.DrvUart1SendBuf) for DrvUart1SendBuf
    drv_ubloxgps.o(i.GPS_Rate_H) refers to drv_ubloxgps.o(.data) for Period_Out_H
    drv_ubloxgps.o(i.GPS_Rate_L) refers to drv_uart.o(i.DrvUart1SendBuf) for DrvUart1SendBuf
    drv_ubloxgps.o(i.GPS_Rate_L) refers to drv_ubloxgps.o(.data) for Period_Out_L
    drv_ubloxgps.o(i.Init_GPS) refers to drv_sys.o(i.MyDelayMs) for MyDelayMs
    drv_ubloxgps.o(i.Init_GPS) refers to drv_uart.o(i.DrvUart1Init) for DrvUart1Init
    drv_ubloxgps.o(i.Init_GPS) refers to drv_uart.o(i.DrvUart1SendBuf) for DrvUart1SendBuf
    drv_ubloxgps.o(i.Init_GPS) refers to drv_ubloxgps.o(i.GPS_Rate_H) for GPS_Rate_H
    drv_ubloxgps.o(i.Init_GPS) refers to drv_ubloxgps.o(.data) for Baud115200
    drv_ubloxgps.o(i.UBLOX_M8_GPS_Data_Receive) refers to drv_ubloxgps.o(.data) for state
    drv_ubloxgps.o(i.UBLOX_M8_GPS_Data_Receive) refers to drv_ubloxgps.o(.bss) for ubx
    drv_sys.o(i.DrvSysInit) refers to drv_sys.o(i.SysTick_Init) for SysTick_Init
    drv_sys.o(i.GetSysRunTimeMs) refers to drv_sys.o(.data) for SysRunTimeMs
    drv_sys.o(i.GetSysRunTimeUs) refers to drv_sys.o(.data) for SysRunTimeMs
    drv_sys.o(i.SysTick_Handler) refers to drv_sys.o(.data) for SysRunTimeMs
    drv_adc.o(i.ADC0Handler) refers to drv_adc.o(.data) for AdcTemp
    drv_adc.o(i.DrvAdcInit) refers to adc.o(i.ADCIntRegister) for ADCIntRegister
    drv_adc.o(i.DrvAdcInit) refers to drv_adc.o(i.ADC0Handler) for ADC0Handler
    drv_adc.o(i.Drv_AdcGetBatVot) refers to drv_adc.o(i.drvAdcTrigger) for drvAdcTrigger
    drv_adc.o(i.Drv_AdcGetBatVot) refers to dfltui.o(.text) for __aeabi_ui2d
    drv_adc.o(i.Drv_AdcGetBatVot) refers to dmul.o(.text) for __aeabi_dmul
    drv_adc.o(i.Drv_AdcGetBatVot) refers to ddiv.o(.text) for __aeabi_ddiv
    drv_adc.o(i.Drv_AdcGetBatVot) refers to d2f.o(.text) for __aeabi_d2f
    drv_adc.o(i.Drv_AdcGetBatVot) refers to drv_adc.o(.data) for AdcTemp
    drv_led.o(i.DvrLedInit) refers to drv_led.o(i.DrvLedOnOff) for DrvLedOnOff
    drv_led.o(i.LED_1ms_DRV) refers to drv_led.o(i.DrvLedOnOff) for DrvLedOnOff
    drv_led.o(i.LED_1ms_DRV) refers to drv_led.o(.data) for led_cnt
    drv_pwmout.o(i.DrvPwmOutInit) refers to drv_pwmout.o(i.DrvMotorPWMSet) for DrvMotorPWMSet
    drv_rcin.o(i.DrvRcPpmInit) refers to timer.o(i.TimerIntRegister) for TimerIntRegister
    drv_rcin.o(i.DrvRcPpmInit) refers to drv_rcin.o(i.PPM_Decode) for PPM_Decode
    drv_rcin.o(i.DrvRcSbusInit) refers to sysctl.o(i.SysCtlClockGet) for SysCtlClockGet
    drv_rcin.o(i.DrvRcSbusInit) refers to uart.o(i.UARTIntRegister) for UARTIntRegister
    drv_rcin.o(i.DrvRcSbusInit) refers to drv_rcin.o(i.Sbus_IRQHandler) for Sbus_IRQHandler
    drv_rcin.o(i.PPM_Decode) refers to drv_bsp.o(i.DrvPpmGetOneCh) for DrvPpmGetOneCh
    drv_rcin.o(i.PPM_Decode) refers to drv_rcin.o(.data) for PeriodVal1
    drv_rcin.o(i.Sbus_IRQHandler) refers to drv_bsp.o(i.DrvSbusGetOneByte) for DrvSbusGetOneByte
    drv_uart.o(i.DrvUart1Init) refers to uart.o(i.UARTIntRegister) for UARTIntRegister
    drv_uart.o(i.DrvUart1Init) refers to drv_uart.o(i.UART1_IRQHandler) for UART1_IRQHandler
    drv_uart.o(i.DrvUart1SendBuf) refers to drv_uart.o(i.DrvUart1TxCheck) for DrvUart1TxCheck
    drv_uart.o(i.DrvUart1SendBuf) refers to drv_uart.o(.data) for U1TxInCnt
    drv_uart.o(i.DrvUart1SendBuf) refers to drv_uart.o(.bss) for U1TxDataTemp
    drv_uart.o(i.DrvUart1TxCheck) refers to drv_uart.o(.data) for U1TxOutCnt
    drv_uart.o(i.DrvUart1TxCheck) refers to drv_uart.o(.bss) for U1TxDataTemp
    drv_uart.o(i.DrvUart2Init) refers to uart.o(i.UARTIntRegister) for UARTIntRegister
    drv_uart.o(i.DrvUart2Init) refers to drv_uart.o(i.UART2_IRQHandler) for UART2_IRQHandler
    drv_uart.o(i.DrvUart2SendBuf) refers to drv_uart.o(i.DrvUart2TxCheck) for DrvUart2TxCheck
    drv_uart.o(i.DrvUart2SendBuf) refers to drv_uart.o(.data) for U2TxInCnt
    drv_uart.o(i.DrvUart2SendBuf) refers to drv_uart.o(.bss) for U2TxDataTemp
    drv_uart.o(i.DrvUart2TxCheck) refers to drv_uart.o(.data) for U2TxOutCnt
    drv_uart.o(i.DrvUart2TxCheck) refers to drv_uart.o(.bss) for U2TxDataTemp
    drv_uart.o(i.DrvUart3Init) refers to uart.o(i.UARTIntRegister) for UARTIntRegister
    drv_uart.o(i.DrvUart3Init) refers to drv_uart.o(i.UART3_IRQHandler) for UART3_IRQHandler
    drv_uart.o(i.DrvUart3SendBuf) refers to drv_uart.o(i.DrvUart3TxCheck) for DrvUart3TxCheck
    drv_uart.o(i.DrvUart3SendBuf) refers to drv_uart.o(.data) for U3TxInCnt
    drv_uart.o(i.DrvUart3SendBuf) refers to drv_uart.o(.bss) for U3TxDataTemp
    drv_uart.o(i.DrvUart3TxCheck) refers to drv_uart.o(.data) for U3TxOutCnt
    drv_uart.o(i.DrvUart3TxCheck) refers to drv_uart.o(.bss) for U3TxDataTemp
    drv_uart.o(i.DrvUart4Init) refers to uart.o(i.UARTIntRegister) for UARTIntRegister
    drv_uart.o(i.DrvUart4Init) refers to drv_uart.o(i.UART4_IRQHandler) for UART4_IRQHandler
    drv_uart.o(i.DrvUart4SendBuf) refers to drv_uart.o(i.DrvUart4TxCheck) for DrvUart4TxCheck
    drv_uart.o(i.DrvUart4SendBuf) refers to drv_uart.o(.data) for U4TxInCnt
    drv_uart.o(i.DrvUart4SendBuf) refers to drv_uart.o(.bss) for U4TxDataTemp
    drv_uart.o(i.DrvUart4TxCheck) refers to drv_uart.o(.data) for U4TxOutCnt
    drv_uart.o(i.DrvUart4TxCheck) refers to drv_uart.o(.bss) for U4TxDataTemp
    drv_uart.o(i.DrvUart5Init) refers to uart.o(i.UARTIntRegister) for UARTIntRegister
    drv_uart.o(i.DrvUart5Init) refers to drv_uart.o(i.UART5_IRQHandler) for UART5_IRQHandler
    drv_uart.o(i.DrvUart5SendBuf) refers to drv_uart.o(i.DrvUart5TxCheck) for DrvUart5TxCheck
    drv_uart.o(i.DrvUart5SendBuf) refers to drv_uart.o(.data) for U5TxInCnt
    drv_uart.o(i.DrvUart5SendBuf) refers to drv_uart.o(.bss) for U5TxDataTemp
    drv_uart.o(i.DrvUart5TxCheck) refers to drv_uart.o(.data) for U5TxOutCnt
    drv_uart.o(i.DrvUart5TxCheck) refers to drv_uart.o(.bss) for U5TxDataTemp
    drv_uart.o(i.DrvUartDataCheck) refers to drv_uart.o(i.DrvUart1TxCheck) for DrvUart1TxCheck
    drv_uart.o(i.DrvUartDataCheck) refers to drv_uart.o(i.DrvUart2TxCheck) for DrvUart2TxCheck
    drv_uart.o(i.DrvUartDataCheck) refers to drv_uart.o(i.DrvUart3TxCheck) for DrvUart3TxCheck
    drv_uart.o(i.DrvUartDataCheck) refers to drv_uart.o(i.DrvUart4TxCheck) for DrvUart4TxCheck
    drv_uart.o(i.DrvUartDataCheck) refers to drv_uart.o(i.DrvUart5TxCheck) for DrvUart5TxCheck
    drv_uart.o(i.UART1_IRQHandler) refers to drv_ubloxgps.o(i.UBLOX_M8_GPS_Data_Receive) for UBLOX_M8_GPS_Data_Receive
    drv_uart.o(i.UART1_IRQHandler) refers to drv_uart.o(i.DrvUart1TxCheck) for DrvUart1TxCheck
    drv_uart.o(i.UART2_IRQHandler) refers to drv_uart.o(i.NoUse) for NoUse
    drv_uart.o(i.UART2_IRQHandler) refers to drv_uart.o(i.DrvUart2TxCheck) for DrvUart2TxCheck
    drv_uart.o(i.UART3_IRQHandler) refers to drv_uart.o(i.NoUse) for NoUse
    drv_uart.o(i.UART3_IRQHandler) refers to drv_uart.o(i.DrvUart3TxCheck) for DrvUart3TxCheck
    drv_uart.o(i.UART4_IRQHandler) refers to drv_anoof.o(i.AnoOF_GetOneByte) for AnoOF_GetOneByte
    drv_uart.o(i.UART4_IRQHandler) refers to drv_uart.o(i.DrvUart4TxCheck) for DrvUart4TxCheck
    drv_uart.o(i.UART5_IRQHandler) refers to ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Prepare) for ANO_DT_LX_Data_Receive_Prepare
    drv_uart.o(i.UART5_IRQHandler) refers to drv_uart.o(i.DrvUart5TxCheck) for DrvUart5TxCheck
    drv_usb.o(i.ControlHandler) refers to usbbuffer.o(i.USBBufferFlush) for USBBufferFlush
    drv_usb.o(i.ControlHandler) refers to drv_usb.o(.data) for g_bUSBConfigured
    drv_usb.o(i.ControlHandler) refers to usb_serial_structs.o(.data) for g_sTxBuffer
    drv_usb.o(i.DrvUsbInit) refers to usbbuffer.o(i.USBBufferInit) for USBBufferInit
    drv_usb.o(i.DrvUsbInit) refers to usbmode.o(i.USBStackModeSet) for USBStackModeSet
    drv_usb.o(i.DrvUsbInit) refers to usbdcdc.o(i.USBDCDCInit) for USBDCDCInit
    drv_usb.o(i.DrvUsbInit) refers to usb.o(i.USBIntRegister) for USBIntRegister
    drv_usb.o(i.DrvUsbInit) refers to usb_serial_structs.o(.data) for g_sTxBuffer
    drv_usb.o(i.DrvUsbInit) refers to usbdhandler.o(i.USB0DeviceIntHandler) for USB0DeviceIntHandler
    drv_usb.o(i.DrvUsbRead) refers to usbbuffer.o(i.USBBufferRead) for USBBufferRead
    drv_usb.o(i.DrvUsbRead) refers to drv_usb.o(.data) for g_bUSBConfigured
    drv_usb.o(i.DrvUsbRead) refers to usb_serial_structs.o(.data) for g_sRxBuffer
    drv_usb.o(i.DrvUsbSend) refers to usbbuffer.o(i.USBBufferWrite) for USBBufferWrite
    drv_usb.o(i.DrvUsbSend) refers to drv_usb.o(.data) for g_bUSBConfigured
    drv_usb.o(i.DrvUsbSend) refers to usb_serial_structs.o(.data) for g_sTxBuffer
    drv_timer.o(i.DrvTimerFcInit) refers to sysctl.o(i.SysCtlClockGet) for SysCtlClockGet
    drv_timer.o(i.DrvTimerFcInit) refers to timer.o(i.TimerIntRegister) for TimerIntRegister
    drv_timer.o(i.DrvTimerFcInit) refers to drv_timer.o(i.Timer0Irq) for Timer0Irq
    drv_timer.o(i.Timer0Irq) refers to timer.o(i.TimerIntClear) for TimerIntClear
    drv_timer.o(i.Timer0Irq) refers to ano_lx.o(i.ANO_LX_Task) for ANO_LX_Task
    startup_tm4c123.o(RESET) refers to startup_tm4c123.o(STACK) for __initial_sp
    startup_tm4c123.o(RESET) refers to startup_tm4c123.o(.text) for Reset_Handler
    startup_tm4c123.o(RESET) refers to drv_sys.o(i.SysTick_Handler) for SysTick_Handler
    startup_tm4c123.o(.text) refers to system_tm4c123.o(i.SystemInit) for SystemInit
    startup_tm4c123.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_tm4c123.o(i.SystemCoreClockUpdate) refers to system_tm4c123.o(i.getOscClk) for getOscClk
    system_tm4c123.o(i.SystemCoreClockUpdate) refers to system_tm4c123.o(.data) for SystemCoreClock
    usb_serial_structs.o(.data) refers to drv_usb.o(i.ControlHandler) for ControlHandler
    usb_serial_structs.o(.data) refers to usbbuffer.o(i.USBBufferEventCallback) for USBBufferEventCallback
    usb_serial_structs.o(.data) refers to usb_serial_structs.o(.constdata) for g_ppui8StringDescriptors
    usb_serial_structs.o(.data) refers to drv_usb.o(i.RxHandler) for RxHandler
    usb_serial_structs.o(.data) refers to usbdcdc.o(i.USBDCDCPacketRead) for USBDCDCPacketRead
    usb_serial_structs.o(.data) refers to usbdcdc.o(i.USBDCDCRxPacketAvailable) for USBDCDCRxPacketAvailable
    usb_serial_structs.o(.data) refers to usb_serial_structs.o(.bss) for g_pui8USBRxBuffer
    usb_serial_structs.o(.data) refers to drv_usb.o(i.TxHandler) for TxHandler
    usb_serial_structs.o(.data) refers to usbdcdc.o(i.USBDCDCPacketWrite) for USBDCDCPacketWrite
    usb_serial_structs.o(.data) refers to usbdcdc.o(i.USBDCDCTxPacketAvailable) for USBDCDCTxPacketAvailable
    adc.o(i.ADCIntRegister) refers to interrupt.o(i.IntRegister) for IntRegister
    adc.o(i.ADCIntRegister) refers to interrupt.o(i.IntEnable) for IntEnable
    adc.o(i.ADCIntUnregister) refers to interrupt.o(i.IntDisable) for IntDisable
    adc.o(i.ADCIntUnregister) refers to interrupt.o(i.IntUnregister) for IntUnregister
    adc.o(i.ADCSoftwareOversampleConfigure) refers to adc.o(.data) for .data
    adc.o(i.ADCSoftwareOversampleDataGet) refers to adc.o(.data) for .data
    adc.o(i.ADCSoftwareOversampleStepConfigure) refers to adc.o(.data) for .data
    sysctl.o(i.SysCtlClockFreqSet) refers to sysctl.o(.constdata) for .constdata
    sysctl.o(i.SysCtlClockGet) refers to sysctl.o(.constdata) for .constdata
    sysctl.o(i.SysCtlClockSet) refers to sysctl.o(.emb_text) for SysCtlDelay
    sysctl.o(i.SysCtlDeepSleep) refers to cpu.o(.emb_text) for CPUwfi
    sysctl.o(i.SysCtlIntRegister) refers to interrupt.o(i.IntRegister) for IntRegister
    sysctl.o(i.SysCtlIntRegister) refers to interrupt.o(i.IntEnable) for IntEnable
    sysctl.o(i.SysCtlIntUnregister) refers to interrupt.o(i.IntDisable) for IntDisable
    sysctl.o(i.SysCtlIntUnregister) refers to interrupt.o(i.IntUnregister) for IntUnregister
    sysctl.o(i.SysCtlSleep) refers to cpu.o(.emb_text) for CPUwfi
    sysctl.o(i.SysCtlVCOGet) refers to sysctl.o(.constdata) for .constdata
    timer.o(i.TimerIntRegister) refers to interrupt.o(i.IntRegister) for IntRegister
    timer.o(i.TimerIntRegister) refers to interrupt.o(i.IntEnable) for IntEnable
    timer.o(i.TimerIntRegister) refers to timer.o(.constdata) for .constdata
    timer.o(i.TimerIntUnregister) refers to interrupt.o(i.IntDisable) for IntDisable
    timer.o(i.TimerIntUnregister) refers to interrupt.o(i.IntUnregister) for IntUnregister
    timer.o(i.TimerIntUnregister) refers to timer.o(.constdata) for .constdata
    uart.o(i.UARTIntRegister) refers to interrupt.o(i.IntRegister) for IntRegister
    uart.o(i.UARTIntRegister) refers to interrupt.o(i.IntEnable) for IntEnable
    uart.o(i.UARTIntRegister) refers to uart.o(.constdata) for .constdata
    uart.o(i.UARTIntUnregister) refers to interrupt.o(i.IntDisable) for IntDisable
    uart.o(i.UARTIntUnregister) refers to interrupt.o(i.IntUnregister) for IntUnregister
    uart.o(i.UARTIntUnregister) refers to uart.o(.constdata) for .constdata
    usb.o(i.USBIntRegister) refers to interrupt.o(i.IntRegister) for IntRegister
    usb.o(i.USBIntRegister) refers to interrupt.o(i.IntEnable) for IntEnable
    usb.o(i.USBIntUnregister) refers to interrupt.o(i.IntDisable) for IntDisable
    usb.o(i.USBIntUnregister) refers to interrupt.o(i.IntUnregister) for IntUnregister
    usbbuffer.o(i.ScheduleNextTransmission) refers to usbringbuf.o(i.USBRingBufContigUsed) for USBRingBufContigUsed
    usbbuffer.o(i.ScheduleNextTransmission) refers to usbringbuf.o(i.USBRingBufUsed) for USBRingBufUsed
    usbbuffer.o(i.USBBufferDataAvailable) refers to usbringbuf.o(i.USBRingBufUsed) for USBRingBufUsed
    usbbuffer.o(i.USBBufferDataRemoved) refers to usbringbuf.o(i.USBRingBufAdvanceRead) for USBRingBufAdvanceRead
    usbbuffer.o(i.USBBufferDataWritten) refers to usbringbuf.o(i.USBRingBufAdvanceWrite) for USBRingBufAdvanceWrite
    usbbuffer.o(i.USBBufferDataWritten) refers to usbbuffer.o(i.ScheduleNextTransmission) for ScheduleNextTransmission
    usbbuffer.o(i.USBBufferEventCallback) refers to usbringbuf.o(i.USBRingBufAdvanceWrite) for USBRingBufAdvanceWrite
    usbbuffer.o(i.USBBufferEventCallback) refers to usbringbuf.o(i.USBRingBufFree) for USBRingBufFree
    usbbuffer.o(i.USBBufferEventCallback) refers to usbringbuf.o(i.USBRingBufWrite) for USBRingBufWrite
    usbbuffer.o(i.USBBufferEventCallback) refers to usbringbuf.o(i.USBRingBufContigFree) for USBRingBufContigFree
    usbbuffer.o(i.USBBufferEventCallback) refers to usbringbuf.o(i.USBRingBufUsed) for USBRingBufUsed
    usbbuffer.o(i.USBBufferEventCallback) refers to usbringbuf.o(i.USBRingBufAdvanceRead) for USBRingBufAdvanceRead
    usbbuffer.o(i.USBBufferEventCallback) refers to usbbuffer.o(i.ScheduleNextTransmission) for ScheduleNextTransmission
    usbbuffer.o(i.USBBufferFlush) refers to usbringbuf.o(i.USBRingBufFlush) for USBRingBufFlush
    usbbuffer.o(i.USBBufferInit) refers to usbringbuf.o(i.USBRingBufInit) for USBRingBufInit
    usbbuffer.o(i.USBBufferRead) refers to usbringbuf.o(i.USBRingBufUsed) for USBRingBufUsed
    usbbuffer.o(i.USBBufferRead) refers to usbringbuf.o(i.USBRingBufRead) for USBRingBufRead
    usbbuffer.o(i.USBBufferSpaceAvailable) refers to usbringbuf.o(i.USBRingBufFree) for USBRingBufFree
    usbbuffer.o(i.USBBufferWrite) refers to usbringbuf.o(i.USBRingBufFree) for USBRingBufFree
    usbbuffer.o(i.USBBufferWrite) refers to usbringbuf.o(i.USBRingBufWrite) for USBRingBufWrite
    usbbuffer.o(i.USBBufferWrite) refers to usbbuffer.o(i.ScheduleNextTransmission) for ScheduleNextTransmission
    usbdcdc.o(i.CDCTickHandler) refers to usbdcdc.o(i.SendBreak) for SendBreak
    usbdcdc.o(i.CDCTickHandler) refers to usb.o(i.USBEndpointDataAvail) for USBEndpointDataAvail
    usbdcdc.o(i.HandleEP0Data) refers to usbdenum.o(i.USBDCDStallEP0) for USBDCDStallEP0
    usbdcdc.o(i.HandleEndpoints) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdcdc.o(i.HandleEndpoints) refers to usb.o(i.USBDevEndpointStatusClear) for USBDevEndpointStatusClear
    usbdcdc.o(i.HandleEndpoints) refers to usbdcdc.o(i.SendSerialState) for SendSerialState
    usbdcdc.o(i.HandleEndpoints) refers to usbdcdc.o(i.ProcessDataFromHost) for ProcessDataFromHost
    usbdcdc.o(i.HandleEndpoints) refers to usbdcdc.o(i.ProcessDataToHost) for ProcessDataToHost
    usbdcdc.o(i.HandleRequests) refers to usbdenum.o(i.USBDCDStallEP0) for USBDCDStallEP0
    usbdcdc.o(i.HandleRequests) refers to usbdenum.o(i.USBDCDRequestDataEP0) for USBDCDRequestDataEP0
    usbdcdc.o(i.HandleRequests) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdcdc.o(i.HandleRequests) refers to usbdenum.o(i.USBDCDSendDataEP0) for USBDCDSendDataEP0
    usbdcdc.o(i.HandleRequests) refers to usbdcdc.o(i.SendBreak) for SendBreak
    usbdcdc.o(i.ProcessDataFromHost) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdcdc.o(i.ProcessDataFromHost) refers to usb.o(i.USBDevEndpointStatusClear) for USBDevEndpointStatusClear
    usbdcdc.o(i.ProcessDataFromHost) refers to usb.o(i.USBEndpointDataAvail) for USBEndpointDataAvail
    usbdcdc.o(i.ProcessDataToHost) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdcdc.o(i.ProcessDataToHost) refers to usb.o(i.USBDevEndpointStatusClear) for USBDevEndpointStatusClear
    usbdcdc.o(i.ProcessDataToHost) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbdcdc.o(i.ProcessDataToHost) refers to usbdcdc.o(.data) for .data
    usbdcdc.o(i.ProcessNotificationToHost) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdcdc.o(i.ProcessNotificationToHost) refers to usb.o(i.USBDevEndpointStatusClear) for USBDevEndpointStatusClear
    usbdcdc.o(i.ProcessNotificationToHost) refers to usbdcdc.o(i.SendSerialState) for SendSerialState
    usbdcdc.o(i.SendSerialState) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbdcdc.o(i.SendSerialState) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbdenum.o(i.USBDCDFeatureGet) for USBDCDFeatureGet
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbdenum.o(i.USBDCDDeviceInfoInit) for USBDCDDeviceInfoInit
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbtick.o(i.InternalUSBTickInit) for InternalUSBTickInit
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbtick.o(i.InternalUSBRegisterTickHandler) for InternalUSBRegisterTickHandler
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbdcdc.o(.constdata) for .constdata
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbdcdc.o(.data) for .data
    usbdcdc.o(i.USBDCDCCompositeInit) refers to usbdcdc.o(i.CDCTickHandler) for CDCTickHandler
    usbdcdc.o(i.USBDCDCInit) refers to usbdcdc.o(i.USBDCDCCompositeInit) for USBDCDCCompositeInit
    usbdcdc.o(i.USBDCDCInit) refers to usbdenum.o(i.USBDCDInit) for USBDCDInit
    usbdcdc.o(i.USBDCDCInit) refers to usbdcdc.o(.data) for .data
    usbdcdc.o(i.USBDCDCInit) refers to usbdcdc.o(.constdata) for .constdata
    usbdcdc.o(i.USBDCDCPacketRead) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdcdc.o(i.USBDCDCPacketRead) refers to usb.o(i.USBEndpointDataAvail) for USBEndpointDataAvail
    usbdcdc.o(i.USBDCDCPacketRead) refers to usb.o(i.USBEndpointDataGet) for USBEndpointDataGet
    usbdcdc.o(i.USBDCDCPacketRead) refers to usb.o(i.USBDevEndpointStatusClear) for USBDevEndpointStatusClear
    usbdcdc.o(i.USBDCDCPacketRead) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdcdc.o(i.USBDCDCPacketWrite) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbdcdc.o(i.USBDCDCPacketWrite) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbdcdc.o(i.USBDCDCPacketWrite) refers to usbdcdc.o(.data) for .data
    usbdcdc.o(i.USBDCDCPowerStatusSet) refers to usbdenum.o(i.USBDCDPowerStatusSet) for USBDCDPowerStatusSet
    usbdcdc.o(i.USBDCDCRemoteWakeupRequest) refers to usbdenum.o(i.USBDCDRemoteWakeupRequest) for USBDCDRemoteWakeupRequest
    usbdcdc.o(i.USBDCDCRxPacketAvailable) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdcdc.o(i.USBDCDCRxPacketAvailable) refers to usb.o(i.USBEndpointDataAvail) for USBEndpointDataAvail
    usbdcdc.o(i.USBDCDCSerialStateChange) refers to usbdcdc.o(i.SendSerialState) for SendSerialState
    usbdcdc.o(i.USBDCDCTerm) refers to usbdenum.o(i.USBDCDTerm) for USBDCDTerm
    usbdcdc.o(i.USBDCDCTxPacketAvailable) refers to usbdcdc.o(.data) for .data
    usbdcdc.o(.constdata) refers to usbdcdc.o(.data) for g_pui8CDCSerDescriptor
    usbdcdc.o(.constdata) refers to usbdcdc.o(.data) for g_pui8IADSerDescriptor
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_pui8CDCSerCommInterface
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_pui8CDCSerDataInterface
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_pui8CDCSerDataInterfaceHS
    usbdcdc.o(.constdata) refers to usbdcdc.o(.data) for g_psCDCSerSections
    usbdcdc.o(.constdata) refers to usbdcdc.o(.data) for g_psCDCSerSectionsHS
    usbdcdc.o(.constdata) refers to usbdcdc.o(.data) for g_psCDCCompSerSections
    usbdcdc.o(.constdata) refers to usbdcdc.o(.data) for g_psCDCCompSerSectionsHS
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_sCDCSerConfigHeader
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_sCDCSerConfigHeaderHS
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_sCDCCompSerConfigHeader
    usbdcdc.o(.constdata) refers to usbdcdc.o(.constdata) for g_sCDCCompSerConfigHeaderHS
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleRequests) for HandleRequests
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleConfigChange) for HandleConfigChange
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleEP0Data) for HandleEP0Data
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleSuspend) for HandleSuspend
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleResume) for HandleResume
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleDisconnect) for HandleDisconnect
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleEndpoints) for HandleEndpoints
    usbdcdc.o(.constdata) refers to usbdcdc.o(i.HandleDevice) for HandleDevice
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerConfigSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerCommInterfaceSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerDataInterfaceSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerConfigSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerCommInterfaceSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerDataInterfaceSectionHS
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerConfigSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sIADSerConfigSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerCommInterfaceSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerDataInterfaceSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerConfigSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sIADSerConfigSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerCommInterfaceSection
    usbdcdc.o(.data) refers to usbdcdc.o(.constdata) for g_sCDCSerDataInterfaceSectionHS
    usbdhandler.o(i.USB0DeviceIntHandler) refers to usb.o(i.USBIntStatusControl) for USBIntStatusControl
    usbdhandler.o(i.USB0DeviceIntHandler) refers to usbdenum.o(i.USBDeviceIntHandlerInternal) for USBDeviceIntHandlerInternal
    usbmode.o(i.OTGDeviceDisconnect) refers to usbmode.o(i.USBOTGSetMode) for USBOTGSetMode
    usbmode.o(i.OTGDeviceDisconnect) refers to usbmode.o(.data) for .data
    usbmode.o(i.USB0DualModeIntHandler) refers to usb.o(i.USBIntStatusControl) for USBIntStatusControl
    usbmode.o(i.USB0DualModeIntHandler) refers to usbhostenum.o(i.USBHostIntHandlerInternal) for USBHostIntHandlerInternal
    usbmode.o(i.USB0DualModeIntHandler) refers to usbdenum.o(i.USBDeviceIntHandlerInternal) for USBDeviceIntHandlerInternal
    usbmode.o(i.USB0DualModeIntHandler) refers to usbmode.o(.data) for .data
    usbmode.o(i.USB0OTGModeIntHandler) refers to usb.o(i.USBIntStatusControl) for USBIntStatusControl
    usbmode.o(i.USB0OTGModeIntHandler) refers to usbhostenum.o(i.USBHCDPowerAutomatic) for USBHCDPowerAutomatic
    usbmode.o(i.USB0OTGModeIntHandler) refers to usb.o(i.USBModeGet) for USBModeGet
    usbmode.o(i.USB0OTGModeIntHandler) refers to usbmode.o(i.USBOTGSetMode) for USBOTGSetMode
    usbmode.o(i.USB0OTGModeIntHandler) refers to usbhostenum.o(i.USBHostIntHandlerInternal) for USBHostIntHandlerInternal
    usbmode.o(i.USB0OTGModeIntHandler) refers to usbhostenum.o(i.InternalUSBHCDSendEvent) for InternalUSBHCDSendEvent
    usbmode.o(i.USB0OTGModeIntHandler) refers to usbdenum.o(i.USBDeviceIntHandlerInternal) for USBDeviceIntHandlerInternal
    usbmode.o(i.USB0OTGModeIntHandler) refers to usbmode.o(.data) for .data
    usbmode.o(i.USBDualModeInit) refers to usb.o(i.USBHostEndpointConfig) for USBHostEndpointConfig
    usbmode.o(i.USBDualModeInit) refers to usb.o(i.USBIntEnableControl) for USBIntEnableControl
    usbmode.o(i.USBDualModeInit) refers to usb.o(i.USBIntEnableEndpoint) for USBIntEnableEndpoint
    usbmode.o(i.USBDualModeInit) refers to usbtick.o(i.InternalUSBTickInit) for InternalUSBTickInit
    usbmode.o(i.USBDualModeInit) refers to interrupt.o(i.IntEnable) for IntEnable
    usbmode.o(i.USBDualModeInit) refers to usb.o(i.USBOTGSessionRequest) for USBOTGSessionRequest
    usbmode.o(i.USBDualModeInit) refers to usbhostenum.o(i.USBHCDPowerConfigGet) for USBHCDPowerConfigGet
    usbmode.o(i.USBDualModeInit) refers to usb.o(i.USBHostPwrConfig) for USBHostPwrConfig
    usbmode.o(i.USBDualModeInit) refers to usbhostenum.o(i.USBHCDPowerAutomatic) for USBHCDPowerAutomatic
    usbmode.o(i.USBDualModeInit) refers to usb.o(i.USBHostPwrEnable) for USBHostPwrEnable
    usbmode.o(i.USBDualModeInit) refers to usbdenum.o(.bss) for g_psDCDInst
    usbmode.o(i.USBDualModeTerm) refers to interrupt.o(i.IntDisable) for IntDisable
    usbmode.o(i.USBDualModeTerm) refers to usb.o(i.USBIntDisableControl) for USBIntDisableControl
    usbmode.o(i.USBDualModeTerm) refers to usb.o(i.USBIntDisableEndpoint) for USBIntDisableEndpoint
    usbmode.o(i.USBDualModeTerm) refers to usbdenum.o(.bss) for g_psDCDInst
    usbmode.o(i.USBOTGFeatureSet) refers to usbdenum.o(i.USBDCDFeatureSet) for USBDCDFeatureSet
    usbmode.o(i.USBOTGFeatureSet) refers to usbhostenum.o(i.USBHCDFeatureSet) for USBHCDFeatureSet
    usbmode.o(i.USBOTGMain) refers to usb.o(i.USBOTGSessionRequest) for USBOTGSessionRequest
    usbmode.o(i.USBOTGMain) refers to usbmode.o(i.USBOTGSetMode) for USBOTGSetMode
    usbmode.o(i.USBOTGMain) refers to usbhostenum.o(i.USBHCDPowerAutomatic) for USBHCDPowerAutomatic
    usbmode.o(i.USBOTGMain) refers to usbhostenum.o(i.InternalUSBHCDSendEvent) for InternalUSBHCDSendEvent
    usbmode.o(i.USBOTGMain) refers to usbhostenum.o(i.USBHCDMain) for USBHCDMain
    usbmode.o(i.USBOTGMain) refers to usbmode.o(.data) for .data
    usbmode.o(i.USBOTGModeInit) refers to sysctl.o(i.SysCtlPeripheralEnable) for SysCtlPeripheralEnable
    usbmode.o(i.USBOTGModeInit) refers to sysctl.o(i.SysCtlUSBPLLEnable) for SysCtlUSBPLLEnable
    usbmode.o(i.USBOTGModeInit) refers to usbhostenum.o(i.USBHCDInit) for USBHCDInit
    usbmode.o(i.USBOTGModeInit) refers to usb.o(i.USBHostEndpointConfig) for USBHostEndpointConfig
    usbmode.o(i.USBOTGModeInit) refers to usb.o(i.USBIntEnableControl) for USBIntEnableControl
    usbmode.o(i.USBOTGModeInit) refers to usb.o(i.USBOTGMode) for USBOTGMode
    usbmode.o(i.USBOTGModeInit) refers to usb.o(i.USBIntEnableEndpoint) for USBIntEnableEndpoint
    usbmode.o(i.USBOTGModeInit) refers to usbhostenum.o(i.USBHCDPowerConfigGet) for USBHCDPowerConfigGet
    usbmode.o(i.USBOTGModeInit) refers to usbhostenum.o(i.USBHCDPowerConfigSet) for USBHCDPowerConfigSet
    usbmode.o(i.USBOTGModeInit) refers to usbhostenum.o(i.USBHCDPowerAutomatic) for USBHCDPowerAutomatic
    usbmode.o(i.USBOTGModeInit) refers to usb.o(i.USBHostPwrEnable) for USBHostPwrEnable
    usbmode.o(i.USBOTGModeInit) refers to interrupt.o(i.IntEnable) for IntEnable
    usbmode.o(i.USBOTGModeInit) refers to usbmode.o(.data) for .data
    usbmode.o(i.USBOTGModeTerm) refers to interrupt.o(i.IntDisable) for IntDisable
    usbmode.o(i.USBOTGModeTerm) refers to usb.o(i.USBIntDisableControl) for USBIntDisableControl
    usbmode.o(i.USBOTGModeTerm) refers to usb.o(i.USBIntDisableEndpoint) for USBIntDisableEndpoint
    usbmode.o(i.USBOTGModeTerm) refers to usbmode.o(i.USBOTGSetMode) for USBOTGSetMode
    usbmode.o(i.USBOTGModeTerm) refers to usbdenum.o(.bss) for g_psDCDInst
    usbmode.o(i.USBOTGPollRate) refers to usbmode.o(.data) for .data
    usbmode.o(i.USBOTGSetMode) refers to usb.o(i.USBHostSuspend) for USBHostSuspend
    usbmode.o(i.USBOTGSetMode) refers to usb.o(i.USBOTGSessionRequest) for USBOTGSessionRequest
    usbmode.o(i.USBOTGSetMode) refers to usbhostenum.o(i.USBHCDPowerAutomatic) for USBHCDPowerAutomatic
    usbmode.o(i.USBOTGSetMode) refers to usbhostenum.o(i.InternalUSBHCDSendEvent) for InternalUSBHCDSendEvent
    usbmode.o(i.USBOTGSetMode) refers to usbmode.o(.data) for .data
    usbmode.o(i.USBStackModeSet) refers to usbmode.o(.data) for .data
    interrupt.o(i.IntDisable) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntEnable) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntIsEnabled) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntMasterDisable) refers to cpu.o(.emb_text) for CPUcpsid
    interrupt.o(i.IntMasterEnable) refers to cpu.o(.emb_text) for CPUcpsie
    interrupt.o(i.IntPendClear) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntPendSet) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntPriorityGet) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntPriorityGroupingGet) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntPriorityGroupingSet) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntPriorityMaskGet) refers to cpu.o(.emb_text) for CPUbasepriGet
    interrupt.o(i.IntPriorityMaskSet) refers to cpu.o(.emb_text) for CPUbasepriSet
    interrupt.o(i.IntPrioritySet) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.IntRegister) refers to interrupt.o(vtable) for vtable
    interrupt.o(i.IntUnregister) refers to interrupt.o(i._IntDefaultHandler) for _IntDefaultHandler
    interrupt.o(i.IntUnregister) refers to interrupt.o(vtable) for vtable
    usbdenum.o(i.USBDCDDeviceInfoInit) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDFeatureGet) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDCDFeatureSet) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDFeatureSet) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDCDInit) refers to sysctl.o(i.SysCtlPeripheralReset) for SysCtlPeripheralReset
    usbdenum.o(i.USBDCDInit) refers to sysctl.o(i.SysCtlPeripheralEnable) for SysCtlPeripheralEnable
    usbdenum.o(i.USBDCDInit) refers to sysctl.o(i.SysCtlUSBPLLEnable) for SysCtlUSBPLLEnable
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBClockEnable) for USBClockEnable
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBULPIEnable) for USBULPIEnable
    usbdenum.o(i.USBDCDInit) refers to usbulpi.o(i.ULPIConfigSet) for ULPIConfigSet
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBULPIDisable) for USBULPIDisable
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBOTGMode) for USBOTGMode
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBDevMode) for USBDevMode
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBDevLPMConfig) for USBDevLPMConfig
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBLPMIntEnable) for USBLPMIntEnable
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBDevLPMEnable) for USBDevLPMEnable
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBDevLPMDisable) for USBDevLPMDisable
    usbdenum.o(i.USBDCDInit) refers to usbdma.o(i.USBLibDMAInit) for USBLibDMAInit
    usbdenum.o(i.USBDCDInit) refers to usbtick.o(i.InternalUSBTickInit) for InternalUSBTickInit
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBIntStatusControl) for USBIntStatusControl
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBIntStatusEndpoint) for USBIntStatusEndpoint
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBIntEnableControl) for USBIntEnableControl
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBIntEnableEndpoint) for USBIntEnableEndpoint
    usbdenum.o(i.USBDCDInit) refers to usb.o(i.USBDevConnect) for USBDevConnect
    usbdenum.o(i.USBDCDInit) refers to interrupt.o(i.IntEnable) for IntEnable
    usbdenum.o(i.USBDCDInit) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDCDInit) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDInit) refers to usbmode.o(.data) for g_iUSBMode
    usbdenum.o(i.USBDCDPowerStatusSet) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDRemoteWakeLPM) refers to usb.o(i.USBLPMRemoteWakeEnabled) for USBLPMRemoteWakeEnabled
    usbdenum.o(i.USBDCDRemoteWakeLPM) refers to usb.o(i.USBDevLPMRemoteWake) for USBDevLPMRemoteWake
    usbdenum.o(i.USBDCDRemoteWakeupRequest) refers to usb.o(i.USBHostResume) for USBHostResume
    usbdenum.o(i.USBDCDRemoteWakeupRequest) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDRequestDataEP0) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDSendDataEP0) refers to usbdenum.o(i.USBDEP0StateTx) for USBDEP0StateTx
    usbdenum.o(i.USBDCDSendDataEP0) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDSetDefaultConfiguration) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDStallEP0) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDCDStallEP0) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDTerm) refers to interrupt.o(i.IntDisable) for IntDisable
    usbdenum.o(i.USBDCDTerm) refers to usbtick.o(i.InternalUSBTickReset) for InternalUSBTickReset
    usbdenum.o(i.USBDCDTerm) refers to usb.o(i.USBIntDisableControl) for USBIntDisableControl
    usbdenum.o(i.USBDCDTerm) refers to usb.o(i.USBIntDisableEndpoint) for USBIntDisableEndpoint
    usbdenum.o(i.USBDCDTerm) refers to usb.o(i.USBDevDisconnect) for USBDevDisconnect
    usbdenum.o(i.USBDCDTerm) refers to usb.o(i.USBIntStatusControl) for USBIntStatusControl
    usbdenum.o(i.USBDCDTerm) refers to usb.o(i.USBIntStatusEndpoint) for USBIntStatusEndpoint
    usbdenum.o(i.USBDCDTerm) refers to sysctl.o(i.SysCtlUSBPLLDisable) for SysCtlUSBPLLDisable
    usbdenum.o(i.USBDCDTerm) refers to sysctl.o(i.SysCtlPeripheralDisable) for SysCtlPeripheralDisable
    usbdenum.o(i.USBDCDTerm) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDCDTerm) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDClearFeature) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDClearFeature) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDClearFeature) refers to usb.o(i.USBDevEndpointStallClear) for USBDevEndpointStallClear
    usbdenum.o(i.USBDClearFeature) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDEP0StateTx) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbdenum.o(i.USBDEP0StateTx) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbdenum.o(i.USBDEP0StateTx) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDEP0StateTx) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDEP0StateTxConfig) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbdenum.o(i.USBDEP0StateTxConfig) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbdenum.o(i.USBDEP0StateTxConfig) refers to usbdcdesc.o(i.USBDCDConfigDescGetSize) for USBDCDConfigDescGetSize
    usbdenum.o(i.USBDEP0StateTxConfig) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDEP0StateTxConfig) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDGetConfiguration) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDGetConfiguration) refers to usbdenum.o(i.USBDEP0StateTx) for USBDEP0StateTx
    usbdenum.o(i.USBDGetDescriptor) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDGetDescriptor) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDGetDescriptor) refers to usbdcdesc.o(i.USBDCDConfigDescGetSize) for USBDCDConfigDescGetSize
    usbdenum.o(i.USBDGetDescriptor) refers to usbdenum.o(i.USBDEP0StateTxConfig) for USBDEP0StateTxConfig
    usbdenum.o(i.USBDGetDescriptor) refers to usbdenum.o(i.USBDEP0StateTx) for USBDEP0StateTx
    usbdenum.o(i.USBDGetDescriptor) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDGetDescriptor) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDGetInterface) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDGetInterface) refers to usbdenum.o(i.USBDEP0StateTx) for USBDEP0StateTx
    usbdenum.o(i.USBDGetInterface) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDGetInterface) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDGetStatus) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDGetStatus) refers to usbdenum.o(i.USBDEP0StateTx) for USBDEP0StateTx
    usbdenum.o(i.USBDGetStatus) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDGetStatus) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDReadAndDispatchRequest) refers to usb.o(i.USBEndpointDataGet) for USBEndpointDataGet
    usbdenum.o(i.USBDReadAndDispatchRequest) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDReadAndDispatchRequest) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDReadAndDispatchRequest) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDReadAndDispatchRequest) refers to usbdenum.o(.constdata) for .constdata
    usbdenum.o(i.USBDSetAddress) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDSetConfiguration) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDSetConfiguration) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDSetConfiguration) refers to usbdconfig.o(i.USBDeviceConfig) for USBDeviceConfig
    usbdenum.o(i.USBDSetConfiguration) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDSetConfiguration) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDSetDescriptor) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDSetDescriptor) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDSetDescriptor) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDSetFeature) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDSetFeature) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDSetFeature) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDSetInterface) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDSetInterface) refers to usbdcdesc.o(i.USBDCDConfigDescGetNum) for USBDCDConfigDescGetNum
    usbdenum.o(i.USBDSetInterface) refers to usbdcdesc.o(i.USBDCDConfigGetInterface) for USBDCDConfigGetInterface
    usbdenum.o(i.USBDSetInterface) refers to usbdconfig.o(i.USBDeviceConfigAlternate) for USBDeviceConfigAlternate
    usbdenum.o(i.USBDSetInterface) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDSetInterface) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDSetInterface) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDSyncFrame) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDSyncFrame) refers to usb.o(i.USBDevEndpointStall) for USBDevEndpointStall
    usbdenum.o(i.USBDSyncFrame) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDeviceEnumHandler) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbdenum.o(i.USBDeviceEnumHandler) refers to usb.o(i.USBDevAddrSet) for USBDevAddrSet
    usbdenum.o(i.USBDeviceEnumHandler) refers to usbdenum.o(i.USBDReadAndDispatchRequest) for USBDReadAndDispatchRequest
    usbdenum.o(i.USBDeviceEnumHandler) refers to usbdenum.o(i.USBDEP0StateTx) for USBDEP0StateTx
    usbdenum.o(i.USBDeviceEnumHandler) refers to usbdenum.o(i.USBDEP0StateTxConfig) for USBDEP0StateTxConfig
    usbdenum.o(i.USBDeviceEnumHandler) refers to usb.o(i.USBEndpointDataGet) for USBEndpointDataGet
    usbdenum.o(i.USBDeviceEnumHandler) refers to usb.o(i.USBDevEndpointDataAck) for USBDevEndpointDataAck
    usbdenum.o(i.USBDeviceEnumHandler) refers to usb.o(i.USBDevEndpointStatusClear) for USBDevEndpointStatusClear
    usbdenum.o(i.USBDeviceEnumHandler) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDeviceEnumHandler) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDeviceEnumResetHandler) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDeviceEnumResetHandler) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usbdenum.o(i.USBDeviceResumeTickHandler) for USBDeviceResumeTickHandler
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usb.o(i.USBDevDisconnect) for USBDevDisconnect
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usbtick.o(i.InternalUSBStartOfFrameTick) for InternalUSBStartOfFrameTick
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usb.o(i.USBLPMIntStatus) for USBLPMIntStatus
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usb.o(i.USBIntStatusEndpoint) for USBIntStatusEndpoint
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usbdenum.o(i.USBDeviceEnumHandler) for USBDeviceEnumHandler
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usb.o(i.USBDevLPMEnable) for USBDevLPMEnable
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usbdenum.o(.data) for .data
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDeviceIntHandlerInternal) refers to usbtick.o(.data) for g_ui32USBSOFCount
    usbdenum.o(i.USBDeviceResumeTickHandler) refers to usb.o(i.USBHostResume) for USBHostResume
    usbdenum.o(i.USBDeviceResumeTickHandler) refers to usbdenum.o(.bss) for .bss
    usbdenum.o(i.USBDeviceResumeTickHandler) refers to usbdenum.o(.data) for .data
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDGetStatus) for USBDGetStatus
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDClearFeature) for USBDClearFeature
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDSetFeature) for USBDSetFeature
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDSetAddress) for USBDSetAddress
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDGetDescriptor) for USBDGetDescriptor
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDSetDescriptor) for USBDSetDescriptor
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDGetConfiguration) for USBDGetConfiguration
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDSetConfiguration) for USBDSetConfiguration
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDGetInterface) for USBDGetInterface
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDSetInterface) for USBDSetInterface
    usbdenum.o(.constdata) refers to usbdenum.o(i.USBDSyncFrame) for USBDSyncFrame
    usbhostenum.o(i.ConfigDescAlloc) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.ConfigDescFree) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.InternalUSBHCDSendEvent) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(i.USBHCDDeviceDisconnected) for USBHCDDeviceDisconnected
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to sysctl.o(i.SysCtlPeripheralReset) for SysCtlPeripheralReset
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to sysctl.o(.emb_text) for SysCtlDelay
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(i.USBHCDInitInternal) for USBHCDInitInternal
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usb.o(i.USBHostReset) for USBHostReset
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usb.o(i.USBHostSpeedGet) for USBHostSpeedGet
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(i.InternalUSBHCDSendEvent) for InternalUSBHCDSendEvent
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhhub.o(i.USBHHubEnumerationError) for USBHHubEnumerationError
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbdesc.o(i.USBDescGetInterface) for USBDescGetInterface
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(i.ConfigDescAlloc) for ConfigDescAlloc
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhhub.o(i.USBHHubEnumerationComplete) for USBHHubEnumerationComplete
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.ProcessUSBDeviceStateMachine) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USB0HostIntHandler) refers to usb.o(i.USBIntStatusControl) for USBIntStatusControl
    usbhostenum.o(i.USB0HostIntHandler) refers to usbhostenum.o(i.USBHostIntHandlerInternal) for USBHostIntHandlerInternal
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostAddrSet) for USBHostAddrSet
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostEndpointConfig) for USBHostEndpointConfig
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostHubAddrSet) for USBHostHubAddrSet
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbhostenum.o(i.USBHCDControlTransfer) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostEndpointStatusClear) for USBHostEndpointStatusClear
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBFIFOFlush) for USBFIFOFlush
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostRequestStatus) for USBHostRequestStatus
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostRequestIN) for USBHostRequestIN
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBEndpointDataGet) for USBEndpointDataGet
    usbhostenum.o(i.USBHCDControlTransfer) refers to usb.o(i.USBHostEndpointDataAck) for USBHostEndpointDataAck
    usbhostenum.o(i.USBHCDControlTransfer) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhostenum.o(i.USBHCDControlTransfer) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDevAddress) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDevClass) refers to usbdesc.o(i.USBDescGetInterface) for USBDescGetInterface
    usbhostenum.o(i.USBHCDDevClass) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDevHubPort) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDevProtocol) refers to usbdesc.o(i.USBDescGetInterface) for USBDescGetInterface
    usbhostenum.o(i.USBHCDDevProtocol) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDevSubClass) refers to usbdesc.o(i.USBDescGetInterface) for USBDescGetInterface
    usbhostenum.o(i.USBHCDDevSubClass) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDeviceDisconnected) refers to usbhostenum.o(i.InternalUSBHCDSendEvent) for InternalUSBHCDSendEvent
    usbhostenum.o(i.USBHCDDeviceDisconnected) refers to usbhostenum.o(i.ConfigDescFree) for ConfigDescFree
    usbhostenum.o(i.USBHCDDeviceDisconnected) refers to usbmode.o(i.OTGDeviceDisconnect) for OTGDeviceDisconnect
    usbhostenum.o(i.USBHCDDeviceDisconnected) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDDeviceDisconnected) refers to usbmode.o(.data) for g_iUSBMode
    usbhostenum.o(i.USBHCDEventDisable) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDEventEnable) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDFeatureSet) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDFeatureSet) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDHubDeviceConnected) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDHubDeviceDisconnected) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDInit) refers to sysctl.o(i.SysCtlPeripheralReset) for SysCtlPeripheralReset
    usbhostenum.o(i.USBHCDInit) refers to sysctl.o(i.SysCtlPeripheralEnable) for SysCtlPeripheralEnable
    usbhostenum.o(i.USBHCDInit) refers to sysctl.o(i.SysCtlUSBPLLEnable) for SysCtlUSBPLLEnable
    usbhostenum.o(i.USBHCDInit) refers to usb.o(i.USBClockEnable) for USBClockEnable
    usbhostenum.o(i.USBHCDInit) refers to usb.o(i.USBULPIEnable) for USBULPIEnable
    usbhostenum.o(i.USBHCDInit) refers to usbulpi.o(i.ULPIConfigSet) for ULPIConfigSet
    usbhostenum.o(i.USBHCDInit) refers to usb.o(i.USBULPIDisable) for USBULPIDisable
    usbhostenum.o(i.USBHCDInit) refers to usb.o(i.USBHostMode) for USBHostMode
    usbhostenum.o(i.USBHCDInit) refers to usbhostenum.o(i.USBHCDInitInternal) for USBHCDInitInternal
    usbhostenum.o(i.USBHCDInit) refers to usbmode.o(.data) for g_iUSBMode
    usbhostenum.o(i.USBHCDInit) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDInit) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBNumEndpointsGet) for USBNumEndpointsGet
    usbhostenum.o(i.USBHCDInitInternal) refers to usbhhub.o(i.USBHHubInit) for USBHHubInit
    usbhostenum.o(i.USBHCDInitInternal) refers to usbdma.o(i.USBLibDMAInit) for USBLibDMAInit
    usbhostenum.o(i.USBHCDInitInternal) refers to usbtick.o(i.InternalUSBTickInit) for InternalUSBTickInit
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBHostEndpointConfig) for USBHostEndpointConfig
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBIntEnableControl) for USBIntEnableControl
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBIntEnableEndpoint) for USBIntEnableEndpoint
    usbhostenum.o(i.USBHCDInitInternal) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhostenum.o(i.USBHCDInitInternal) refers to usbhostenum.o(i.USBHCDPowerConfigSet) for USBHCDPowerConfigSet
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBHostPwrEnable) for USBHostPwrEnable
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBOTGSessionRequest) for USBOTGSessionRequest
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBHostLPMConfig) for USBHostLPMConfig
    usbhostenum.o(i.USBHCDInitInternal) refers to usb.o(i.USBLPMIntEnable) for USBLPMIntEnable
    usbhostenum.o(i.USBHCDInitInternal) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDInitInternal) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDInitInternal) refers to usbmode.o(.data) for g_iUSBMode
    usbhostenum.o(i.USBHCDLPMResume) refers to usb.o(i.USBHostLPMResume) for USBHostLPMResume
    usbhostenum.o(i.USBHCDLPMSleep) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHCDLPMSleep) refers to usb.o(i.USBHostLPMSend) for USBHostLPMSend
    usbhostenum.o(i.USBHCDLPMSleep) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhostenum.o(i.USBHCDLPMSleep) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDMain) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHCDMain) refers to usbhostenum.o(i.InternalUSBHCDSendEvent) for InternalUSBHCDSendEvent
    usbhostenum.o(i.USBHCDMain) refers to usbhostenum.o(i.USBHostCheckPipes) for USBHostCheckPipes
    usbhostenum.o(i.USBHCDMain) refers to usbhhub.o(i.USBHHubMain) for USBHHubMain
    usbhostenum.o(i.USBHCDMain) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhostenum.o(i.USBHCDMain) refers to usbhostenum.o(i.ProcessUSBDeviceStateMachine) for ProcessUSBDeviceStateMachine
    usbhostenum.o(i.USBHCDMain) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeAlloc) refers to usbhostenum.o(i.USBHCDPipeAllocSize) for USBHCDPipeAllocSize
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usb.o(i.USBHostEndpointStatusClear) for USBHostEndpointStatusClear
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usb.o(i.USBEndpointDataToggleClear) for USBEndpointDataToggleClear
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usb.o(i.USBFIFOConfigSet) for USBFIFOConfigSet
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usb.o(i.USBHostAddrSet) for USBHostAddrSet
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usb.o(i.USBHostHubAddrSet) for USBHostHubAddrSet
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeAllocSize) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPipeConfig) refers to usb.o(i.USBHostEndpointConfig) for USBHostEndpointConfig
    usbhostenum.o(i.USBHCDPipeConfig) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPipeConfig) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeDataAck) refers to usb.o(i.USBHostEndpointDataAck) for USBHostEndpointDataAck
    usbhostenum.o(i.USBHCDPipeDataAck) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeFree) refers to usb.o(i.USBHostAddrSet) for USBHostAddrSet
    usbhostenum.o(i.USBHCDPipeFree) refers to usb.o(i.USBHostHubAddrSet) for USBHostHubAddrSet
    usbhostenum.o(i.USBHCDPipeFree) refers to usb.o(i.USBHostRequestINClear) for USBHostRequestINClear
    usbhostenum.o(i.USBHCDPipeFree) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPipeFree) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeRead) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHCDPipeRead) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhostenum.o(i.USBHCDPipeRead) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbhostenum.o(i.USBHCDPipeRead) refers to usb.o(i.USBHostRequestIN) for USBHostRequestIN
    usbhostenum.o(i.USBHCDPipeRead) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.USBHCDPipeRead) refers to usb.o(i.USBEndpointDataToggleClear) for USBEndpointDataToggleClear
    usbhostenum.o(i.USBHCDPipeRead) refers to sysctl.o(.emb_text) for SysCtlDelay
    usbhostenum.o(i.USBHCDPipeRead) refers to usb.o(i.USBHostEndpointDataAck) for USBHostEndpointDataAck
    usbhostenum.o(i.USBHCDPipeRead) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeRead) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPipeReadNonBlocking) refers to usb.o(i.USBEndpointDataGet) for USBEndpointDataGet
    usbhostenum.o(i.USBHCDPipeReadNonBlocking) refers to usb.o(i.USBHostEndpointDataAck) for USBHostEndpointDataAck
    usbhostenum.o(i.USBHCDPipeReadNonBlocking) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeSchedule) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbhostenum.o(i.USBHCDPipeSchedule) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbhostenum.o(i.USBHCDPipeSchedule) refers to usb.o(i.USBHostRequestIN) for USBHostRequestIN
    usbhostenum.o(i.USBHCDPipeSchedule) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbhostenum.o(i.USBHCDPipeSchedule) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeTransferSizeGet) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeWrite) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHCDPipeWrite) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhostenum.o(i.USBHCDPipeWrite) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbhostenum.o(i.USBHCDPipeWrite) refers to usb.o(i.USBEndpointDataPut) for USBEndpointDataPut
    usbhostenum.o(i.USBHCDPipeWrite) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbhostenum.o(i.USBHCDPipeWrite) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.USBHCDPipeWrite) refers to usb.o(i.USBEndpointDataToggleClear) for USBEndpointDataToggleClear
    usbhostenum.o(i.USBHCDPipeWrite) refers to sysctl.o(.emb_text) for SysCtlDelay
    usbhostenum.o(i.USBHCDPipeWrite) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDPipeWrite) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPowerAutomatic) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPowerConfigGet) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPowerConfigInit) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDPowerConfigSet) refers to usb.o(i.USBHostPwrConfig) for USBHostPwrConfig
    usbhostenum.o(i.USBHCDPowerConfigSet) refers to usb.o(i.USBHostPwrEnable) for USBHostPwrEnable
    usbhostenum.o(i.USBHCDPowerConfigSet) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDRegisterDrivers) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDReset) refers to usb.o(i.USBHostReset) for USBHostReset
    usbhostenum.o(i.USBHCDReset) refers to sysctl.o(.emb_text) for SysCtlDelay
    usbhostenum.o(i.USBHCDReset) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDResume) refers to usb.o(i.USBHostResume) for USBHostResume
    usbhostenum.o(i.USBHCDResume) refers to sysctl.o(.emb_text) for SysCtlDelay
    usbhostenum.o(i.USBHCDResume) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDSetAddress) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.USBHCDSetAddress) refers to sysctl.o(.emb_text) for SysCtlDelay
    usbhostenum.o(i.USBHCDSetAddress) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHCDSetAddress) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHCDSetConfig) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.USBHCDSetInterface) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.USBHCDStringDescriptorGet) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhostenum.o(i.USBHCDSuspend) refers to usb.o(i.USBHostSuspend) for USBHostSuspend
    usbhostenum.o(i.USBHCDTerm) refers to usb.o(i.USBOTGSessionRequest) for USBOTGSessionRequest
    usbhostenum.o(i.USBHCDTerm) refers to usb.o(i.USBHostPwrDisable) for USBHostPwrDisable
    usbhostenum.o(i.USBHCDTerm) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHCDTerm) refers to usb.o(i.USBIntDisableControl) for USBIntDisableControl
    usbhostenum.o(i.USBHCDTerm) refers to usb.o(i.USBIntDisableEndpoint) for USBIntDisableEndpoint
    usbhostenum.o(i.USBHCDTerm) refers to usbhostenum.o(i.ConfigDescFree) for ConfigDescFree
    usbhostenum.o(i.USBHCDTerm) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHostCheckPipes) refers to usbhostenum.o(.data) for .data
    usbhostenum.o(i.USBHostCheckPipes) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBHostPwrDisable) for USBHostPwrDisable
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBHostPwrEnable) for USBHostPwrEnable
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBOTGSessionRequest) for USBOTGSessionRequest
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBOTGMode) for USBOTGMode
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usbtick.o(i.InternalUSBStartOfFrameTick) for InternalUSBStartOfFrameTick
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBLPMIntStatus) for USBLPMIntStatus
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBHostEndpointDataAck) for USBHostEndpointDataAck
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBEndpointDataSend) for USBEndpointDataSend
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBIntStatusEndpoint) for USBIntStatusEndpoint
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBEndpointStatus) for USBEndpointStatus
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBHostEndpointStatusClear) for USBHostEndpointStatusClear
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usbdma.o(i.USBLibDMAAddrGet) for USBLibDMAAddrGet
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBEndpointDataAvail) for USBEndpointDataAvail
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usb.o(i.USBEndpointDataGet) for USBEndpointDataGet
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usbhostenum.o(.bss) for .bss
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usbmode.o(.data) for g_iUSBMode
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usbtick.o(.data) for g_ui32USBSOFCount
    usbhostenum.o(i.USBHostIntHandlerInternal) refers to usbhostenum.o(.data) for .data
    usbringbuf.o(i.USBRingBufAdvanceRead) refers to interrupt.o(i.IntMasterDisable) for IntMasterDisable
    usbringbuf.o(i.USBRingBufAdvanceRead) refers to interrupt.o(i.IntMasterEnable) for IntMasterEnable
    usbringbuf.o(i.USBRingBufFlush) refers to interrupt.o(i.IntMasterDisable) for IntMasterDisable
    usbringbuf.o(i.USBRingBufFlush) refers to interrupt.o(i.IntMasterEnable) for IntMasterEnable
    usbringbuf.o(i.USBRingBufRead) refers to interrupt.o(i.IntMasterDisable) for IntMasterDisable
    usbringbuf.o(i.USBRingBufRead) refers to interrupt.o(i.IntMasterEnable) for IntMasterEnable
    usbringbuf.o(i.USBRingBufReadOne) refers to interrupt.o(i.IntMasterDisable) for IntMasterDisable
    usbringbuf.o(i.USBRingBufReadOne) refers to interrupt.o(i.IntMasterEnable) for IntMasterEnable
    usbringbuf.o(i.USBRingBufWrite) refers to interrupt.o(i.IntMasterDisable) for IntMasterDisable
    usbringbuf.o(i.USBRingBufWrite) refers to interrupt.o(i.IntMasterEnable) for IntMasterEnable
    usbringbuf.o(i.USBRingBufWriteOne) refers to interrupt.o(i.IntMasterDisable) for IntMasterDisable
    usbringbuf.o(i.USBRingBufWriteOne) refers to interrupt.o(i.IntMasterEnable) for IntMasterEnable
    usbtick.o(i.InternalUSBRegisterTickHandler) refers to usbtick.o(.bss) for .bss
    usbtick.o(i.InternalUSBStartOfFrameTick) refers to usbtick.o(.data) for .data
    usbtick.o(i.InternalUSBStartOfFrameTick) refers to usbtick.o(.bss) for .bss
    usbtick.o(i.InternalUSBTickInit) refers to usbtick.o(.data) for .data
    usbtick.o(i.InternalUSBTickInit) refers to usbtick.o(.bss) for .bss
    usbtick.o(i.InternalUSBTickReset) refers to usbtick.o(.data) for .data
    usbdcdesc.o(i.USBDCDConfigDescGet) refers to usbdesc.o(i.USBDescGetNum) for USBDescGetNum
    usbdcdesc.o(i.USBDCDConfigDescGet) refers to usbdesc.o(i.USBDescGet) for USBDescGet
    usbdcdesc.o(i.USBDCDConfigDescGetNum) refers to usbdesc.o(i.USBDescGetNum) for USBDescGetNum
    usbdcdesc.o(i.USBDCDConfigGetInterface) refers to usbdesc.o(i.USBDescGetNum) for USBDescGetNum
    usbdcdesc.o(i.USBDCDConfigGetInterface) refers to usbdesc.o(i.USBDescGet) for USBDescGet
    usbdcdesc.o(i.USBDCDConfigGetInterfaceEndpoint) refers to usbdcdesc.o(i.USBDCDConfigGetInterface) for USBDCDConfigGetInterface
    usbdconfig.o(i.USBDeviceConfig) refers to usbdcdesc.o(i.USBDCDConfigDescGetNum) for USBDCDConfigDescGetNum
    usbdconfig.o(i.USBDeviceConfig) refers to usbdcdesc.o(i.USBDCDConfigDescGet) for USBDCDConfigDescGet
    usbdconfig.o(i.USBDeviceConfig) refers to usbdcdesc.o(i.USBDCDConfigGetInterface) for USBDCDConfigGetInterface
    usbdconfig.o(i.USBDeviceConfig) refers to usbdcdesc.o(i.USBDCDConfigGetInterfaceEndpoint) for USBDCDConfigGetInterfaceEndpoint
    usbdconfig.o(i.USBDeviceConfig) refers to usb.o(i.USBDevEndpointConfigSet) for USBDevEndpointConfigSet
    usbdconfig.o(i.USBDeviceConfig) refers to usb.o(i.USBFIFOConfigSet) for USBFIFOConfigSet
    usbdconfig.o(i.USBDeviceConfigAlternate) refers to usbdcdesc.o(i.USBDCDConfigDescGetNum) for USBDCDConfigDescGetNum
    usbdconfig.o(i.USBDeviceConfigAlternate) refers to usbdcdesc.o(i.USBDCDConfigGetInterface) for USBDCDConfigGetInterface
    usbdconfig.o(i.USBDeviceConfigAlternate) refers to usbdcdesc.o(i.USBDCDConfigGetInterfaceEndpoint) for USBDCDConfigGetInterfaceEndpoint
    usbdconfig.o(i.USBDeviceConfigAlternate) refers to usb.o(i.USBDevEndpointConfigSet) for USBDevEndpointConfigSet
    usbdma.o(i.USBLibDMAInit) refers to usb.o(i.USBControllerVersion) for USBControllerVersion
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(.bss) for .bss
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBArbSizeSet) for uDMAUSBArbSizeSet
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelAllocate) for uDMAUSBChannelAllocate
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelDisable) for uDMAUSBChannelDisable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelEnable) for uDMAUSBChannelEnable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelIntEnable) for uDMAUSBChannelIntEnable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelIntDisable) for uDMAUSBChannelIntDisable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelRelease) for uDMAUSBChannelRelease
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBChannelStatus) for uDMAUSBChannelStatus
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.DMAUSBIntHandler) for DMAUSBIntHandler
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBIntStatus) for uDMAUSBIntStatus
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.DMAUSBIntStatusClear) for DMAUSBIntStatusClear
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.DMAUSBStatus) for DMAUSBStatus
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBTransfer) for uDMAUSBTransfer
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.uDMAUSBUnitSizeSet) for uDMAUSBUnitSizeSet
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBArbSizeSet) for iDMAUSBArbSizeSet
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelAllocate) for iDMAUSBChannelAllocate
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelStatus) for iDMAUSBChannelStatus
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBIntStatus) for iDMAUSBIntStatus
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelIntEnable) for iDMAUSBChannelIntEnable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelIntDisable) for iDMAUSBChannelIntDisable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBTransfer) for iDMAUSBTransfer
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelRelease) for iDMAUSBChannelRelease
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelEnable) for iDMAUSBChannelEnable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBChannelDisable) for iDMAUSBChannelDisable
    usbdma.o(i.USBLibDMAInit) refers to usbdma.o(i.iDMAUSBUnitSizeSet) for iDMAUSBUnitSizeSet
    usbdma.o(i.iDMAUSBChannelAllocate) refers to usb.o(i.USBDMAChannelDisable) for USBDMAChannelDisable
    usbdma.o(i.iDMAUSBChannelDisable) refers to usb.o(i.USBDMAChannelDisable) for USBDMAChannelDisable
    usbdma.o(i.iDMAUSBChannelDisable) refers to usb.o(i.USBDMAChannelIntDisable) for USBDMAChannelIntDisable
    usbdma.o(i.iDMAUSBChannelEnable) refers to interrupt.o(i.IntIsEnabled) for IntIsEnabled
    usbdma.o(i.iDMAUSBChannelEnable) refers to interrupt.o(i.IntDisable) for IntDisable
    usbdma.o(i.iDMAUSBChannelEnable) refers to usb.o(i.USBDMAChannelIntEnable) for USBDMAChannelIntEnable
    usbdma.o(i.iDMAUSBChannelEnable) refers to usb.o(i.USBDMAChannelEnable) for USBDMAChannelEnable
    usbdma.o(i.iDMAUSBChannelEnable) refers to interrupt.o(i.IntEnable) for IntEnable
    usbdma.o(i.iDMAUSBChannelIntDisable) refers to usb.o(i.USBDMAChannelIntDisable) for USBDMAChannelIntDisable
    usbdma.o(i.iDMAUSBChannelIntEnable) refers to usb.o(i.USBDMAChannelIntEnable) for USBDMAChannelIntEnable
    usbdma.o(i.iDMAUSBChannelRelease) refers to usb.o(i.USBDMAChannelDisable) for USBDMAChannelDisable
    usbdma.o(i.iDMAUSBChannelStatus) refers to usb.o(i.USBDMAChannelStatus) for USBDMAChannelStatus
    usbdma.o(i.iDMAUSBIntStatus) refers to usb.o(i.USBDMAChannelIntStatus) for USBDMAChannelIntStatus
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBDMAChannelAddressSet) for USBDMAChannelAddressSet
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBDMAChannelCountSet) for USBDMAChannelCountSet
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBEndpointPacketCountSet) for USBEndpointPacketCountSet
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBEndpointDMAConfigSet) for USBEndpointDMAConfigSet
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBDMAChannelConfigSet) for USBDMAChannelConfigSet
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBEndpointDMAEnable) for USBEndpointDMAEnable
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBDMAChannelEnable) for USBDMAChannelEnable
    usbdma.o(i.iDMAUSBTransfer) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbdma.o(i.uDMAUSBArbSizeSet) refers to udma.o(i.uDMAChannelControlSet) for uDMAChannelControlSet
    usbdma.o(i.uDMAUSBChannelAllocate) refers to usb.o(i.USBEndpointDMAChannel) for USBEndpointDMAChannel
    usbdma.o(i.uDMAUSBChannelAllocate) refers to udma.o(i.uDMAChannelAttributeDisable) for uDMAChannelAttributeDisable
    usbdma.o(i.uDMAUSBChannelAllocate) refers to udma.o(i.uDMAChannelControlSet) for uDMAChannelControlSet
    usbdma.o(i.uDMAUSBChannelAllocate) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbdma.o(i.uDMAUSBChannelDisable) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbdma.o(i.uDMAUSBChannelDisable) refers to udma.o(i.uDMAChannelDisable) for uDMAChannelDisable
    usbdma.o(i.uDMAUSBChannelEnable) refers to interrupt.o(i.IntIsEnabled) for IntIsEnabled
    usbdma.o(i.uDMAUSBChannelEnable) refers to interrupt.o(i.IntDisable) for IntDisable
    usbdma.o(i.uDMAUSBChannelEnable) refers to usb.o(i.USBEndpointDMAEnable) for USBEndpointDMAEnable
    usbdma.o(i.uDMAUSBChannelEnable) refers to udma.o(i.uDMAChannelEnable) for uDMAChannelEnable
    usbdma.o(i.uDMAUSBChannelEnable) refers to interrupt.o(i.IntEnable) for IntEnable
    usbdma.o(i.uDMAUSBChannelRelease) refers to udma.o(i.uDMAChannelAttributeDisable) for uDMAChannelAttributeDisable
    usbdma.o(i.uDMAUSBChannelRelease) refers to usb.o(i.USBEndpointDMADisable) for USBEndpointDMADisable
    usbdma.o(i.uDMAUSBIntStatus) refers to udma.o(i.uDMAChannelModeGet) for uDMAChannelModeGet
    usbdma.o(i.uDMAUSBTransfer) refers to usb.o(i.USBFIFOAddrGet) for USBFIFOAddrGet
    usbdma.o(i.uDMAUSBTransfer) refers to udma.o(i.uDMAChannelTransferSet) for uDMAChannelTransferSet
    usbdma.o(i.uDMAUSBTransfer) refers to usb.o(i.USBEndpointPacketCountSet) for USBEndpointPacketCountSet
    usbdma.o(i.uDMAUSBTransfer) refers to usb.o(i.USBEndpointDMAConfigSet) for USBEndpointDMAConfigSet
    usbdma.o(i.uDMAUSBTransfer) refers to usbdma.o(i.uDMAUSBChannelEnable) for uDMAUSBChannelEnable
    usbdma.o(i.uDMAUSBUnitSizeSet) refers to udma.o(i.uDMAChannelControlSet) for uDMAChannelControlSet
    usbhhub.o(i.HubDriverClose) refers to usbhostenum.o(i.USBHCDHubDeviceDisconnected) for USBHCDHubDeviceDisconnected
    usbhhub.o(i.HubDriverClose) refers to usbhostenum.o(i.USBHCDPipeFree) for USBHCDPipeFree
    usbhhub.o(i.HubDriverClose) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.HubDriverOpen) refers to usbdesc.o(i.USBDescGetInterface) for USBDescGetInterface
    usbhhub.o(i.HubDriverOpen) refers to usbdesc.o(i.USBDescGetInterfaceEndpoint) for USBDescGetInterfaceEndpoint
    usbhhub.o(i.HubDriverOpen) refers to usbhostenum.o(i.USBHCDPipeAlloc) for USBHCDPipeAlloc
    usbhhub.o(i.HubDriverOpen) refers to usbhostenum.o(i.USBHCDPipeConfig) for USBHCDPipeConfig
    usbhhub.o(i.HubDriverOpen) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhhub.o(i.HubDriverOpen) refers to usbhostenum.o(i.USBHCDPipeFree) for USBHCDPipeFree
    usbhhub.o(i.HubDriverOpen) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.HubDriverOpen) refers to usbhhub.o(i.HubIntINCallback) for HubIntINCallback
    usbhhub.o(i.HubIntINCallback) refers to usbhostenum.o(i.USBHCDPipeSchedule) for USBHCDPipeSchedule
    usbhhub.o(i.HubIntINCallback) refers to usbhostenum.o(i.USBHCDPipeDataAck) for USBHCDPipeDataAck
    usbhhub.o(i.HubIntINCallback) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.HubIntINCallback) refers to usbhhub.o(.data) for .data
    usbhhub.o(i.USBHHubEnumerationComplete) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.USBHHubEnumerationError) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.USBHHubInit) refers to usbhhub.o(.data) for .data
    usbhhub.o(i.USBHHubInit) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.USBHHubLPMSleep) refers to usbhostenum.o(i.USBHCDLPMSleep) for USBHCDLPMSleep
    usbhhub.o(i.USBHHubLPMStatus) refers to usbhostenum.o(i.USBHCDLPMStatus) for USBHCDLPMStatus
    usbhhub.o(i.USBHHubMain) refers to usbhostenum.o(i.USBHCDControlTransfer) for USBHCDControlTransfer
    usbhhub.o(i.USBHHubMain) refers to usbhostenum.o(i.USBHCDHubDeviceConnected) for USBHCDHubDeviceConnected
    usbhhub.o(i.USBHHubMain) refers to interrupt.o(i.IntDisable) for IntDisable
    usbhhub.o(i.USBHHubMain) refers to interrupt.o(i.IntEnable) for IntEnable
    usbhhub.o(i.USBHHubMain) refers to usbhostenum.o(i.USBHCDHubDeviceDisconnected) for USBHCDHubDeviceDisconnected
    usbhhub.o(i.USBHHubMain) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(i.USBHHubMain) refers to usbhhub.o(.data) for .data
    usbhhub.o(i.USBHHubOpen) refers to usbhhub.o(.bss) for .bss
    usbhhub.o(.constdata) refers to usbhhub.o(i.HubDriverOpen) for HubDriverOpen
    usbhhub.o(.constdata) refers to usbhhub.o(i.HubDriverClose) for HubDriverClose
    usbulpi.o(i.ULPIConfigSet) refers to usb.o(i.USBULPIRegRead) for USBULPIRegRead
    usbulpi.o(i.ULPIConfigSet) refers to usb.o(i.USBULPIRegWrite) for USBULPIRegWrite
    usbulpi.o(i.ULPIPowerTransceiver) refers to usb.o(i.USBULPIRegWrite) for USBULPIRegWrite
    udma.o(i.uDMAIntRegister) refers to interrupt.o(i.IntRegister) for IntRegister
    udma.o(i.uDMAIntRegister) refers to interrupt.o(i.IntEnable) for IntEnable
    udma.o(i.uDMAIntUnregister) refers to interrupt.o(i.IntDisable) for IntDisable
    udma.o(i.uDMAIntUnregister) refers to interrupt.o(i.IntUnregister) for IntUnregister
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_tm4c123.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_tm4c123.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing ano_scheduler.o(.rev16_text), (4 bytes).
    Removing ano_scheduler.o(.revsh_text), (4 bytes).
    Removing ano_scheduler.o(.rrx_text), (6 bytes).
    Removing user_task.o(.rev16_text), (4 bytes).
    Removing user_task.o(.revsh_text), (4 bytes).
    Removing user_task.o(.rrx_text), (6 bytes).
    Removing ano_dt_lx.o(.rev16_text), (4 bytes).
    Removing ano_dt_lx.o(.revsh_text), (4 bytes).
    Removing ano_dt_lx.o(.rrx_text), (6 bytes).
    Removing ano_lx.o(.rev16_text), (4 bytes).
    Removing ano_lx.o(.revsh_text), (4 bytes).
    Removing ano_lx.o(.rrx_text), (6 bytes).
    Removing lx_fc_ext_sensor.o(.rev16_text), (4 bytes).
    Removing lx_fc_ext_sensor.o(.revsh_text), (4 bytes).
    Removing lx_fc_ext_sensor.o(.rrx_text), (6 bytes).
    Removing lx_fc_fun.o(.rev16_text), (4 bytes).
    Removing lx_fc_fun.o(.revsh_text), (4 bytes).
    Removing lx_fc_fun.o(.rrx_text), (6 bytes).
    Removing lx_fc_fun.o(i.ACC_Calibrate), (56 bytes).
    Removing lx_fc_fun.o(i.GYR_Calibrate), (56 bytes).
    Removing lx_fc_fun.o(i.Horizontal_Move), (104 bytes).
    Removing lx_fc_state.o(.rev16_text), (4 bytes).
    Removing lx_fc_state.o(.revsh_text), (4 bytes).
    Removing lx_fc_state.o(.rrx_text), (6 bytes).
    Removing drv_bsp.o(.rev16_text), (4 bytes).
    Removing drv_bsp.o(.revsh_text), (4 bytes).
    Removing drv_bsp.o(.rrx_text), (6 bytes).
    Removing ano_math.o(.rev16_text), (4 bytes).
    Removing ano_math.o(.revsh_text), (4 bytes).
    Removing ano_math.o(.rrx_text), (6 bytes).
    Removing ano_math.o(i.To_180_degrees_db), (132 bytes).
    Removing ano_math.o(i.fast_atan2), (380 bytes).
    Removing ano_math.o(i.fifo), (42 bytes).
    Removing ano_math.o(i.length_limit), (144 bytes).
    Removing ano_math.o(i.mx_sin), (340 bytes).
    Removing ano_math.o(i.my_HPF), (368 bytes).
    Removing ano_math.o(i.my_abs), (26 bytes).
    Removing ano_math.o(i.my_atan), (24 bytes).
    Removing ano_math.o(i.my_cos), (152 bytes).
    Removing ano_math.o(i.my_deadzone_2), (48 bytes).
    Removing ano_math.o(i.my_sin), (116 bytes).
    Removing ano_math.o(i.my_sqrt), (20 bytes).
    Removing ano_math.o(i.my_sqrt_reciprocal), (80 bytes).
    Removing ano_math.o(i.rot_vec_2), (84 bytes).
    Removing ano_math.o(i.vec_2_cross_product), (26 bytes).
    Removing ano_math.o(i.vec_2_dot_product), (26 bytes).
    Removing ano_math.o(i.vec_3_cross_product_err_sinx), (86 bytes).
    Removing ano_math.o(i.vec_3_dot_product), (38 bytes).
    Removing ano_math.o(.constdata), (1028 bytes).
    Removing drv_anoof.o(.rev16_text), (4 bytes).
    Removing drv_anoof.o(.revsh_text), (4 bytes).
    Removing drv_anoof.o(.rrx_text), (6 bytes).
    Removing drv_ubloxgps.o(.rev16_text), (4 bytes).
    Removing drv_ubloxgps.o(.revsh_text), (4 bytes).
    Removing drv_ubloxgps.o(.rrx_text), (6 bytes).
    Removing drv_ubloxgps.o(i.GPS_Rate_L), (16 bytes).
    Removing drv_sys.o(.rev16_text), (4 bytes).
    Removing drv_sys.o(.revsh_text), (4 bytes).
    Removing drv_sys.o(.rrx_text), (6 bytes).
    Removing drv_adc.o(.rev16_text), (4 bytes).
    Removing drv_adc.o(.revsh_text), (4 bytes).
    Removing drv_adc.o(.rrx_text), (6 bytes).
    Removing drv_led.o(.rev16_text), (4 bytes).
    Removing drv_led.o(.revsh_text), (4 bytes).
    Removing drv_led.o(.rrx_text), (6 bytes).
    Removing drv_pwmout.o(.rev16_text), (4 bytes).
    Removing drv_pwmout.o(.revsh_text), (4 bytes).
    Removing drv_pwmout.o(.rrx_text), (6 bytes).
    Removing drv_pwmout.o(i.DrvHeatSet), (52 bytes).
    Removing drv_rcin.o(.rev16_text), (4 bytes).
    Removing drv_rcin.o(.revsh_text), (4 bytes).
    Removing drv_rcin.o(.rrx_text), (6 bytes).
    Removing drv_uart.o(.rev16_text), (4 bytes).
    Removing drv_uart.o(.revsh_text), (4 bytes).
    Removing drv_uart.o(.rrx_text), (6 bytes).
    Removing drv_uart.o(i.DrvUart2SendBuf), (52 bytes).
    Removing drv_uart.o(i.DrvUart3SendBuf), (52 bytes).
    Removing drv_uart.o(i.DrvUart4SendBuf), (52 bytes).
    Removing drv_usb.o(.rev16_text), (4 bytes).
    Removing drv_usb.o(.revsh_text), (4 bytes).
    Removing drv_usb.o(.rrx_text), (6 bytes).
    Removing drv_usb.o(i.ControlHandler), (156 bytes).
    Removing drv_usb.o(i.DrvUsbInit), (124 bytes).
    Removing drv_usb.o(i.DrvUsbRead), (40 bytes).
    Removing drv_usb.o(i.DrvUsbSend), (32 bytes).
    Removing drv_usb.o(i.RxHandler), (38 bytes).
    Removing drv_usb.o(i.TxHandler), (18 bytes).
    Removing drv_usb.o(.data), (17 bytes).
    Removing drv_timer.o(.rev16_text), (4 bytes).
    Removing drv_timer.o(.revsh_text), (4 bytes).
    Removing drv_timer.o(.rrx_text), (6 bytes).
    Removing startup_tm4c123.o(HEAP), (1280 bytes).
    Removing system_tm4c123.o(.rev16_text), (4 bytes).
    Removing system_tm4c123.o(.revsh_text), (4 bytes).
    Removing system_tm4c123.o(.rrx_text), (6 bytes).
    Removing system_tm4c123.o(i.SystemCoreClockUpdate), (196 bytes).
    Removing system_tm4c123.o(i.getOscClk), (244 bytes).
    Removing system_tm4c123.o(.data), (4 bytes).
    Removing usb_serial_structs.o(.bss), (512 bytes).
    Removing usb_serial_structs.o(.constdata), (216 bytes).
    Removing usb_serial_structs.o(.data), (208 bytes).
    Removing adc.o(i.ADCBusy), (8 bytes).
    Removing adc.o(i.ADCClockConfigGet), (32 bytes).
    Removing adc.o(i.ADCClockConfigSet), (28 bytes).
    Removing adc.o(i.ADCComparatorConfigure), (10 bytes).
    Removing adc.o(i.ADCComparatorIntClear), (4 bytes).
    Removing adc.o(i.ADCComparatorIntDisable), (18 bytes).
    Removing adc.o(i.ADCComparatorIntEnable), (16 bytes).
    Removing adc.o(i.ADCComparatorIntStatus), (4 bytes).
    Removing adc.o(i.ADCComparatorRegionSet), (14 bytes).
    Removing adc.o(i.ADCComparatorReset), (40 bytes).
    Removing adc.o(i.ADCHardwareOversampleConfigure), (18 bytes).
    Removing adc.o(i.ADCIntClear), (10 bytes).
    Removing adc.o(i.ADCIntClearEx), (4 bytes).
    Removing adc.o(i.ADCIntDisable), (16 bytes).
    Removing adc.o(i.ADCIntDisableEx), (10 bytes).
    Removing adc.o(i.ADCIntEnable), (16 bytes).
    Removing adc.o(i.ADCIntEnableEx), (8 bytes).
    Removing adc.o(i.ADCIntStatus), (52 bytes).
    Removing adc.o(i.ADCIntStatusEx), (22 bytes).
    Removing adc.o(i.ADCIntUnregister), (108 bytes).
    Removing adc.o(i.ADCPhaseDelayGet), (4 bytes).
    Removing adc.o(i.ADCPhaseDelaySet), (4 bytes).
    Removing adc.o(i.ADCProcessorTrigger), (26 bytes).
    Removing adc.o(i.ADCReferenceGet), (8 bytes).
    Removing adc.o(i.ADCReferenceSet), (12 bytes).
    Removing adc.o(i.ADCSequenceConfigure), (96 bytes).
    Removing adc.o(i.ADCSequenceDMADisable), (18 bytes).
    Removing adc.o(i.ADCSequenceDMAEnable), (16 bytes).
    Removing adc.o(i.ADCSequenceDataGet), (36 bytes).
    Removing adc.o(i.ADCSequenceDisable), (16 bytes).
    Removing adc.o(i.ADCSequenceEnable), (14 bytes).
    Removing adc.o(i.ADCSequenceOverflow), (12 bytes).
    Removing adc.o(i.ADCSequenceOverflowClear), (10 bytes).
    Removing adc.o(i.ADCSequenceStepConfigure), (154 bytes).
    Removing adc.o(i.ADCSequenceUnderflow), (12 bytes).
    Removing adc.o(i.ADCSequenceUnderflowClear), (10 bytes).
    Removing adc.o(i.ADCSoftwareOversampleConfigure), (44 bytes).
    Removing adc.o(i.ADCSoftwareOversampleDataGet), (88 bytes).
    Removing adc.o(i.ADCSoftwareOversampleStepConfigure), (168 bytes).
    Removing adc.o(.data), (6 bytes).
    Removing sysctl.o(.emb_text), (6 bytes).
    Removing sysctl.o(i.SysCtlAltClkConfig), (12 bytes).
    Removing sysctl.o(i.SysCtlClockFreqSet), (740 bytes).
    Removing sysctl.o(i.SysCtlClockOutConfig), (20 bytes).
    Removing sysctl.o(i.SysCtlClockSet), (288 bytes).
    Removing sysctl.o(i.SysCtlDeepSleep), (32 bytes).
    Removing sysctl.o(i.SysCtlDeepSleepClockConfigSet), (112 bytes).
    Removing sysctl.o(i.SysCtlDeepSleepClockSet), (12 bytes).
    Removing sysctl.o(i.SysCtlDeepSleepPowerSet), (12 bytes).
    Removing sysctl.o(i.SysCtlFlashSectorSizeGet), (56 bytes).
    Removing sysctl.o(i.SysCtlFlashSizeGet), (56 bytes).
    Removing sysctl.o(i.SysCtlGPIOAHBDisable), (28 bytes).
    Removing sysctl.o(i.SysCtlGPIOAHBEnable), (24 bytes).
    Removing sysctl.o(i.SysCtlIntClear), (12 bytes).
    Removing sysctl.o(i.SysCtlIntDisable), (16 bytes).
    Removing sysctl.o(i.SysCtlIntEnable), (16 bytes).
    Removing sysctl.o(i.SysCtlIntRegister), (20 bytes).
    Removing sysctl.o(i.SysCtlIntStatus), (16 bytes).
    Removing sysctl.o(i.SysCtlIntUnregister), (18 bytes).
    Removing sysctl.o(i.SysCtlLDODeepSleepGet), (12 bytes).
    Removing sysctl.o(i.SysCtlLDODeepSleepSet), (12 bytes).
    Removing sysctl.o(i.SysCtlLDOSleepGet), (12 bytes).
    Removing sysctl.o(i.SysCtlLDOSleepSet), (12 bytes).
    Removing sysctl.o(i.SysCtlMOSCConfigSet), (12 bytes).
    Removing sysctl.o(i.SysCtlNMIClear), (16 bytes).
    Removing sysctl.o(i.SysCtlNMIStatus), (12 bytes).
    Removing sysctl.o(i.SysCtlPIOSCCalibrate), (76 bytes).
    Removing sysctl.o(i.SysCtlPWMClockGet), (24 bytes).
    Removing sysctl.o(i.SysCtlPWMClockSet), (20 bytes).
    Removing sysctl.o(i.SysCtlPeripheralClockGating), (68 bytes).
    Removing sysctl.o(i.SysCtlPeripheralDeepSleepDisable), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralDeepSleepEnable), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralDisable), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralEnable), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralPowerOff), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralPowerOn), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralPresent), (44 bytes).
    Removing sysctl.o(i.SysCtlPeripheralReady), (44 bytes).
    Removing sysctl.o(i.SysCtlPeripheralReset), (60 bytes).
    Removing sysctl.o(i.SysCtlPeripheralSleepDisable), (40 bytes).
    Removing sysctl.o(i.SysCtlPeripheralSleepEnable), (40 bytes).
    Removing sysctl.o(i.SysCtlReset), (16 bytes).
    Removing sysctl.o(i.SysCtlResetBehaviorGet), (12 bytes).
    Removing sysctl.o(i.SysCtlResetBehaviorSet), (12 bytes).
    Removing sysctl.o(i.SysCtlResetCauseClear), (16 bytes).
    Removing sysctl.o(i.SysCtlResetCauseGet), (12 bytes).
    Removing sysctl.o(i.SysCtlSRAMSizeGet), (16 bytes).
    Removing sysctl.o(i.SysCtlSleep), (4 bytes).
    Removing sysctl.o(i.SysCtlSleepPowerSet), (12 bytes).
    Removing sysctl.o(i.SysCtlUSBPLLDisable), (16 bytes).
    Removing sysctl.o(i.SysCtlUSBPLLEnable), (16 bytes).
    Removing sysctl.o(i.SysCtlVCOGet), (120 bytes).
    Removing sysctl.o(i.SysCtlVoltageEventClear), (16 bytes).
    Removing sysctl.o(i.SysCtlVoltageEventConfig), (12 bytes).
    Removing sysctl.o(i.SysCtlVoltageEventStatus), (12 bytes).
    Removing timer.o(i.TimerADCEventGet), (4 bytes).
    Removing timer.o(i.TimerADCEventSet), (4 bytes).
    Removing timer.o(i.TimerClockSourceGet), (6 bytes).
    Removing timer.o(i.TimerClockSourceSet), (6 bytes).
    Removing timer.o(i.TimerConfigure), (96 bytes).
    Removing timer.o(i.TimerControlEvent), (18 bytes).
    Removing timer.o(i.TimerControlLevel), (22 bytes).
    Removing timer.o(i.TimerControlStall), (22 bytes).
    Removing timer.o(i.TimerControlTrigger), (80 bytes).
    Removing timer.o(i.TimerControlWaitOnTrigger), (48 bytes).
    Removing timer.o(i.TimerDMAEventGet), (4 bytes).
    Removing timer.o(i.TimerDMAEventSet), (4 bytes).
    Removing timer.o(i.TimerDisable), (16 bytes).
    Removing timer.o(i.TimerEnable), (14 bytes).
    Removing timer.o(i.TimerIntDisable), (10 bytes).
    Removing timer.o(i.TimerIntEnable), (8 bytes).
    Removing timer.o(i.TimerIntStatus), (10 bytes).
    Removing timer.o(i.TimerIntUnregister), (104 bytes).
    Removing timer.o(i.TimerLoadGet), (10 bytes).
    Removing timer.o(i.TimerLoadGet64), (14 bytes).
    Removing timer.o(i.TimerLoadSet), (18 bytes).
    Removing timer.o(i.TimerLoadSet64), (6 bytes).
    Removing timer.o(i.TimerMatchGet), (10 bytes).
    Removing timer.o(i.TimerMatchGet64), (14 bytes).
    Removing timer.o(i.TimerMatchSet), (18 bytes).
    Removing timer.o(i.TimerMatchSet64), (6 bytes).
    Removing timer.o(i.TimerPrescaleGet), (10 bytes).
    Removing timer.o(i.TimerPrescaleMatchGet), (10 bytes).
    Removing timer.o(i.TimerPrescaleMatchSet), (18 bytes).
    Removing timer.o(i.TimerPrescaleSet), (18 bytes).
    Removing timer.o(i.TimerRTCDisable), (10 bytes).
    Removing timer.o(i.TimerRTCEnable), (10 bytes).
    Removing timer.o(i.TimerSynchronize), (4 bytes).
    Removing timer.o(i.TimerUpdateMode), (42 bytes).
    Removing timer.o(i.TimerValueGet), (10 bytes).
    Removing timer.o(i.TimerValueGet64), (14 bytes).
    Removing uart.o(i.UART9BitAddrSend), (38 bytes).
    Removing uart.o(i.UART9BitAddrSet), (10 bytes).
    Removing uart.o(i.UART9BitDisable), (14 bytes).
    Removing uart.o(i.UART9BitEnable), (14 bytes).
    Removing uart.o(i.UARTBreakCtl), (18 bytes).
    Removing uart.o(i.UARTBusy), (8 bytes).
    Removing uart.o(i.UARTCharGet), (12 bytes).
    Removing uart.o(i.UARTCharGetNonBlocking), (16 bytes).
    Removing uart.o(i.UARTCharPut), (12 bytes).
    Removing uart.o(i.UARTCharPutNonBlocking), (16 bytes).
    Removing uart.o(i.UARTCharsAvail), (10 bytes).
    Removing uart.o(i.UARTClockSourceGet), (6 bytes).
    Removing uart.o(i.UARTClockSourceSet), (6 bytes).
    Removing uart.o(i.UARTConfigGetExpClk), (46 bytes).
    Removing uart.o(i.UARTConfigSetExpClk), (110 bytes).
    Removing uart.o(i.UARTDMADisable), (10 bytes).
    Removing uart.o(i.UARTDMAEnable), (8 bytes).
    Removing uart.o(i.UARTDisable), (30 bytes).
    Removing uart.o(i.UARTDisableSIR), (10 bytes).
    Removing uart.o(i.UARTEnable), (20 bytes).
    Removing uart.o(i.UARTEnableSIR), (18 bytes).
    Removing uart.o(i.UARTFIFODisable), (10 bytes).
    Removing uart.o(i.UARTFIFOEnable), (10 bytes).
    Removing uart.o(i.UARTFIFOLevelGet), (16 bytes).
    Removing uart.o(i.UARTFIFOLevelSet), (6 bytes).
    Removing uart.o(i.UARTFlowControlGet), (8 bytes).
    Removing uart.o(i.UARTFlowControlSet), (12 bytes).
    Removing uart.o(i.UARTIntClear), (4 bytes).
    Removing uart.o(i.UARTIntDisable), (10 bytes).
    Removing uart.o(i.UARTIntEnable), (8 bytes).
    Removing uart.o(i.UARTIntStatus), (10 bytes).
    Removing uart.o(i.UARTIntUnregister), (88 bytes).
    Removing uart.o(i.UARTLoopbackEnable), (10 bytes).
    Removing uart.o(i.UARTModemControlClear), (14 bytes).
    Removing uart.o(i.UARTModemControlGet), (8 bytes).
    Removing uart.o(i.UARTModemControlSet), (12 bytes).
    Removing uart.o(i.UARTModemStatusGet), (10 bytes).
    Removing uart.o(i.UARTParityModeGet), (8 bytes).
    Removing uart.o(i.UARTParityModeSet), (12 bytes).
    Removing uart.o(i.UARTRxErrorClear), (6 bytes).
    Removing uart.o(i.UARTRxErrorGet), (8 bytes).
    Removing uart.o(i.UARTSmartCardDisable), (10 bytes).
    Removing uart.o(i.UARTSmartCardEnable), (22 bytes).
    Removing uart.o(i.UARTSpaceAvail), (10 bytes).
    Removing uart.o(i.UARTTxIntModeGet), (8 bytes).
    Removing uart.o(i.UARTTxIntModeSet), (12 bytes).
    Removing usb.o(i.USBClockDisable), (8 bytes).
    Removing usb.o(i.USBClockEnable), (10 bytes).
    Removing usb.o(i.USBControllerVersion), (10 bytes).
    Removing usb.o(i.USBDMAChannelAddressGet), (10 bytes).
    Removing usb.o(i.USBDMAChannelAddressSet), (10 bytes).
    Removing usb.o(i.USBDMAChannelConfigSet), (22 bytes).
    Removing usb.o(i.USBDMAChannelCountGet), (10 bytes).
    Removing usb.o(i.USBDMAChannelCountSet), (10 bytes).
    Removing usb.o(i.USBDMAChannelDisable), (18 bytes).
    Removing usb.o(i.USBDMAChannelEnable), (18 bytes).
    Removing usb.o(i.USBDMAChannelIntDisable), (18 bytes).
    Removing usb.o(i.USBDMAChannelIntEnable), (18 bytes).
    Removing usb.o(i.USBDMAChannelIntStatus), (6 bytes).
    Removing usb.o(i.USBDMAChannelStatus), (14 bytes).
    Removing usb.o(i.USBDMAChannelStatusClear), (20 bytes).
    Removing usb.o(i.USBDMANumChannels), (8 bytes).
    Removing usb.o(i.USBDevAddrGet), (4 bytes).
    Removing usb.o(i.USBDevAddrSet), (4 bytes).
    Removing usb.o(i.USBDevConnect), (10 bytes).
    Removing usb.o(i.USBDevDisconnect), (10 bytes).
    Removing usb.o(i.USBDevEndpointConfigGet), (144 bytes).
    Removing usb.o(i.USBDevEndpointConfigSet), (136 bytes).
    Removing usb.o(i.USBDevEndpointDataAck), (36 bytes).
    Removing usb.o(i.USBDevEndpointStall), (52 bytes).
    Removing usb.o(i.USBDevEndpointStallClear), (74 bytes).
    Removing usb.o(i.USBDevEndpointStatusClear), (94 bytes).
    Removing usb.o(i.USBDevLPMConfig), (6 bytes).
    Removing usb.o(i.USBDevLPMDisable), (14 bytes).
    Removing usb.o(i.USBDevLPMEnable), (14 bytes).
    Removing usb.o(i.USBDevLPMRemoteWake), (14 bytes).
    Removing usb.o(i.USBDevMode), (8 bytes).
    Removing usb.o(i.USBDevSpeedGet), (14 bytes).
    Removing usb.o(i.USBEndpointDMAChannel), (26 bytes).
    Removing usb.o(i.USBEndpointDMAConfigSet), (86 bytes).
    Removing usb.o(i.USBEndpointDMADisable), (36 bytes).
    Removing usb.o(i.USBEndpointDMAEnable), (36 bytes).
    Removing usb.o(i.USBEndpointDataAvail), (30 bytes).
    Removing usb.o(i.USBEndpointDataGet), (84 bytes).
    Removing usb.o(i.USBEndpointDataPut), (58 bytes).
    Removing usb.o(i.USBEndpointDataSend), (48 bytes).
    Removing usb.o(i.USBEndpointDataToggleClear), (36 bytes).
    Removing usb.o(i.USBEndpointPacketCountSet), (12 bytes).
    Removing usb.o(i.USBEndpointStatus), (16 bytes).
    Removing usb.o(i.USBFIFOAddrGet), (8 bytes).
    Removing usb.o(i.USBFIFOConfigGet), (80 bytes).
    Removing usb.o(i.USBFIFOConfigSet), (64 bytes).
    Removing usb.o(i.USBFIFOFlush), (80 bytes).
    Removing usb.o(i.USBFrameNumberGet), (4 bytes).
    Removing usb.o(i.USBHighSpeed), (18 bytes).
    Removing usb.o(i.USBHostAddrGet), (20 bytes).
    Removing usb.o(i.USBHostAddrSet), (22 bytes).
    Removing usb.o(i.USBHostEndpointConfig), (242 bytes).
    Removing usb.o(i.USBHostEndpointDataAck), (32 bytes).
    Removing usb.o(i.USBHostEndpointDataToggle), (112 bytes).
    Removing usb.o(i.USBHostEndpointPing), (28 bytes).
    Removing usb.o(i.USBHostEndpointSpeed), (84 bytes).
    Removing usb.o(i.USBHostEndpointStatusClear), (40 bytes).
    Removing usb.o(i.USBHostHubAddrGet), (20 bytes).
    Removing usb.o(i.USBHostHubAddrSet), (70 bytes).
    Removing usb.o(i.USBHostLPMConfig), (24 bytes).
    Removing usb.o(i.USBHostLPMResume), (14 bytes).
    Removing usb.o(i.USBHostLPMSend), (36 bytes).
    Removing usb.o(i.USBHostMode), (8 bytes).
    Removing usb.o(i.USBHostPwrConfig), (26 bytes).
    Removing usb.o(i.USBHostPwrDisable), (14 bytes).
    Removing usb.o(i.USBHostPwrEnable), (14 bytes).
    Removing usb.o(i.USBHostPwrFaultDisable), (14 bytes).
    Removing usb.o(i.USBHostPwrFaultEnable), (14 bytes).
    Removing usb.o(i.USBHostRequestIN), (18 bytes).
    Removing usb.o(i.USBHostRequestINClear), (22 bytes).
    Removing usb.o(i.USBHostRequestStatus), (8 bytes).
    Removing usb.o(i.USBHostReset), (18 bytes).
    Removing usb.o(i.USBHostResume), (18 bytes).
    Removing usb.o(i.USBHostSpeedGet), (44 bytes).
    Removing usb.o(i.USBHostSuspend), (10 bytes).
    Removing usb.o(i.USBIntDisableControl), (44 bytes).
    Removing usb.o(i.USBIntDisableEndpoint), (24 bytes).
    Removing usb.o(i.USBIntEnableControl), (44 bytes).
    Removing usb.o(i.USBIntEnableEndpoint), (22 bytes).
    Removing usb.o(i.USBIntRegister), (68 bytes).
    Removing usb.o(i.USBIntStatusControl), (60 bytes).
    Removing usb.o(i.USBIntStatusEndpoint), (10 bytes).
    Removing usb.o(i.USBIntUnregister), (68 bytes).
    Removing usb.o(i.USBLPMEndpointGet), (10 bytes).
    Removing usb.o(i.USBLPMIntDisable), (14 bytes).
    Removing usb.o(i.USBLPMIntEnable), (12 bytes).
    Removing usb.o(i.USBLPMIntStatus), (6 bytes).
    Removing usb.o(i.USBLPMLinkStateGet), (10 bytes).
    Removing usb.o(i.USBLPMRemoteWakeEnabled), (14 bytes).
    Removing usb.o(i.USBModeConfig), (6 bytes).
    Removing usb.o(i.USBModeGet), (10 bytes).
    Removing usb.o(i.USBNumEndpointsGet), (10 bytes).
    Removing usb.o(i.USBOTGMode), (8 bytes).
    Removing usb.o(i.USBOTGSessionRequest), (22 bytes).
    Removing usb.o(i.USBPHYPowerOff), (10 bytes).
    Removing usb.o(i.USBPHYPowerOn), (10 bytes).
    Removing usb.o(i.USBULPIConfig), (6 bytes).
    Removing usb.o(i.USBULPIDisable), (14 bytes).
    Removing usb.o(i.USBULPIEnable), (14 bytes).
    Removing usb.o(i.USBULPIRegRead), (32 bytes).
    Removing usb.o(i.USBULPIRegWrite), (32 bytes).
    Removing usbbuffer.o(i.ScheduleNextTransmission), (166 bytes).
    Removing usbbuffer.o(i.USBBufferCallbackDataSet), (8 bytes).
    Removing usbbuffer.o(i.USBBufferDataAvailable), (6 bytes).
    Removing usbbuffer.o(i.USBBufferDataRemoved), (12 bytes).
    Removing usbbuffer.o(i.USBBufferDataWritten), (24 bytes).
    Removing usbbuffer.o(i.USBBufferEventCallback), (340 bytes).
    Removing usbbuffer.o(i.USBBufferFlush), (6 bytes).
    Removing usbbuffer.o(i.USBBufferInfoGet), (18 bytes).
    Removing usbbuffer.o(i.USBBufferInit), (24 bytes).
    Removing usbbuffer.o(i.USBBufferRead), (40 bytes).
    Removing usbbuffer.o(i.USBBufferSpaceAvailable), (6 bytes).
    Removing usbbuffer.o(i.USBBufferWrite), (52 bytes).
    Removing usbbuffer.o(i.USBBufferZeroLengthPacketInsert), (22 bytes).
    Removing usbdcdc.o(i.CDCTickHandler), (238 bytes).
    Removing usbdcdc.o(i.HandleConfigChange), (50 bytes).
    Removing usbdcdc.o(i.HandleDevice), (194 bytes).
    Removing usbdcdc.o(i.HandleDisconnect), (34 bytes).
    Removing usbdcdc.o(i.HandleEP0Data), (154 bytes).
    Removing usbdcdc.o(i.HandleEndpoints), (126 bytes).
    Removing usbdcdc.o(i.HandleRequests), (374 bytes).
    Removing usbdcdc.o(i.HandleResume), (12 bytes).
    Removing usbdcdc.o(i.HandleSuspend), (12 bytes).
    Removing usbdcdc.o(i.ProcessDataFromHost), (134 bytes).
    Removing usbdcdc.o(i.ProcessDataToHost), (100 bytes).
    Removing usbdcdc.o(i.ProcessNotificationToHost), (60 bytes).
    Removing usbdcdc.o(i.SendBreak), (68 bytes).
    Removing usbdcdc.o(i.SendSerialState), (150 bytes).
    Removing usbdcdc.o(i.USBDCDCCompositeInit), (220 bytes).
    Removing usbdcdc.o(i.USBDCDCInit), (76 bytes).
    Removing usbdcdc.o(i.USBDCDCPacketRead), (164 bytes).
    Removing usbdcdc.o(i.USBDCDCPacketWrite), (88 bytes).
    Removing usbdcdc.o(i.USBDCDCPowerStatusSet), (6 bytes).
    Removing usbdcdc.o(i.USBDCDCRemoteWakeupRequest), (6 bytes).
    Removing usbdcdc.o(i.USBDCDCRxPacketAvailable), (56 bytes).
    Removing usbdcdc.o(i.USBDCDCSerialStateChange), (50 bytes).
    Removing usbdcdc.o(i.USBDCDCSetControlCBData), (8 bytes).
    Removing usbdcdc.o(i.USBDCDCSetRxCBData), (8 bytes).
    Removing usbdcdc.o(i.USBDCDCSetTxCBData), (8 bytes).
    Removing usbdcdc.o(i.USBDCDCTerm), (18 bytes).
    Removing usbdcdc.o(i.USBDCDCTxPacketAvailable), (20 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (35 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (23 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (23 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (8 bytes).
    Removing usbdcdc.o(.constdata), (64 bytes).
    Removing usbdcdc.o(.data), (29 bytes).
    Removing usbdcdc.o(.data), (8 bytes).
    Removing usbdcdc.o(.data), (12 bytes).
    Removing usbdcdc.o(.data), (12 bytes).
    Removing usbdcdc.o(.data), (16 bytes).
    Removing usbdcdc.o(.data), (16 bytes).
    Removing usbdhandler.o(i.USB0DeviceIntHandler), (24 bytes).
    Removing usbmode.o(i.OTGDeviceDisconnect), (28 bytes).
    Removing usbmode.o(i.USB0DualModeIntHandler), (76 bytes).
    Removing usbmode.o(i.USB0OTGModeIntHandler), (204 bytes).
    Removing usbmode.o(i.USBDualModeInit), (116 bytes).
    Removing usbmode.o(i.USBDualModeTerm), (44 bytes).
    Removing usbmode.o(i.USBOTGFeatureSet), (44 bytes).
    Removing usbmode.o(i.USBOTGMain), (136 bytes).
    Removing usbmode.o(i.USBOTGModeInit), (184 bytes).
    Removing usbmode.o(i.USBOTGModeTerm), (52 bytes).
    Removing usbmode.o(i.USBOTGPollRate), (12 bytes).
    Removing usbmode.o(i.USBOTGSetMode), (136 bytes).
    Removing usbmode.o(i.USBStackModeSet), (28 bytes).
    Removing usbmode.o(.data), (16 bytes).
    Removing cpu.o(.emb_text), (38 bytes).
    Removing interrupt.o(i.IntDisable), (100 bytes).
    Removing interrupt.o(i.IntIsEnabled), (96 bytes).
    Removing interrupt.o(i.IntMasterDisable), (14 bytes).
    Removing interrupt.o(i.IntMasterEnable), (14 bytes).
    Removing interrupt.o(i.IntPendClear), (68 bytes).
    Removing interrupt.o(i.IntPendSet), (84 bytes).
    Removing interrupt.o(i.IntPriorityGet), (28 bytes).
    Removing interrupt.o(i.IntPriorityGroupingGet), (36 bytes).
    Removing interrupt.o(i.IntPriorityGroupingSet), (28 bytes).
    Removing interrupt.o(i.IntPriorityMaskGet), (4 bytes).
    Removing interrupt.o(i.IntPriorityMaskSet), (4 bytes).
    Removing interrupt.o(i.IntPrioritySet), (40 bytes).
    Removing interrupt.o(i.IntTrigger), (12 bytes).
    Removing interrupt.o(i.IntUnregister), (20 bytes).
    Removing interrupt.o(i._IntDefaultHandler), (2 bytes).
    Removing usbdenum.o(i.USBDCDDeviceInfoInit), (68 bytes).
    Removing usbdenum.o(i.USBDCDFeatureGet), (24 bytes).
    Removing usbdenum.o(i.USBDCDFeatureSet), (128 bytes).
    Removing usbdenum.o(i.USBDCDInit), (424 bytes).
    Removing usbdenum.o(i.USBDCDPowerStatusSet), (28 bytes).
    Removing usbdenum.o(i.USBDCDRemoteWakeLPM), (32 bytes).
    Removing usbdenum.o(i.USBDCDRemoteWakeupRequest), (56 bytes).
    Removing usbdenum.o(i.USBDCDRequestDataEP0), (20 bytes).
    Removing usbdenum.o(i.USBDCDSendDataEP0), (20 bytes).
    Removing usbdenum.o(i.USBDCDSetDefaultConfiguration), (12 bytes).
    Removing usbdenum.o(i.USBDCDStallEP0), (28 bytes).
    Removing usbdenum.o(i.USBDCDTerm), (92 bytes).
    Removing usbdenum.o(i.USBDClearFeature), (164 bytes).
    Removing usbdenum.o(i.USBDEP0StateTx), (116 bytes).
    Removing usbdenum.o(i.USBDEP0StateTxConfig), (372 bytes).
    Removing usbdenum.o(i.USBDGetConfiguration), (52 bytes).
    Removing usbdenum.o(i.USBDGetDescriptor), (368 bytes).
    Removing usbdenum.o(i.USBDGetInterface), (92 bytes).
    Removing usbdenum.o(i.USBDGetStatus), (132 bytes).
    Removing usbdenum.o(i.USBDReadAndDispatchRequest), (124 bytes).
    Removing usbdenum.o(i.USBDSetAddress), (36 bytes).
    Removing usbdenum.o(i.USBDSetConfiguration), (164 bytes).
    Removing usbdenum.o(i.USBDSetDescriptor), (40 bytes).
    Removing usbdenum.o(i.USBDSetFeature), (132 bytes).
    Removing usbdenum.o(i.USBDSetInterface), (188 bytes).
    Removing usbdenum.o(i.USBDSyncFrame), (40 bytes).
    Removing usbdenum.o(i.USBDeviceEnumHandler), (236 bytes).
    Removing usbdenum.o(i.USBDeviceEnumResetHandler), (64 bytes).
    Removing usbdenum.o(i.USBDeviceIntHandlerInternal), (408 bytes).
    Removing usbdenum.o(i.USBDeviceResumeTickHandler), (88 bytes).
    Removing usbdenum.o(.bss), (144 bytes).
    Removing usbdenum.o(.constdata), (52 bytes).
    Removing usbdenum.o(.data), (16 bytes).
    Removing usbhostenum.o(i.ConfigDescAlloc), (112 bytes).
    Removing usbhostenum.o(i.ConfigDescFree), (64 bytes).
    Removing usbhostenum.o(i.InternalUSBHCDSendEvent), (56 bytes).
    Removing usbhostenum.o(i.ProcessUSBDeviceStateMachine), (984 bytes).
    Removing usbhostenum.o(i.USB0HostIntHandler), (24 bytes).
    Removing usbhostenum.o(i.USBHCDControlTransfer), (628 bytes).
    Removing usbhostenum.o(i.USBHCDDevAddress), (40 bytes).
    Removing usbhostenum.o(i.USBHCDDevClass), (60 bytes).
    Removing usbhostenum.o(i.USBHCDDevHubPort), (40 bytes).
    Removing usbhostenum.o(i.USBHCDDevProtocol), (60 bytes).
    Removing usbhostenum.o(i.USBHCDDevSubClass), (60 bytes).
    Removing usbhostenum.o(i.USBHCDDeviceDisconnected), (160 bytes).
    Removing usbhostenum.o(i.USBHCDEventDisable), (88 bytes).
    Removing usbhostenum.o(i.USBHCDEventEnable), (88 bytes).
    Removing usbhostenum.o(i.USBHCDFeatureSet), (140 bytes).
    Removing usbhostenum.o(i.USBHCDHubDeviceConnected), (100 bytes).
    Removing usbhostenum.o(i.USBHCDHubDeviceDisconnected), (16 bytes).
    Removing usbhostenum.o(i.USBHCDInit), (256 bytes).
    Removing usbhostenum.o(i.USBHCDInitInternal), (404 bytes).
    Removing usbhostenum.o(i.USBHCDLPMResume), (12 bytes).
    Removing usbhostenum.o(i.USBHCDLPMSleep), (88 bytes).
    Removing usbhostenum.o(i.USBHCDLPMStatus), (26 bytes).
    Removing usbhostenum.o(i.USBHCDMain), (248 bytes).
    Removing usbhostenum.o(i.USBHCDPipeAlloc), (12 bytes).
    Removing usbhostenum.o(i.USBHCDPipeAllocSize), (756 bytes).
    Removing usbhostenum.o(i.USBHCDPipeConfig), (236 bytes).
    Removing usbhostenum.o(i.USBHCDPipeDataAck), (48 bytes).
    Removing usbhostenum.o(i.USBHCDPipeFree), (292 bytes).
    Removing usbhostenum.o(i.USBHCDPipeRead), (488 bytes).
    Removing usbhostenum.o(i.USBHCDPipeReadNonBlocking), (68 bytes).
    Removing usbhostenum.o(i.USBHCDPipeSchedule), (180 bytes).
    Removing usbhostenum.o(i.USBHCDPipeStatus), (4 bytes).
    Removing usbhostenum.o(i.USBHCDPipeTransferSizeGet), (28 bytes).
    Removing usbhostenum.o(i.USBHCDPipeWrite), (444 bytes).
    Removing usbhostenum.o(i.USBHCDPowerAutomatic), (20 bytes).
    Removing usbhostenum.o(i.USBHCDPowerConfigGet), (12 bytes).
    Removing usbhostenum.o(i.USBHCDPowerConfigInit), (12 bytes).
    Removing usbhostenum.o(i.USBHCDPowerConfigSet), (84 bytes).
    Removing usbhostenum.o(i.USBHCDRegisterDrivers), (16 bytes).
    Removing usbhostenum.o(i.USBHCDReset), (60 bytes).
    Removing usbhostenum.o(i.USBHCDResume), (44 bytes).
    Removing usbhostenum.o(i.USBHCDSetAddress), (84 bytes).
    Removing usbhostenum.o(i.USBHCDSetConfig), (48 bytes).
    Removing usbhostenum.o(i.USBHCDSetInterface), (50 bytes).
    Removing usbhostenum.o(i.USBHCDStringDescriptorGet), (58 bytes).
    Removing usbhostenum.o(i.USBHCDSuspend), (12 bytes).
    Removing usbhostenum.o(i.USBHCDTerm), (136 bytes).
    Removing usbhostenum.o(i.USBHostCheckPipes), (116 bytes).
    Removing usbhostenum.o(i.USBHostIntHandlerInternal), (1188 bytes).
    Removing usbhostenum.o(.bss), (1812 bytes).
    Removing usbhostenum.o(.data), (32 bytes).
    Removing usbringbuf.o(i.USBRingBufAdvanceRead), (70 bytes).
    Removing usbringbuf.o(i.USBRingBufAdvanceWrite), (84 bytes).
    Removing usbringbuf.o(i.USBRingBufContigFree), (30 bytes).
    Removing usbringbuf.o(i.USBRingBufContigUsed), (16 bytes).
    Removing usbringbuf.o(i.USBRingBufEmpty), (14 bytes).
    Removing usbringbuf.o(i.USBRingBufFlush), (26 bytes).
    Removing usbringbuf.o(i.USBRingBufFree), (22 bytes).
    Removing usbringbuf.o(i.USBRingBufFull), (26 bytes).
    Removing usbringbuf.o(i.USBRingBufInit), (12 bytes).
    Removing usbringbuf.o(i.USBRingBufRead), (82 bytes).
    Removing usbringbuf.o(i.USBRingBufReadOne), (54 bytes).
    Removing usbringbuf.o(i.USBRingBufSize), (4 bytes).
    Removing usbringbuf.o(i.USBRingBufUsed), (20 bytes).
    Removing usbringbuf.o(i.USBRingBufWrite), (80 bytes).
    Removing usbringbuf.o(i.USBRingBufWriteOne), (56 bytes).
    Removing usbtick.o(i.InternalUSBRegisterTickHandler), (52 bytes).
    Removing usbtick.o(i.InternalUSBStartOfFrameTick), (56 bytes).
    Removing usbtick.o(i.InternalUSBTickInit), (56 bytes).
    Removing usbtick.o(i.InternalUSBTickReset), (12 bytes).
    Removing usbtick.o(.bss), (48 bytes).
    Removing usbtick.o(.data), (8 bytes).
    Removing usbtick.o(.data), (4 bytes).
    Removing usbdcdesc.o(i.USBDCDConfigDescGet), (86 bytes).
    Removing usbdcdesc.o(i.USBDCDConfigDescGetNum), (54 bytes).
    Removing usbdcdesc.o(i.USBDCDConfigDescGetSize), (34 bytes).
    Removing usbdcdesc.o(i.USBDCDConfigGetInterface), (188 bytes).
    Removing usbdcdesc.o(i.USBDCDConfigGetInterfaceEndpoint), (94 bytes).
    Removing usbdcdesc.o(i.USBDCDConfigGetNumAlternateInterfaces), (88 bytes).
    Removing usbdconfig.o(i.USBDeviceConfig), (416 bytes).
    Removing usbdconfig.o(i.USBDeviceConfigAlternate), (200 bytes).
    Removing usbdesc.o(i.USBDescGet), (54 bytes).
    Removing usbdesc.o(i.USBDescGetInterface), (122 bytes).
    Removing usbdesc.o(i.USBDescGetInterfaceEndpoint), (66 bytes).
    Removing usbdesc.o(i.USBDescGetNum), (46 bytes).
    Removing usbdesc.o(i.USBDescGetNumAlternateInterfaces), (42 bytes).
    Removing usbdma.o(i.DMAUSBIntHandler), (64 bytes).
    Removing usbdma.o(i.DMAUSBIntStatusClear), (14 bytes).
    Removing usbdma.o(i.DMAUSBStatus), (4 bytes).
    Removing usbdma.o(i.USBLibDMAAddrGet), (8 bytes).
    Removing usbdma.o(i.USBLibDMAInit), (348 bytes).
    Removing usbdma.o(i.USBLibDMASizeGet), (8 bytes).
    Removing usbdma.o(i.iDMAUSBArbSizeSet), (2 bytes).
    Removing usbdma.o(i.iDMAUSBChannelAllocate), (132 bytes).
    Removing usbdma.o(i.iDMAUSBChannelDisable), (52 bytes).
    Removing usbdma.o(i.iDMAUSBChannelEnable), (82 bytes).
    Removing usbdma.o(i.iDMAUSBChannelIntDisable), (8 bytes).
    Removing usbdma.o(i.iDMAUSBChannelIntEnable), (8 bytes).
    Removing usbdma.o(i.iDMAUSBChannelRelease), (62 bytes).
    Removing usbdma.o(i.iDMAUSBChannelStatus), (56 bytes).
    Removing usbdma.o(i.iDMAUSBIntStatus), (6 bytes).
    Removing usbdma.o(i.iDMAUSBTransfer), (244 bytes).
    Removing usbdma.o(i.iDMAUSBUnitSizeSet), (2 bytes).
    Removing usbdma.o(i.uDMAUSBArbSizeSet), (104 bytes).
    Removing usbdma.o(i.uDMAUSBChannelAllocate), (192 bytes).
    Removing usbdma.o(i.uDMAUSBChannelDisable), (88 bytes).
    Removing usbdma.o(i.uDMAUSBChannelEnable), (106 bytes).
    Removing usbdma.o(i.uDMAUSBChannelIntDisable), (2 bytes).
    Removing usbdma.o(i.uDMAUSBChannelIntEnable), (2 bytes).
    Removing usbdma.o(i.uDMAUSBChannelRelease), (90 bytes).
    Removing usbdma.o(i.uDMAUSBChannelStatus), (38 bytes).
    Removing usbdma.o(i.uDMAUSBIntStatus), (52 bytes).
    Removing usbdma.o(i.uDMAUSBTransfer), (200 bytes).
    Removing usbdma.o(i.uDMAUSBUnitSizeSet), (84 bytes).
    Removing usbdma.o(.bss), (272 bytes).
    Removing usbhhub.o(i.HubDriverClose), (132 bytes).
    Removing usbhhub.o(i.HubDriverOpen), (400 bytes).
    Removing usbhhub.o(i.HubIntINCallback), (88 bytes).
    Removing usbhhub.o(i.USBHHubClose), (8 bytes).
    Removing usbhhub.o(i.USBHHubEnumerationComplete), (28 bytes).
    Removing usbhhub.o(i.USBHHubEnumerationError), (28 bytes).
    Removing usbhhub.o(i.USBHHubInit), (68 bytes).
    Removing usbhhub.o(i.USBHHubLPMSleep), (6 bytes).
    Removing usbhhub.o(i.USBHHubLPMStatus), (6 bytes).
    Removing usbhhub.o(i.USBHHubMain), (888 bytes).
    Removing usbhhub.o(i.USBHHubOpen), (20 bytes).
    Removing usbhhub.o(.bss), (156 bytes).
    Removing usbhhub.o(.constdata), (16 bytes).
    Removing usbhhub.o(.data), (8 bytes).
    Removing usbulpi.o(i.ULPIConfigSet), (90 bytes).
    Removing usbulpi.o(i.ULPIPowerTransceiver), (18 bytes).
    Removing udma.o(i.uDMAChannelAssign), (44 bytes).
    Removing udma.o(i.uDMAChannelAttributeDisable), (72 bytes).
    Removing udma.o(i.uDMAChannelAttributeEnable), (72 bytes).
    Removing udma.o(i.uDMAChannelAttributeGet), (64 bytes).
    Removing udma.o(i.uDMAChannelControlSet), (32 bytes).
    Removing udma.o(i.uDMAChannelDisable), (20 bytes).
    Removing udma.o(i.uDMAChannelEnable), (20 bytes).
    Removing udma.o(i.uDMAChannelIsEnabled), (28 bytes).
    Removing udma.o(i.uDMAChannelModeGet), (40 bytes).
    Removing udma.o(i.uDMAChannelRequest), (20 bytes).
    Removing udma.o(i.uDMAChannelScatterGatherSet), (84 bytes).
    Removing udma.o(i.uDMAChannelSelectDefault), (16 bytes).
    Removing udma.o(i.uDMAChannelSelectSecondary), (16 bytes).
    Removing udma.o(i.uDMAChannelSizeGet), (32 bytes).
    Removing udma.o(i.uDMAChannelTransferSet), (128 bytes).
    Removing udma.o(i.uDMAControlAlternateBaseGet), (12 bytes).
    Removing udma.o(i.uDMAControlBaseGet), (12 bytes).
    Removing udma.o(i.uDMAControlBaseSet), (12 bytes).
    Removing udma.o(i.uDMADisable), (12 bytes).
    Removing udma.o(i.uDMAEnable), (12 bytes).
    Removing udma.o(i.uDMAErrorStatusClear), (12 bytes).
    Removing udma.o(i.uDMAErrorStatusGet), (12 bytes).
    Removing udma.o(i.uDMAIntClear), (12 bytes).
    Removing udma.o(i.uDMAIntRegister), (18 bytes).
    Removing udma.o(i.uDMAIntStatus), (12 bytes).
    Removing udma.o(i.uDMAIntUnregister), (18 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

665 unused section(s) (total 42421 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    RESET                                    0x00000000   Section      620  startup_tm4c123.o(RESET)
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\DriversBsp\Ano_Math.c                 0x00000000   Number         0  ano_math.o ABSOLUTE
    ..\DriversBsp\Drv_AnoOf.c                0x00000000   Number         0  drv_anoof.o ABSOLUTE
    ..\DriversBsp\Drv_BSP.c                  0x00000000   Number         0  drv_bsp.o ABSOLUTE
    ..\DriversBsp\Drv_UbloxGPS.c             0x00000000   Number         0  drv_ubloxgps.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_Adc.c  0x00000000   Number         0  drv_adc.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_Led.c  0x00000000   Number         0  drv_led.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_PwmOut.c 0x00000000   Number         0  drv_pwmout.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_RcIn.c 0x00000000   Number         0  drv_rcin.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_Sys.c  0x00000000   Number         0  drv_sys.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_Timer.c 0x00000000   Number         0  drv_timer.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_Uart.c 0x00000000   Number         0  drv_uart.o ABSOLUTE
    ..\DriversMcu\TM4C123\Drivers\Drv_Usb.c  0x00000000   Number         0  drv_usb.o ABSOLUTE
    ..\DriversMcu\TM4C123\Libraries\src\startup_TM4C123.s 0x00000000   Number         0  startup_tm4c123.o ABSOLUTE
    ..\DriversMcu\TM4C123\Libraries\src\system_TM4C123.c 0x00000000   Number         0  system_tm4c123.o ABSOLUTE
    ..\DriversMcu\TM4C123\Libraries\src\usb_serial_structs.c 0x00000000   Number         0  usb_serial_structs.o ABSOLUTE
    ..\FcSrc\ANO_DT_LX.c                     0x00000000   Number         0  ano_dt_lx.o ABSOLUTE
    ..\FcSrc\ANO_LX.c                        0x00000000   Number         0  ano_lx.o ABSOLUTE
    ..\FcSrc\Ano_Scheduler.c                 0x00000000   Number         0  ano_scheduler.o ABSOLUTE
    ..\FcSrc\LX_FC_EXT_Sensor.c              0x00000000   Number         0  lx_fc_ext_sensor.o ABSOLUTE
    ..\FcSrc\LX_FC_Fun.c                     0x00000000   Number         0  lx_fc_fun.o ABSOLUTE
    ..\FcSrc\LX_FC_State.c                   0x00000000   Number         0  lx_fc_state.o ABSOLUTE
    ..\FcSrc\User_Task.c                     0x00000000   Number         0  user_task.o ABSOLUTE
    ..\FcSrc\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\DriversBsp\\Ano_Math.c               0x00000000   Number         0  ano_math.o ABSOLUTE
    ..\\DriversBsp\\Drv_AnoOf.c              0x00000000   Number         0  drv_anoof.o ABSOLUTE
    ..\\DriversBsp\\Drv_BSP.c                0x00000000   Number         0  drv_bsp.o ABSOLUTE
    ..\\DriversBsp\\Drv_UbloxGPS.c           0x00000000   Number         0  drv_ubloxgps.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_Adc.c 0x00000000   Number         0  drv_adc.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_Led.c 0x00000000   Number         0  drv_led.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_PwmOut.c 0x00000000   Number         0  drv_pwmout.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_RcIn.c 0x00000000   Number         0  drv_rcin.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_Sys.c 0x00000000   Number         0  drv_sys.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_Timer.c 0x00000000   Number         0  drv_timer.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_Uart.c 0x00000000   Number         0  drv_uart.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Drivers\\Drv_Usb.c 0x00000000   Number         0  drv_usb.o ABSOLUTE
    ..\\DriversMcu\\TM4C123\\Libraries\\src\\system_TM4C123.c 0x00000000   Number         0  system_tm4c123.o ABSOLUTE
    ..\\FcSrc\\ANO_DT_LX.c                   0x00000000   Number         0  ano_dt_lx.o ABSOLUTE
    ..\\FcSrc\\ANO_LX.c                      0x00000000   Number         0  ano_lx.o ABSOLUTE
    ..\\FcSrc\\Ano_Scheduler.c               0x00000000   Number         0  ano_scheduler.o ABSOLUTE
    ..\\FcSrc\\LX_FC_EXT_Sensor.c            0x00000000   Number         0  lx_fc_ext_sensor.o ABSOLUTE
    ..\\FcSrc\\LX_FC_Fun.c                   0x00000000   Number         0  lx_fc_fun.o ABSOLUTE
    ..\\FcSrc\\LX_FC_State.c                 0x00000000   Number         0  lx_fc_state.o ABSOLUTE
    ..\\FcSrc\\User_Task.c                   0x00000000   Number         0  user_task.o ABSOLUTE
    ..\\FcSrc\\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cpu.c                                    0x00000000   Number         0  cpu.o ABSOLUTE
    cpu.c                                    0x00000000   Number         0  cpu.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    device\usbdcdc.c                         0x00000000   Number         0  usbdcdc.o ABSOLUTE
    device\usbdcdesc.c                       0x00000000   Number         0  usbdcdesc.o ABSOLUTE
    device\usbdconfig.c                      0x00000000   Number         0  usbdconfig.o ABSOLUTE
    device\usbdenum.c                        0x00000000   Number         0  usbdenum.o ABSOLUTE
    device\usbdhandler.c                     0x00000000   Number         0  usbdhandler.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    host\usbhhub.c                           0x00000000   Number         0  usbhhub.o ABSOLUTE
    host\usbhostenum.c                       0x00000000   Number         0  usbhostenum.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    interrupt.c                              0x00000000   Number         0  interrupt.o ABSOLUTE
    sysctl.c                                 0x00000000   Number         0  sysctl.o ABSOLUTE
    sysctl.c                                 0x00000000   Number         0  sysctl.o ABSOLUTE
    timer.c                                  0x00000000   Number         0  timer.o ABSOLUTE
    uart.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    udma.c                                   0x00000000   Number         0  udma.o ABSOLUTE
    usb.c                                    0x00000000   Number         0  usb.o ABSOLUTE
    usbbuffer.c                              0x00000000   Number         0  usbbuffer.o ABSOLUTE
    usbdesc.c                                0x00000000   Number         0  usbdesc.o ABSOLUTE
    usbdma.c                                 0x00000000   Number         0  usbdma.o ABSOLUTE
    usbmode.c                                0x00000000   Number         0  usbmode.o ABSOLUTE
    usbringbuf.c                             0x00000000   Number         0  usbringbuf.o ABSOLUTE
    usbtick.c                                0x00000000   Number         0  usbtick.o ABSOLUTE
    usbulpi.c                                0x00000000   Number         0  usbulpi.o ABSOLUTE
    .ARM.Collect$$$$00000000                 0x0000026c   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x0000026c   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00000270   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00000274   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00000274   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00000274   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x0000027c   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x00000280   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x00000280   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x00000280   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x00000280   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x00000284   Section      252  startup_tm4c123.o(.text)
    $v0                                      0x00000284   Number         0  startup_tm4c123.o(.text)
    .text                                    0x00000380   Section        0  dmul.o(.text)
    .text                                    0x00000464   Section        0  ddiv.o(.text)
    .text                                    0x00000542   Section        0  dfltui.o(.text)
    .text                                    0x0000055c   Section        0  d2f.o(.text)
    .text                                    0x00000594   Section        0  llshl.o(.text)
    .text                                    0x000005b2   Section        0  iusefp.o(.text)
    .text                                    0x000005b2   Section        0  fepilogue.o(.text)
    .text                                    0x00000620   Section        0  depilogue.o(.text)
    .text                                    0x000006dc   Section       36  init.o(.text)
    .text                                    0x00000700   Section        0  llushr.o(.text)
    .text                                    0x00000720   Section        0  __dczerorl2.o(.text)
    i.ADC0Handler                            0x00000778   Section        0  drv_adc.o(i.ADC0Handler)
    ADC0Handler                              0x00000779   Thumb Code    34  drv_adc.o(i.ADC0Handler)
    i.ADCIntRegister                         0x000007a4   Section        0  adc.o(i.ADCIntRegister)
    i.ANO_DT_Init                            0x0000080c   Section        0  ano_dt_lx.o(i.ANO_DT_Init)
    i.ANO_DT_LX_Data_Receive_Anl             0x000008b8   Section        0  ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl)
    ANO_DT_LX_Data_Receive_Anl               0x000008b9   Thumb Code   486  ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Anl)
    i.ANO_DT_LX_Data_Receive_Prepare         0x00000abc   Section        0  ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Prepare)
    i.ANO_DT_LX_Send_Data                    0x00000bd4   Section        0  ano_dt_lx.o(i.ANO_DT_LX_Send_Data)
    ANO_DT_LX_Send_Data                      0x00000bd5   Thumb Code    16  ano_dt_lx.o(i.ANO_DT_LX_Send_Data)
    i.ANO_LX_Data_Exchange_Task              0x00000be4   Section        0  ano_dt_lx.o(i.ANO_LX_Data_Exchange_Task)
    i.ANO_LX_Task                            0x00000c28   Section        0  ano_lx.o(i.ANO_LX_Task)
    i.Add_Send_Data                          0x00000cc4   Section        0  ano_dt_lx.o(i.Add_Send_Data)
    Add_Send_Data                            0x00000cc5   Thumb Code   390  ano_dt_lx.o(i.Add_Send_Data)
    i.All_Init                               0x00000e64   Section        0  drv_bsp.o(i.All_Init)
    i.AnoOF_Check_State                      0x00000ec8   Section        0  drv_anoof.o(i.AnoOF_Check_State)
    i.AnoOF_DataAnl                          0x00000f88   Section        0  drv_anoof.o(i.AnoOF_DataAnl)
    AnoOF_DataAnl                            0x00000f89   Thumb Code   364  drv_anoof.o(i.AnoOF_DataAnl)
    i.AnoOF_GetOneByte                       0x00001104   Section        0  drv_anoof.o(i.AnoOF_GetOneByte)
    i.Bat_Voltage_Data_Handle                0x00001210   Section        0  ano_lx.o(i.Bat_Voltage_Data_Handle)
    Bat_Voltage_Data_Handle                  0x00001211   Thumb Code    28  ano_lx.o(i.Bat_Voltage_Data_Handle)
    i.CK_Back                                0x00001234   Section        0  ano_dt_lx.o(i.CK_Back)
    i.CK_Back_Check                          0x00001244   Section        0  ano_dt_lx.o(i.CK_Back_Check)
    CK_Back_Check                            0x00001245   Thumb Code    90  ano_dt_lx.o(i.CK_Back_Check)
    i.CMD_Send                               0x000012ac   Section        0  ano_dt_lx.o(i.CMD_Send)
    i.Check_To_Send                          0x000012c8   Section        0  ano_dt_lx.o(i.Check_To_Send)
    Check_To_Send                            0x000012c9   Thumb Code   138  ano_dt_lx.o(i.Check_To_Send)
    i.DrvAdcInit                             0x00001358   Section        0  drv_adc.o(i.DrvAdcInit)
    i.DrvLedOnOff                            0x00001410   Section        0  drv_led.o(i.DrvLedOnOff)
    i.DrvMotorPWMSet                         0x000014c4   Section        0  drv_pwmout.o(i.DrvMotorPWMSet)
    i.DrvPpmGetOneCh                         0x000015b0   Section        0  drv_bsp.o(i.DrvPpmGetOneCh)
    i.DrvPwmOutInit                          0x00001614   Section        0  drv_pwmout.o(i.DrvPwmOutInit)
    i.DrvRcInputInit                         0x000018ec   Section        0  drv_bsp.o(i.DrvRcInputInit)
    i.DrvRcInputTask                         0x00001904   Section        0  drv_bsp.o(i.DrvRcInputTask)
    i.DrvRcPpmInit                           0x000019fc   Section        0  drv_rcin.o(i.DrvRcPpmInit)
    i.DrvRcSbusInit                          0x00001ad0   Section        0  drv_rcin.o(i.DrvRcSbusInit)
    i.DrvSbusGetOneByte                      0x00001b88   Section        0  drv_bsp.o(i.DrvSbusGetOneByte)
    i.DrvSysInit                             0x00001dd0   Section        0  drv_sys.o(i.DrvSysInit)
    i.DrvTimerFcInit                         0x00001e20   Section        0  drv_timer.o(i.DrvTimerFcInit)
    i.DrvUart1Init                           0x00001e9c   Section        0  drv_uart.o(i.DrvUart1Init)
    i.DrvUart1SendBuf                        0x00001f6c   Section        0  drv_uart.o(i.DrvUart1SendBuf)
    i.DrvUart1TxCheck                        0x00001fa0   Section        0  drv_uart.o(i.DrvUart1TxCheck)
    i.DrvUart2Init                           0x00001fe4   Section        0  drv_uart.o(i.DrvUart2Init)
    i.DrvUart2TxCheck                        0x000020bc   Section        0  drv_uart.o(i.DrvUart2TxCheck)
    i.DrvUart3Init                           0x00002100   Section        0  drv_uart.o(i.DrvUart3Init)
    i.DrvUart3TxCheck                        0x000021d8   Section        0  drv_uart.o(i.DrvUart3TxCheck)
    i.DrvUart4Init                           0x0000221c   Section        0  drv_uart.o(i.DrvUart4Init)
    i.DrvUart4TxCheck                        0x000022f4   Section        0  drv_uart.o(i.DrvUart4TxCheck)
    i.DrvUart5Init                           0x00002338   Section        0  drv_uart.o(i.DrvUart5Init)
    i.DrvUart5SendBuf                        0x0000242c   Section        0  drv_uart.o(i.DrvUart5SendBuf)
    i.DrvUart5TxCheck                        0x00002460   Section        0  drv_uart.o(i.DrvUart5TxCheck)
    i.DrvUartDataCheck                       0x000024a4   Section        0  drv_uart.o(i.DrvUartDataCheck)
    i.Drv_AdcGetBatVot                       0x000024bc   Section        0  drv_adc.o(i.Drv_AdcGetBatVot)
    i.DvrLedInit                             0x0000252c   Section        0  drv_led.o(i.DvrLedInit)
    i.ESC_Output                             0x000025b0   Section        0  ano_lx.o(i.ESC_Output)
    ESC_Output                               0x000025b1   Thumb Code   364  ano_lx.o(i.ESC_Output)
    i.FC_Lock                                0x0000272c   Section        0  lx_fc_fun.o(i.FC_Lock)
    i.FC_Unlock                              0x0000276c   Section        0  lx_fc_fun.o(i.FC_Unlock)
    i.Frame_Send                             0x000027ac   Section        0  ano_dt_lx.o(i.Frame_Send)
    Frame_Send                               0x000027ad   Thumb Code   190  ano_dt_lx.o(i.Frame_Send)
    i.GPS_Data_Prepare_Task                  0x00002874   Section        0  drv_ubloxgps.o(i.GPS_Data_Prepare_Task)
    i.GPS_Rate_H                             0x000029c0   Section        0  drv_ubloxgps.o(i.GPS_Rate_H)
    i.General_Velocity_Data_Handle           0x000029d0   Section        0  lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle)
    General_Velocity_Data_Handle             0x000029d1   Thumb Code   120  lx_fc_ext_sensor.o(i.General_Velocity_Data_Handle)
    i.GetSysRunTimeMs                        0x00002a60   Section        0  drv_sys.o(i.GetSysRunTimeMs)
    i.GetSysRunTimeUs                        0x00002a6c   Section        0  drv_sys.o(i.GetSysRunTimeUs)
    i.Horizontal_Calibrate                   0x00002aa0   Section        0  lx_fc_fun.o(i.Horizontal_Calibrate)
    i.Init_GPS                               0x00002ad8   Section        0  drv_ubloxgps.o(i.Init_GPS)
    i.IntEnable                              0x00002bb8   Section        0  interrupt.o(i.IntEnable)
    i.IntRegister                            0x00002c1c   Section        0  interrupt.o(i.IntRegister)
    i.LED_1ms_DRV                            0x00002c50   Section        0  drv_led.o(i.LED_1ms_DRV)
    i.LX_Cali_Trig_Check                     0x00002ca8   Section        0  lx_fc_state.o(i.LX_Cali_Trig_Check)
    i.LX_Change_Mode                         0x00002d4c   Section        0  lx_fc_fun.o(i.LX_Change_Mode)
    i.LX_FC_EXT_Sensor_Task                  0x00002da8   Section        0  lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task)
    i.LX_FC_State_Task                       0x00002e08   Section        0  lx_fc_state.o(i.LX_FC_State_Task)
    i.LX_Unlock_Lock_Check                   0x00002e28   Section        0  lx_fc_state.o(i.LX_Unlock_Lock_Check)
    LX_Unlock_Lock_Check                     0x00002e29   Thumb Code   438  lx_fc_state.o(i.LX_Unlock_Lock_Check)
    i.Loop_1000Hz                            0x00002ff8   Section        0  ano_scheduler.o(i.Loop_1000Hz)
    Loop_1000Hz                              0x00002ff9   Thumb Code     2  ano_scheduler.o(i.Loop_1000Hz)
    i.Loop_100Hz                             0x00002ffa   Section        0  ano_scheduler.o(i.Loop_100Hz)
    Loop_100Hz                               0x00002ffb   Thumb Code     2  ano_scheduler.o(i.Loop_100Hz)
    i.Loop_200Hz                             0x00002ffc   Section        0  ano_scheduler.o(i.Loop_200Hz)
    Loop_200Hz                               0x00002ffd   Thumb Code     2  ano_scheduler.o(i.Loop_200Hz)
    i.Loop_20Hz                              0x00002ffe   Section        0  ano_scheduler.o(i.Loop_20Hz)
    Loop_20Hz                                0x00002fff   Thumb Code     2  ano_scheduler.o(i.Loop_20Hz)
    i.Loop_2Hz                               0x00003000   Section        0  ano_scheduler.o(i.Loop_2Hz)
    Loop_2Hz                                 0x00003001   Thumb Code     2  ano_scheduler.o(i.Loop_2Hz)
    i.Loop_500Hz                             0x00003002   Section        0  ano_scheduler.o(i.Loop_500Hz)
    Loop_500Hz                               0x00003003   Thumb Code     2  ano_scheduler.o(i.Loop_500Hz)
    i.Loop_50Hz                              0x00003004   Section        0  ano_scheduler.o(i.Loop_50Hz)
    Loop_50Hz                                0x00003005   Thumb Code     8  ano_scheduler.o(i.Loop_50Hz)
    i.Mag_Calibrate                          0x0000300c   Section        0  lx_fc_fun.o(i.Mag_Calibrate)
    i.MyDelayMs                              0x00003044   Section        0  drv_sys.o(i.MyDelayMs)
    i.NoUse                                  0x00003068   Section        0  drv_uart.o(i.NoUse)
    i.OneKey_Land                            0x0000306c   Section        0  lx_fc_fun.o(i.OneKey_Land)
    i.OneKey_Return_Home                     0x000030a4   Section        0  lx_fc_fun.o(i.OneKey_Return_Home)
    i.OneKey_Takeoff                         0x000030dc   Section        0  lx_fc_fun.o(i.OneKey_Takeoff)
    i.PAR_Back                               0x00003124   Section        0  ano_dt_lx.o(i.PAR_Back)
    i.PPM_Decode                             0x00003138   Section        0  drv_rcin.o(i.PPM_Decode)
    PPM_Decode                               0x00003139   Thumb Code   114  drv_rcin.o(i.PPM_Decode)
    i.RC_Data_Task                           0x000031bc   Section        0  ano_lx.o(i.RC_Data_Task)
    RC_Data_Task                             0x000031bd   Thumb Code   594  ano_lx.o(i.RC_Data_Task)
    i.Sbus_IRQHandler                        0x00003454   Section        0  drv_rcin.o(i.Sbus_IRQHandler)
    Sbus_IRQHandler                          0x00003455   Thumb Code    76  drv_rcin.o(i.Sbus_IRQHandler)
    i.Scheduler_Run                          0x000034a4   Section        0  ano_scheduler.o(i.Scheduler_Run)
    i.Scheduler_Setup                        0x000034f4   Section        0  ano_scheduler.o(i.Scheduler_Setup)
    i.SysCtlClockGet                         0x00003544   Section        0  sysctl.o(i.SysCtlClockGet)
    i.SysTick_Handler                        0x00003698   Section        0  drv_sys.o(i.SysTick_Handler)
    i.SysTick_Init                           0x000036b0   Section        0  drv_sys.o(i.SysTick_Init)
    i.SystemInit                             0x000036e8   Section        0  system_tm4c123.o(i.SystemInit)
    i.Timer0Irq                              0x00003780   Section        0  drv_timer.o(i.Timer0Irq)
    i.TimerIntClear                          0x00003794   Section        0  timer.o(i.TimerIntClear)
    i.TimerIntRegister                       0x00003798   Section        0  timer.o(i.TimerIntRegister)
    i.UART1_IRQHandler                       0x00003800   Section        0  drv_uart.o(i.UART1_IRQHandler)
    i.UART2_IRQHandler                       0x00003858   Section        0  drv_uart.o(i.UART2_IRQHandler)
    i.UART3_IRQHandler                       0x000038b0   Section        0  drv_uart.o(i.UART3_IRQHandler)
    i.UART4_IRQHandler                       0x00003908   Section        0  drv_uart.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x00003960   Section        0  drv_uart.o(i.UART5_IRQHandler)
    i.UARTIntRegister                        0x000039b8   Section        0  uart.o(i.UARTIntRegister)
    i.UBLOX_M8_GPS_Data_Receive              0x00003a10   Section        0  drv_ubloxgps.o(i.UBLOX_M8_GPS_Data_Receive)
    i.UserTask_OneKeyCmd                     0x00003b74   Section        0  user_task.o(i.UserTask_OneKeyCmd)
    i.__scatterload_copy                     0x00003c3c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00003c4a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00003c4c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.drvAdcTrigger                          0x00003c5c   Section        0  drv_adc.o(i.drvAdcTrigger)
    i.main                                   0x00003c74   Section        0  main.o(i.main)
    i.my_deadzone                            0x00003c84   Section        0  ano_math.o(i.my_deadzone)
    i.rcSignalCheck                          0x00003cbc   Section        0  drv_bsp.o(i.rcSignalCheck)
    rcSignalCheck                            0x00003cbd   Thumb Code   168  drv_bsp.o(i.rcSignalCheck)
    .constdata                               0x00003d74   Section      596  sysctl.o(.constdata)
    g_pui32VCOFrequencies                    0x00003d74   Data           8  sysctl.o(.constdata)
    g_pui32Xtals                             0x00003d7c   Data         108  sysctl.o(.constdata)
    g_pppui32XTALtoVCO                       0x00003de8   Data         432  sysctl.o(.constdata)
    g_sXTALtoMEMTIM                          0x00003f98   Data          48  sysctl.o(.constdata)
    .constdata                               0x00003fc8   Section      160  timer.o(.constdata)
    g_ppui32TimerIntMap                      0x00003fc8   Data          96  timer.o(.constdata)
    g_ppui32TimerIntMapSnowflake             0x00004028   Data          64  timer.o(.constdata)
    .constdata                               0x00004068   Section      128  uart.o(.constdata)
    g_ppui32UARTIntMap                       0x00004068   Data          64  uart.o(.constdata)
    g_ppui32UARTIntMapSnowflake              0x000040a8   Data          64  uart.o(.constdata)
    .constdata                               0x000040e8   Section      268  interrupt.o(.constdata)
    g_pui32Priority                          0x000040e8   Data          32  interrupt.o(.constdata)
    g_pui32Regs                              0x00004108   Data         156  interrupt.o(.constdata)
    g_pui32EnRegs                            0x000041a4   Data          20  interrupt.o(.constdata)
    g_pui32Dii16Regs                         0x000041b8   Data          20  interrupt.o(.constdata)
    g_pui32PendRegs                          0x000041cc   Data          20  interrupt.o(.constdata)
    g_pui32UnpendRegs                        0x000041e0   Data          20  interrupt.o(.constdata)
    .data                                    0x20000000   Section       84  ano_scheduler.o(.data)
    sched_tasks                              0x20000000   Data          84  ano_scheduler.o(.data)
    .data                                    0x20000054   Section        4  user_task.o(.data)
    one_key_takeoff_f                        0x20000054   Data           1  user_task.o(.data)
    one_key_land_f                           0x20000055   Data           1  user_task.o(.data)
    one_key_mission_f                        0x20000056   Data           1  user_task.o(.data)
    mission_step                             0x20000057   Data           1  user_task.o(.data)
    .data                                    0x20000058   Section        6  ano_dt_lx.o(.data)
    DT_data_cnt                              0x20000058   Data           1  ano_dt_lx.o(.data)
    repeat_cnt                               0x20000059   Data           1  ano_dt_lx.o(.data)
    _data_len                                0x2000005a   Data           1  ano_dt_lx.o(.data)
    _data_cnt                                0x2000005b   Data           1  ano_dt_lx.o(.data)
    rxstate                                  0x2000005c   Data           1  ano_dt_lx.o(.data)
    time_dly                                 0x2000005d   Data           1  ano_dt_lx.o(.data)
    .data                                    0x2000005e   Section       30  ano_lx.o(.data)
    fail_safe_change_mod                     0x2000006f   Data           1  ano_lx.o(.data)
    fail_safe_return_home                    0x20000070   Data           1  ano_lx.o(.data)
    mod_f                                    0x20000071   Data           3  ano_lx.o(.data)
    mod_f_time_cnt                           0x20000074   Data           2  ano_lx.o(.data)
    esc_calibrated                           0x20000076   Data           1  ano_lx.o(.data)
    tmp_cnt                                  0x20000078   Data           4  ano_lx.o(.data)
    .data                                    0x2000007c   Section        4  lx_fc_ext_sensor.o(.data)
    of_update_cnt                            0x2000007c   Data           1  lx_fc_ext_sensor.o(.data)
    of_alt_update_cnt                        0x2000007d   Data           1  lx_fc_ext_sensor.o(.data)
    dT_ms                                    0x2000007e   Data           1  lx_fc_ext_sensor.o(.data)
    of_alt_update_cnt                        0x2000007f   Data           1  lx_fc_ext_sensor.o(.data)
    .data                                    0x20000080   Section        1  lx_fc_fun.o(.data)
    old_mode                                 0x20000080   Data           1  lx_fc_fun.o(.data)
    .data                                    0x20000082   Section        6  lx_fc_state.o(.data)
    time_dly_cnt_ms                          0x20000084   Data           2  lx_fc_state.o(.data)
    unlock_lock_flag                         0x20000086   Data           1  lx_fc_state.o(.data)
    cali_f                                   0x20000087   Data           1  lx_fc_state.o(.data)
    .data                                    0x20000088   Section       19  drv_bsp.o(.data)
    ch_sta                                   0x20000088   Data           1  drv_bsp.o(.data)
    sbus_time                                0x2000008c   Data           8  drv_bsp.o(.data)
    cnt                                      0x20000094   Data           1  drv_bsp.o(.data)
    frame_cnt                                0x20000095   Data           1  drv_bsp.o(.data)
    cnt_tmp                                  0x20000096   Data           1  drv_bsp.o(.data)
    time_dly                                 0x20000098   Data           2  drv_bsp.o(.data)
    failsafe                                 0x2000009a   Data           1  drv_bsp.o(.data)
    .data                                    0x2000009b   Section        3  drv_anoof.o(.data)
    _data_len                                0x2000009b   Data           1  drv_anoof.o(.data)
    _data_cnt                                0x2000009c   Data           1  drv_anoof.o(.data)
    rxstate                                  0x2000009d   Data           1  drv_anoof.o(.data)
    .data                                    0x2000009e   Section      182  drv_ubloxgps.o(.data)
    protocol_class                           0x2000014d   Data           1  drv_ubloxgps.o(.data)
    protocol_id                              0x2000014e   Data           1  drv_ubloxgps.o(.data)
    protocol_length_t                        0x2000014f   Data           1  drv_ubloxgps.o(.data)
    protocol_length                          0x20000150   Data           2  drv_ubloxgps.o(.data)
    state                                    0x20000153   Data           1  drv_ubloxgps.o(.data)
    .data                                    0x20000158   Section        8  drv_sys.o(.data)
    SysRunTimeMs                             0x20000158   Data           8  drv_sys.o(.data)
    .data                                    0x20000160   Section        4  drv_adc.o(.data)
    .data                                    0x20000164   Section       12  drv_led.o(.data)
    led_cnt                                  0x20000168   Data           8  drv_led.o(.data)
    .data                                    0x20000170   Section       12  drv_rcin.o(.data)
    PeriodVal1                               0x20000170   Data           4  drv_rcin.o(.data)
    PeriodVal2                               0x20000174   Data           4  drv_rcin.o(.data)
    PulseHigh                                0x20000178   Data           4  drv_rcin.o(.data)
    .data                                    0x2000017c   Section       10  drv_uart.o(.data)
    vtable                                   0x20000400   Section      620  interrupt.o(vtable)
    g_pfnRAMVectors                          0x20000400   Data         620  interrupt.o(vtable)
    .bss                                     0x2000066c   Section     1872  ano_dt_lx.o(.bss)
    DT_RxBuffer                              0x20000cbc   Data         256  ano_dt_lx.o(.bss)
    .bss                                     0x20000dbc   Section       56  ano_lx.o(.bss)
    pwm                                      0x20000de4   Data          16  ano_lx.o(.bss)
    .bss                                     0x20000df4   Section       48  lx_fc_ext_sensor.o(.bss)
    .bss                                     0x20000e24   Section       11  lx_fc_state.o(.bss)
    .bss                                     0x20000e30   Section      107  drv_bsp.o(.bss)
    datatmp                                  0x20000e82   Data          25  drv_bsp.o(.bss)
    .bss                                     0x20000e9c   Section      124  drv_anoof.o(.bss)
    _datatemp                                0x20000ed8   Data          50  drv_anoof.o(.bss)
    check_time_ms                            0x20000f0c   Data          12  drv_anoof.o(.bss)
    .bss                                     0x20000f18   Section      100  drv_ubloxgps.o(.bss)
    .bss                                     0x20000f7c   Section     1280  drv_uart.o(.bss)
    STACK                                    0x20001480   Section     2560  startup_tm4c123.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_tm4c123.o(RESET)
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x0000026c   Data           0  startup_tm4c123.o(RESET)
    __Vectors_Size                           0x0000026c   Number         0  startup_tm4c123.o ABSOLUTE
    __main                                   0x0000026d   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x0000026d   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00000271   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00000275   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00000275   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00000275   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00000275   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x0000027d   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x00000281   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x00000281   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x00000285   Thumb Code     8  startup_tm4c123.o(.text)
    NMI_Handler                              0x0000028d   Thumb Code     2  startup_tm4c123.o(.text)
    HardFault_Handler                        0x0000028f   Thumb Code     2  startup_tm4c123.o(.text)
    MemManage_Handler                        0x00000291   Thumb Code     2  startup_tm4c123.o(.text)
    BusFault_Handler                         0x00000293   Thumb Code     2  startup_tm4c123.o(.text)
    UsageFault_Handler                       0x00000295   Thumb Code     2  startup_tm4c123.o(.text)
    SVC_Handler                              0x00000297   Thumb Code     2  startup_tm4c123.o(.text)
    DebugMon_Handler                         0x00000299   Thumb Code     2  startup_tm4c123.o(.text)
    PendSV_Handler                           0x0000029b   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOA_Handler                            0x0000029f   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOB_Handler                            0x000002a1   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOC_Handler                            0x000002a3   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOD_Handler                            0x000002a5   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOE_Handler                            0x000002a7   Thumb Code     2  startup_tm4c123.o(.text)
    UART0_Handler                            0x000002a9   Thumb Code     2  startup_tm4c123.o(.text)
    UART1_Handler                            0x000002ab   Thumb Code     2  startup_tm4c123.o(.text)
    SSI0_Handler                             0x000002ad   Thumb Code     2  startup_tm4c123.o(.text)
    I2C0_Handler                             0x000002af   Thumb Code     2  startup_tm4c123.o(.text)
    PMW0_FAULT_Handler                       0x000002b1   Thumb Code     2  startup_tm4c123.o(.text)
    PWM0_0_Handler                           0x000002b3   Thumb Code     2  startup_tm4c123.o(.text)
    PWM0_1_Handler                           0x000002b5   Thumb Code     2  startup_tm4c123.o(.text)
    PWM0_2_Handler                           0x000002b7   Thumb Code     2  startup_tm4c123.o(.text)
    QEI0_Handler                             0x000002b9   Thumb Code     2  startup_tm4c123.o(.text)
    ADC0SS0_Handler                          0x000002bb   Thumb Code     2  startup_tm4c123.o(.text)
    ADC0SS1_Handler                          0x000002bd   Thumb Code     2  startup_tm4c123.o(.text)
    ADC0SS2_Handler                          0x000002bf   Thumb Code     2  startup_tm4c123.o(.text)
    ADC0SS3_Handler                          0x000002c1   Thumb Code     2  startup_tm4c123.o(.text)
    WDT0_Handler                             0x000002c3   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER0A_Handler                          0x000002c5   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER0B_Handler                          0x000002c7   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER1A_Handler                          0x000002c9   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER1B_Handler                          0x000002cb   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER2A_Handler                          0x000002cd   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER2B_Handler                          0x000002cf   Thumb Code     2  startup_tm4c123.o(.text)
    COMP0_Handler                            0x000002d1   Thumb Code     2  startup_tm4c123.o(.text)
    COMP1_Handler                            0x000002d3   Thumb Code     2  startup_tm4c123.o(.text)
    COMP2_Handler                            0x000002d5   Thumb Code     2  startup_tm4c123.o(.text)
    SYSCTL_Handler                           0x000002d7   Thumb Code     2  startup_tm4c123.o(.text)
    FLASH_Handler                            0x000002d9   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOF_Handler                            0x000002db   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOG_Handler                            0x000002dd   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOH_Handler                            0x000002df   Thumb Code     2  startup_tm4c123.o(.text)
    UART2_Handler                            0x000002e1   Thumb Code     2  startup_tm4c123.o(.text)
    SSI1_Handler                             0x000002e3   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER3A_Handler                          0x000002e5   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER3B_Handler                          0x000002e7   Thumb Code     2  startup_tm4c123.o(.text)
    I2C1_Handler                             0x000002e9   Thumb Code     2  startup_tm4c123.o(.text)
    QEI1_Handler                             0x000002eb   Thumb Code     2  startup_tm4c123.o(.text)
    CAN0_Handler                             0x000002ed   Thumb Code     2  startup_tm4c123.o(.text)
    CAN1_Handler                             0x000002ef   Thumb Code     2  startup_tm4c123.o(.text)
    CAN2_Handler                             0x000002f1   Thumb Code     2  startup_tm4c123.o(.text)
    HIB_Handler                              0x000002f3   Thumb Code     2  startup_tm4c123.o(.text)
    USB0_Handler                             0x000002f5   Thumb Code     2  startup_tm4c123.o(.text)
    PWM0_3_Handler                           0x000002f7   Thumb Code     2  startup_tm4c123.o(.text)
    UDMA_Handler                             0x000002f9   Thumb Code     2  startup_tm4c123.o(.text)
    UDMAERR_Handler                          0x000002fb   Thumb Code     2  startup_tm4c123.o(.text)
    ADC1SS0_Handler                          0x000002fd   Thumb Code     2  startup_tm4c123.o(.text)
    ADC1SS1_Handler                          0x000002ff   Thumb Code     2  startup_tm4c123.o(.text)
    ADC1SS2_Handler                          0x00000301   Thumb Code     2  startup_tm4c123.o(.text)
    ADC1SS3_Handler                          0x00000303   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOJ_Handler                            0x00000305   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOK_Handler                            0x00000307   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOL_Handler                            0x00000309   Thumb Code     2  startup_tm4c123.o(.text)
    SSI2_Handler                             0x0000030b   Thumb Code     2  startup_tm4c123.o(.text)
    SSI3_Handler                             0x0000030d   Thumb Code     2  startup_tm4c123.o(.text)
    UART3_Handler                            0x0000030f   Thumb Code     2  startup_tm4c123.o(.text)
    UART4_Handler                            0x00000311   Thumb Code     2  startup_tm4c123.o(.text)
    UART5_Handler                            0x00000313   Thumb Code     2  startup_tm4c123.o(.text)
    UART6_Handler                            0x00000315   Thumb Code     2  startup_tm4c123.o(.text)
    UART7_Handler                            0x00000317   Thumb Code     2  startup_tm4c123.o(.text)
    I2C2_Handler                             0x00000319   Thumb Code     2  startup_tm4c123.o(.text)
    I2C3_Handler                             0x0000031b   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER4A_Handler                          0x0000031d   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER4B_Handler                          0x0000031f   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER5A_Handler                          0x00000321   Thumb Code     2  startup_tm4c123.o(.text)
    TIMER5B_Handler                          0x00000323   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER0A_Handler                         0x00000325   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER0B_Handler                         0x00000327   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER1A_Handler                         0x00000329   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER1B_Handler                         0x0000032b   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER2A_Handler                         0x0000032d   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER2B_Handler                         0x0000032f   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER3A_Handler                         0x00000331   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER3B_Handler                         0x00000333   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER4A_Handler                         0x00000335   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER4B_Handler                         0x00000337   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER5A_Handler                         0x00000339   Thumb Code     2  startup_tm4c123.o(.text)
    WTIMER5B_Handler                         0x0000033b   Thumb Code     2  startup_tm4c123.o(.text)
    FPU_Handler                              0x0000033d   Thumb Code     2  startup_tm4c123.o(.text)
    I2C4_Handler                             0x0000033f   Thumb Code     2  startup_tm4c123.o(.text)
    I2C5_Handler                             0x00000341   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOM_Handler                            0x00000343   Thumb Code     2  startup_tm4c123.o(.text)
    GPION_Handler                            0x00000345   Thumb Code     2  startup_tm4c123.o(.text)
    QEI2_Handler                             0x00000347   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP0_Handler                           0x00000349   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP1_Handler                           0x0000034b   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP2_Handler                           0x0000034d   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP3_Handler                           0x0000034f   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP4_Handler                           0x00000351   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP5_Handler                           0x00000353   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP6_Handler                           0x00000355   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOP7_Handler                           0x00000357   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ0_Handler                           0x00000359   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ1_Handler                           0x0000035b   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ2_Handler                           0x0000035d   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ3_Handler                           0x0000035f   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ4_Handler                           0x00000361   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ5_Handler                           0x00000363   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ6_Handler                           0x00000365   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOQ7_Handler                           0x00000367   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOR_Handler                            0x00000369   Thumb Code     2  startup_tm4c123.o(.text)
    GPIOS_Handler                            0x0000036b   Thumb Code     2  startup_tm4c123.o(.text)
    PMW1_0_Handler                           0x0000036d   Thumb Code     2  startup_tm4c123.o(.text)
    PWM1_1_Handler                           0x0000036f   Thumb Code     2  startup_tm4c123.o(.text)
    PWM1_2_Handler                           0x00000371   Thumb Code     2  startup_tm4c123.o(.text)
    PWM1_3_Handler                           0x00000373   Thumb Code     2  startup_tm4c123.o(.text)
    PWM1_FAULT_Handler                       0x00000375   Thumb Code     2  startup_tm4c123.o(.text)
    __aeabi_dmul                             0x00000381   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x00000465   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x00000543   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2f                              0x0000055d   Thumb Code    56  d2f.o(.text)
    __aeabi_llsl                             0x00000595   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x00000595   Thumb Code     0  llshl.o(.text)
    __I$use$fp                               0x000005b3   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x000005b3   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x000005c5   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x00000621   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0000063f   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x000006dd   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x000006dd   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x00000701   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x00000701   Thumb Code     0  llushr.o(.text)
    __decompress                             0x00000721   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x00000721   Thumb Code    86  __dczerorl2.o(.text)
    ADCIntRegister                           0x000007a5   Thumb Code    92  adc.o(i.ADCIntRegister)
    ANO_DT_Init                              0x0000080d   Thumb Code   166  ano_dt_lx.o(i.ANO_DT_Init)
    ANO_DT_LX_Data_Receive_Prepare           0x00000abd   Thumb Code   258  ano_dt_lx.o(i.ANO_DT_LX_Data_Receive_Prepare)
    ANO_LX_Data_Exchange_Task                0x00000be5   Thumb Code    68  ano_dt_lx.o(i.ANO_LX_Data_Exchange_Task)
    ANO_LX_Task                              0x00000c29   Thumb Code   144  ano_lx.o(i.ANO_LX_Task)
    All_Init                                 0x00000e65   Thumb Code    94  drv_bsp.o(i.All_Init)
    AnoOF_Check_State                        0x00000ec9   Thumb Code   180  drv_anoof.o(i.AnoOF_Check_State)
    AnoOF_GetOneByte                         0x00001105   Thumb Code   252  drv_anoof.o(i.AnoOF_GetOneByte)
    CK_Back                                  0x00001235   Thumb Code    12  ano_dt_lx.o(i.CK_Back)
    CMD_Send                                 0x000012ad   Thumb Code    22  ano_dt_lx.o(i.CMD_Send)
    DrvAdcInit                               0x00001359   Thumb Code   164  drv_adc.o(i.DrvAdcInit)
    DrvLedOnOff                              0x00001411   Thumb Code   170  drv_led.o(i.DrvLedOnOff)
    DrvMotorPWMSet                           0x000014c5   Thumb Code   222  drv_pwmout.o(i.DrvMotorPWMSet)
    DrvPpmGetOneCh                           0x000015b1   Thumb Code    92  drv_bsp.o(i.DrvPpmGetOneCh)
    DrvPwmOutInit                            0x00001615   Thumb Code   666  drv_pwmout.o(i.DrvPwmOutInit)
    DrvRcInputInit                           0x000018ed   Thumb Code    20  drv_bsp.o(i.DrvRcInputInit)
    DrvRcInputTask                           0x00001905   Thumb Code   232  drv_bsp.o(i.DrvRcInputTask)
    DrvRcPpmInit                             0x000019fd   Thumb Code   182  drv_rcin.o(i.DrvRcPpmInit)
    DrvRcSbusInit                            0x00001ad1   Thumb Code   156  drv_rcin.o(i.DrvRcSbusInit)
    DrvSbusGetOneByte                        0x00001b89   Thumb Code   560  drv_bsp.o(i.DrvSbusGetOneByte)
    DrvSysInit                               0x00001dd1   Thumb Code    68  drv_sys.o(i.DrvSysInit)
    DrvTimerFcInit                           0x00001e21   Thumb Code   110  drv_timer.o(i.DrvTimerFcInit)
    DrvUart1Init                             0x00001e9d   Thumb Code   192  drv_uart.o(i.DrvUart1Init)
    DrvUart1SendBuf                          0x00001f6d   Thumb Code    42  drv_uart.o(i.DrvUart1SendBuf)
    DrvUart1TxCheck                          0x00001fa1   Thumb Code    52  drv_uart.o(i.DrvUart1TxCheck)
    DrvUart2Init                             0x00001fe5   Thumb Code   188  drv_uart.o(i.DrvUart2Init)
    DrvUart2TxCheck                          0x000020bd   Thumb Code    52  drv_uart.o(i.DrvUart2TxCheck)
    DrvUart3Init                             0x00002101   Thumb Code   188  drv_uart.o(i.DrvUart3Init)
    DrvUart3TxCheck                          0x000021d9   Thumb Code    52  drv_uart.o(i.DrvUart3TxCheck)
    DrvUart4Init                             0x0000221d   Thumb Code   188  drv_uart.o(i.DrvUart4Init)
    DrvUart4TxCheck                          0x000022f5   Thumb Code    52  drv_uart.o(i.DrvUart4TxCheck)
    DrvUart5Init                             0x00002339   Thumb Code   206  drv_uart.o(i.DrvUart5Init)
    DrvUart5SendBuf                          0x0000242d   Thumb Code    42  drv_uart.o(i.DrvUart5SendBuf)
    DrvUart5TxCheck                          0x00002461   Thumb Code    52  drv_uart.o(i.DrvUart5TxCheck)
    DrvUartDataCheck                         0x000024a5   Thumb Code    24  drv_uart.o(i.DrvUartDataCheck)
    Drv_AdcGetBatVot                         0x000024bd   Thumb Code    84  drv_adc.o(i.Drv_AdcGetBatVot)
    DvrLedInit                               0x0000252d   Thumb Code   120  drv_led.o(i.DvrLedInit)
    FC_Lock                                  0x0000272d   Thumb Code    56  lx_fc_fun.o(i.FC_Lock)
    FC_Unlock                                0x0000276d   Thumb Code    56  lx_fc_fun.o(i.FC_Unlock)
    GPS_Data_Prepare_Task                    0x00002875   Thumb Code   306  drv_ubloxgps.o(i.GPS_Data_Prepare_Task)
    GPS_Rate_H                               0x000029c1   Thumb Code    12  drv_ubloxgps.o(i.GPS_Rate_H)
    GetSysRunTimeMs                          0x00002a61   Thumb Code     6  drv_sys.o(i.GetSysRunTimeMs)
    GetSysRunTimeUs                          0x00002a6d   Thumb Code    46  drv_sys.o(i.GetSysRunTimeUs)
    Horizontal_Calibrate                     0x00002aa1   Thumb Code    50  lx_fc_fun.o(i.Horizontal_Calibrate)
    Init_GPS                                 0x00002ad9   Thumb Code   196  drv_ubloxgps.o(i.Init_GPS)
    IntEnable                                0x00002bb9   Thumb Code    92  interrupt.o(i.IntEnable)
    IntRegister                              0x00002c1d   Thumb Code    44  interrupt.o(i.IntRegister)
    LED_1ms_DRV                              0x00002c51   Thumb Code    78  drv_led.o(i.LED_1ms_DRV)
    LX_Cali_Trig_Check                       0x00002ca9   Thumb Code   150  lx_fc_state.o(i.LX_Cali_Trig_Check)
    LX_Change_Mode                           0x00002d4d   Thumb Code    80  lx_fc_fun.o(i.LX_Change_Mode)
    LX_FC_EXT_Sensor_Task                    0x00002da9   Thumb Code    78  lx_fc_ext_sensor.o(i.LX_FC_EXT_Sensor_Task)
    LX_FC_State_Task                         0x00002e09   Thumb Code    26  lx_fc_state.o(i.LX_FC_State_Task)
    Mag_Calibrate                            0x0000300d   Thumb Code    50  lx_fc_fun.o(i.Mag_Calibrate)
    MyDelayMs                                0x00003045   Thumb Code    30  drv_sys.o(i.MyDelayMs)
    NoUse                                    0x00003069   Thumb Code     2  drv_uart.o(i.NoUse)
    OneKey_Land                              0x0000306d   Thumb Code    50  lx_fc_fun.o(i.OneKey_Land)
    OneKey_Return_Home                       0x000030a5   Thumb Code    50  lx_fc_fun.o(i.OneKey_Return_Home)
    OneKey_Takeoff                           0x000030dd   Thumb Code    66  lx_fc_fun.o(i.OneKey_Takeoff)
    PAR_Back                                 0x00003125   Thumb Code    14  ano_dt_lx.o(i.PAR_Back)
    Scheduler_Run                            0x000034a5   Thumb Code    76  ano_scheduler.o(i.Scheduler_Run)
    Scheduler_Setup                          0x000034f5   Thumb Code    76  ano_scheduler.o(i.Scheduler_Setup)
    SysCtlClockGet                           0x00003545   Thumb Code   304  sysctl.o(i.SysCtlClockGet)
    SysTick_Handler                          0x00003699   Thumb Code    20  drv_sys.o(i.SysTick_Handler)
    SysTick_Init                             0x000036b1   Thumb Code    54  drv_sys.o(i.SysTick_Init)
    SystemInit                               0x000036e9   Thumb Code   112  system_tm4c123.o(i.SystemInit)
    Timer0Irq                                0x00003781   Thumb Code    16  drv_timer.o(i.Timer0Irq)
    TimerIntClear                            0x00003795   Thumb Code     4  timer.o(i.TimerIntClear)
    TimerIntRegister                         0x00003799   Thumb Code    88  timer.o(i.TimerIntRegister)
    UART1_IRQHandler                         0x00003801   Thumb Code    82  drv_uart.o(i.UART1_IRQHandler)
    UART2_IRQHandler                         0x00003859   Thumb Code    82  drv_uart.o(i.UART2_IRQHandler)
    UART3_IRQHandler                         0x000038b1   Thumb Code    82  drv_uart.o(i.UART3_IRQHandler)
    UART4_IRQHandler                         0x00003909   Thumb Code    82  drv_uart.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x00003961   Thumb Code    82  drv_uart.o(i.UART5_IRQHandler)
    UARTIntRegister                          0x000039b9   Thumb Code    70  uart.o(i.UARTIntRegister)
    UBLOX_M8_GPS_Data_Receive                0x00003a11   Thumb Code   328  drv_ubloxgps.o(i.UBLOX_M8_GPS_Data_Receive)
    UserTask_OneKeyCmd                       0x00003b75   Thumb Code   178  user_task.o(i.UserTask_OneKeyCmd)
    __scatterload_copy                       0x00003c3d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00003c4b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00003c4d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    drvAdcTrigger                            0x00003c5d   Thumb Code    18  drv_adc.o(i.drvAdcTrigger)
    main                                     0x00003c75   Thumb Code    16  main.o(i.main)
    my_deadzone                              0x00003c85   Thumb Code    54  ano_math.o(i.my_deadzone)
    Region$$Table$$Base                      0x000041f4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00004214   Number         0  anon$$obj.o(Region$$Table)
    fc_bat                                   0x2000005e   Data           4  ano_lx.o(.data)
    fc_att                                   0x20000062   Data           7  ano_lx.o(.data)
    fc_vel                                   0x20000069   Data           6  ano_lx.o(.data)
    sti_fun                                  0x20000082   Data           2  lx_fc_state.o(.data)
    pvt_receive_updata                       0x2000009e   Data           1  drv_ubloxgps.o(.data)
    fre_test_cnt                             0x200000a0   Data           2  drv_ubloxgps.o(.data)
    Period_Out_H                             0x200000a2   Data          14  drv_ubloxgps.o(.data)
    Period_Out_L                             0x200000b0   Data          14  drv_ubloxgps.o(.data)
    Pulse_Period                             0x200000be   Data          28  drv_ubloxgps.o(.data)
    Baud115200                               0x200000da   Data          28  drv_ubloxgps.o(.data)
    Save_Con                                 0x200000f6   Data          21  drv_ubloxgps.o(.data)
    POLLSH_SET                               0x2000010b   Data          11  drv_ubloxgps.o(.data)
    SOL_SET                                  0x20000116   Data          11  drv_ubloxgps.o(.data)
    VELNED_SET                               0x20000121   Data          11  drv_ubloxgps.o(.data)
    TIMEUTC_SET                              0x2000012c   Data          11  drv_ubloxgps.o(.data)
    DOP_SET                                  0x20000137   Data          11  drv_ubloxgps.o(.data)
    PVT_SET                                  0x20000142   Data          11  drv_ubloxgps.o(.data)
    pvt_recerve_ok_cnt                       0x20000152   Data           1  drv_ubloxgps.o(.data)
    AdcTemp                                  0x20000160   Data           4  drv_adc.o(.data)
    led                                      0x20000164   Data           4  drv_led.o(.data)
    U1TxInCnt                                0x2000017c   Data           1  drv_uart.o(.data)
    U1TxOutCnt                               0x2000017d   Data           1  drv_uart.o(.data)
    U2TxInCnt                                0x2000017e   Data           1  drv_uart.o(.data)
    U2TxOutCnt                               0x2000017f   Data           1  drv_uart.o(.data)
    U3TxInCnt                                0x20000180   Data           1  drv_uart.o(.data)
    U3TxOutCnt                               0x20000181   Data           1  drv_uart.o(.data)
    U4TxInCnt                                0x20000182   Data           1  drv_uart.o(.data)
    U4TxOutCnt                               0x20000183   Data           1  drv_uart.o(.data)
    U5TxInCnt                                0x20000184   Data           1  drv_uart.o(.data)
    U5TxOutCnt                               0x20000185   Data           1  drv_uart.o(.data)
    send_buffer                              0x2000066c   Data          50  ano_dt_lx.o(.bss)
    dt                                       0x200006a0   Data        1564  ano_dt_lx.o(.bss)
    rt_tar                                   0x20000dbc   Data          14  ano_lx.o(.bss)
    pwm_to_esc                               0x20000dca   Data          16  ano_lx.o(.bss)
    fc_att_qua                               0x20000dda   Data           9  ano_lx.o(.bss)
    ext_sens                                 0x20000df4   Data          48  lx_fc_ext_sensor.o(.bss)
    fc_sta                                   0x20000e24   Data          11  lx_fc_state.o(.bss)
    rc_in                                    0x20000e30   Data          82  drv_bsp.o(.bss)
    ano_of                                   0x20000e9c   Data          60  drv_anoof.o(.bss)
    ubx                                      0x20000f18   Data         100  drv_ubloxgps.o(.bss)
    U1TxDataTemp                             0x20000f7c   Data         256  drv_uart.o(.bss)
    U2TxDataTemp                             0x2000107c   Data         256  drv_uart.o(.bss)
    U3TxDataTemp                             0x2000117c   Data         256  drv_uart.o(.bss)
    U4TxDataTemp                             0x2000127c   Data         256  drv_uart.o(.bss)
    U5TxDataTemp                             0x2000137c   Data         256  drv_uart.o(.bss)
    __initial_sp                             0x20001e80   Data           0  startup_tm4c123.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00000285

  Load Region LR_1 (Base: 0x00000000, Size: 0x00004880, Max: 0xffffffff, ABSOLUTE, COMPRESSED[0x000042b4])

    Execution Region ER_RO (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00004214, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x0000026c   Data   RO         1359    RESET               startup_tm4c123.o
    0x0000026c   0x0000026c   0x00000000   Code   RO         4904  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x0000026c   0x0000026c   0x00000004   Code   RO         4923    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x00000270   0x00000270   0x00000004   Code   RO         4926    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x00000274   0x00000274   0x00000000   Code   RO         4928    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x00000274   0x00000274   0x00000000   Code   RO         4930    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x00000274   0x00000274   0x00000008   Code   RO         4931    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x0000027c   0x0000027c   0x00000004   Code   RO         4938    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x00000280   0x00000280   0x00000000   Code   RO         4933    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x00000280   0x00000280   0x00000000   Code   RO         4935    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x00000280   0x00000280   0x00000004   Code   RO         4924    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x00000284   0x00000284   0x000000fc   Code   RO         1360  * .text               startup_tm4c123.o
    0x00000380   0x00000380   0x000000e4   Code   RO         4909    .text               mf_w.l(dmul.o)
    0x00000464   0x00000464   0x000000de   Code   RO         4911    .text               mf_w.l(ddiv.o)
    0x00000542   0x00000542   0x0000001a   Code   RO         4915    .text               mf_w.l(dfltui.o)
    0x0000055c   0x0000055c   0x00000038   Code   RO         4921    .text               mf_w.l(d2f.o)
    0x00000594   0x00000594   0x0000001e   Code   RO         4939    .text               mc_w.l(llshl.o)
    0x000005b2   0x000005b2   0x00000000   Code   RO         4943    .text               mc_w.l(iusefp.o)
    0x000005b2   0x000005b2   0x0000006e   Code   RO         4944    .text               mf_w.l(fepilogue.o)
    0x00000620   0x00000620   0x000000ba   Code   RO         4946    .text               mf_w.l(depilogue.o)
    0x000006da   0x000006da   0x00000002   PAD
    0x000006dc   0x000006dc   0x00000024   Code   RO         4948    .text               mc_w.l(init.o)
    0x00000700   0x00000700   0x00000020   Code   RO         4950    .text               mc_w.l(llushr.o)
    0x00000720   0x00000720   0x00000056   Code   RO         4960    .text               mc_w.l(__dczerorl2.o)
    0x00000776   0x00000776   0x00000002   PAD
    0x00000778   0x00000778   0x0000002c   Code   RO          893    i.ADC0Handler       drv_adc.o
    0x000007a4   0x000007a4   0x00000068   Code   RO         1452    i.ADCIntRegister    driverlib.lib(adc.o)
    0x0000080c   0x0000080c   0x000000ac   Code   RO          196    i.ANO_DT_Init       ano_dt_lx.o
    0x000008b8   0x000008b8   0x00000204   Code   RO          197    i.ANO_DT_LX_Data_Receive_Anl  ano_dt_lx.o
    0x00000abc   0x00000abc   0x00000118   Code   RO          198    i.ANO_DT_LX_Data_Receive_Prepare  ano_dt_lx.o
    0x00000bd4   0x00000bd4   0x00000010   Code   RO          199    i.ANO_DT_LX_Send_Data  ano_dt_lx.o
    0x00000be4   0x00000be4   0x00000044   Code   RO          200    i.ANO_LX_Data_Exchange_Task  ano_dt_lx.o
    0x00000c28   0x00000c28   0x0000009c   Code   RO          302    i.ANO_LX_Task       ano_lx.o
    0x00000cc4   0x00000cc4   0x000001a0   Code   RO          201    i.Add_Send_Data     ano_dt_lx.o
    0x00000e64   0x00000e64   0x00000064   Code   RO          538    i.All_Init          drv_bsp.o
    0x00000ec8   0x00000ec8   0x000000c0   Code   RO          739    i.AnoOF_Check_State  drv_anoof.o
    0x00000f88   0x00000f88   0x0000017c   Code   RO          740    i.AnoOF_DataAnl     drv_anoof.o
    0x00001104   0x00001104   0x0000010c   Code   RO          741    i.AnoOF_GetOneByte  drv_anoof.o
    0x00001210   0x00001210   0x00000024   Code   RO          303    i.Bat_Voltage_Data_Handle  ano_lx.o
    0x00001234   0x00001234   0x00000010   Code   RO          202    i.CK_Back           ano_dt_lx.o
    0x00001244   0x00001244   0x00000068   Code   RO          203    i.CK_Back_Check     ano_dt_lx.o
    0x000012ac   0x000012ac   0x0000001c   Code   RO          204    i.CMD_Send          ano_dt_lx.o
    0x000012c8   0x000012c8   0x00000090   Code   RO          205    i.Check_To_Send     ano_dt_lx.o
    0x00001358   0x00001358   0x000000b8   Code   RO          894    i.DrvAdcInit        drv_adc.o
    0x00001410   0x00001410   0x000000b4   Code   RO          949    i.DrvLedOnOff       drv_led.o
    0x000014c4   0x000014c4   0x000000ec   Code   RO          992    i.DrvMotorPWMSet    drv_pwmout.o
    0x000015b0   0x000015b0   0x00000064   Code   RO          539    i.DrvPpmGetOneCh    drv_bsp.o
    0x00001614   0x00001614   0x000002d8   Code   RO          993    i.DrvPwmOutInit     drv_pwmout.o
    0x000018ec   0x000018ec   0x00000018   Code   RO          540    i.DrvRcInputInit    drv_bsp.o
    0x00001904   0x00001904   0x000000f8   Code   RO          541    i.DrvRcInputTask    drv_bsp.o
    0x000019fc   0x000019fc   0x000000d4   Code   RO         1036    i.DrvRcPpmInit      drv_rcin.o
    0x00001ad0   0x00001ad0   0x000000b8   Code   RO         1037    i.DrvRcSbusInit     drv_rcin.o
    0x00001b88   0x00001b88   0x00000248   Code   RO          542    i.DrvSbusGetOneByte  drv_bsp.o
    0x00001dd0   0x00001dd0   0x00000050   Code   RO          833    i.DrvSysInit        drv_sys.o
    0x00001e20   0x00001e20   0x0000007c   Code   RO         1327    i.DrvTimerFcInit    drv_timer.o
    0x00001e9c   0x00001e9c   0x000000d0   Code   RO         1083    i.DrvUart1Init      drv_uart.o
    0x00001f6c   0x00001f6c   0x00000034   Code   RO         1084    i.DrvUart1SendBuf   drv_uart.o
    0x00001fa0   0x00001fa0   0x00000044   Code   RO         1085    i.DrvUart1TxCheck   drv_uart.o
    0x00001fe4   0x00001fe4   0x000000d8   Code   RO         1086    i.DrvUart2Init      drv_uart.o
    0x000020bc   0x000020bc   0x00000044   Code   RO         1088    i.DrvUart2TxCheck   drv_uart.o
    0x00002100   0x00002100   0x000000d8   Code   RO         1089    i.DrvUart3Init      drv_uart.o
    0x000021d8   0x000021d8   0x00000044   Code   RO         1091    i.DrvUart3TxCheck   drv_uart.o
    0x0000221c   0x0000221c   0x000000d8   Code   RO         1092    i.DrvUart4Init      drv_uart.o
    0x000022f4   0x000022f4   0x00000044   Code   RO         1094    i.DrvUart4TxCheck   drv_uart.o
    0x00002338   0x00002338   0x000000f4   Code   RO         1095    i.DrvUart5Init      drv_uart.o
    0x0000242c   0x0000242c   0x00000034   Code   RO         1096    i.DrvUart5SendBuf   drv_uart.o
    0x00002460   0x00002460   0x00000044   Code   RO         1097    i.DrvUart5TxCheck   drv_uart.o
    0x000024a4   0x000024a4   0x00000018   Code   RO         1098    i.DrvUartDataCheck  drv_uart.o
    0x000024bc   0x000024bc   0x00000070   Code   RO          895    i.Drv_AdcGetBatVot  drv_adc.o
    0x0000252c   0x0000252c   0x00000084   Code   RO          950    i.DvrLedInit        drv_led.o
    0x000025b0   0x000025b0   0x0000017c   Code   RO          304    i.ESC_Output        ano_lx.o
    0x0000272c   0x0000272c   0x00000040   Code   RO          412    i.FC_Lock           lx_fc_fun.o
    0x0000276c   0x0000276c   0x00000040   Code   RO          413    i.FC_Unlock         lx_fc_fun.o
    0x000027ac   0x000027ac   0x000000c8   Code   RO          206    i.Frame_Send        ano_dt_lx.o
    0x00002874   0x00002874   0x0000014c   Code   RO          781    i.GPS_Data_Prepare_Task  drv_ubloxgps.o
    0x000029c0   0x000029c0   0x00000010   Code   RO          782    i.GPS_Rate_H        drv_ubloxgps.o
    0x000029d0   0x000029d0   0x00000090   Code   RO          375    i.General_Velocity_Data_Handle  lx_fc_ext_sensor.o
    0x00002a60   0x00002a60   0x0000000c   Code   RO          834    i.GetSysRunTimeMs   drv_sys.o
    0x00002a6c   0x00002a6c   0x00000034   Code   RO          835    i.GetSysRunTimeUs   drv_sys.o
    0x00002aa0   0x00002aa0   0x00000038   Code   RO          415    i.Horizontal_Calibrate  lx_fc_fun.o
    0x00002ad8   0x00002ad8   0x000000e0   Code   RO          784    i.Init_GPS          drv_ubloxgps.o
    0x00002bb8   0x00002bb8   0x00000064   Code   RO         3653    i.IntEnable         driverlib.lib(interrupt.o)
    0x00002c1c   0x00002c1c   0x00000034   Code   RO         3665    i.IntRegister       driverlib.lib(interrupt.o)
    0x00002c50   0x00002c50   0x00000058   Code   RO          951    i.LED_1ms_DRV       drv_led.o
    0x00002ca8   0x00002ca8   0x000000a4   Code   RO          496    i.LX_Cali_Trig_Check  lx_fc_state.o
    0x00002d4c   0x00002d4c   0x0000005c   Code   RO          417    i.LX_Change_Mode    lx_fc_fun.o
    0x00002da8   0x00002da8   0x00000060   Code   RO          376    i.LX_FC_EXT_Sensor_Task  lx_fc_ext_sensor.o
    0x00002e08   0x00002e08   0x00000020   Code   RO          497    i.LX_FC_State_Task  lx_fc_state.o
    0x00002e28   0x00002e28   0x000001d0   Code   RO          498    i.LX_Unlock_Lock_Check  lx_fc_state.o
    0x00002ff8   0x00002ff8   0x00000002   Code   RO           95    i.Loop_1000Hz       ano_scheduler.o
    0x00002ffa   0x00002ffa   0x00000002   Code   RO           96    i.Loop_100Hz        ano_scheduler.o
    0x00002ffc   0x00002ffc   0x00000002   Code   RO           97    i.Loop_200Hz        ano_scheduler.o
    0x00002ffe   0x00002ffe   0x00000002   Code   RO           98    i.Loop_20Hz         ano_scheduler.o
    0x00003000   0x00003000   0x00000002   Code   RO           99    i.Loop_2Hz          ano_scheduler.o
    0x00003002   0x00003002   0x00000002   Code   RO          100    i.Loop_500Hz        ano_scheduler.o
    0x00003004   0x00003004   0x00000008   Code   RO          101    i.Loop_50Hz         ano_scheduler.o
    0x0000300c   0x0000300c   0x00000038   Code   RO          418    i.Mag_Calibrate     lx_fc_fun.o
    0x00003044   0x00003044   0x00000024   Code   RO          836    i.MyDelayMs         drv_sys.o
    0x00003068   0x00003068   0x00000002   Code   RO         1099    i.NoUse             drv_uart.o
    0x0000306a   0x0000306a   0x00000002   PAD
    0x0000306c   0x0000306c   0x00000038   Code   RO          419    i.OneKey_Land       lx_fc_fun.o
    0x000030a4   0x000030a4   0x00000038   Code   RO          420    i.OneKey_Return_Home  lx_fc_fun.o
    0x000030dc   0x000030dc   0x00000048   Code   RO          421    i.OneKey_Takeoff    lx_fc_fun.o
    0x00003124   0x00003124   0x00000014   Code   RO          207    i.PAR_Back          ano_dt_lx.o
    0x00003138   0x00003138   0x00000084   Code   RO         1038    i.PPM_Decode        drv_rcin.o
    0x000031bc   0x000031bc   0x00000298   Code   RO          305    i.RC_Data_Task      ano_lx.o
    0x00003454   0x00003454   0x00000050   Code   RO         1039    i.Sbus_IRQHandler   drv_rcin.o
    0x000034a4   0x000034a4   0x00000050   Code   RO          102    i.Scheduler_Run     ano_scheduler.o
    0x000034f4   0x000034f4   0x00000050   Code   RO          103    i.Scheduler_Setup   ano_scheduler.o
    0x00003544   0x00003544   0x00000154   Code   RO         1716    i.SysCtlClockGet    driverlib.lib(sysctl.o)
    0x00003698   0x00003698   0x00000018   Code   RO          837    i.SysTick_Handler   drv_sys.o
    0x000036b0   0x000036b0   0x00000036   Code   RO          838    i.SysTick_Init      drv_sys.o
    0x000036e6   0x000036e6   0x00000002   PAD
    0x000036e8   0x000036e8   0x00000098   Code   RO         1368    i.SystemInit        system_tm4c123.o
    0x00003780   0x00003780   0x00000014   Code   RO         1328    i.Timer0Irq         drv_timer.o
    0x00003794   0x00003794   0x00000004   Code   RO         2081    i.TimerIntClear     driverlib.lib(timer.o)
    0x00003798   0x00003798   0x00000068   Code   RO         2084    i.TimerIntRegister  driverlib.lib(timer.o)
    0x00003800   0x00003800   0x00000058   Code   RO         1100    i.UART1_IRQHandler  drv_uart.o
    0x00003858   0x00003858   0x00000058   Code   RO         1101    i.UART2_IRQHandler  drv_uart.o
    0x000038b0   0x000038b0   0x00000058   Code   RO         1102    i.UART3_IRQHandler  drv_uart.o
    0x00003908   0x00003908   0x00000058   Code   RO         1103    i.UART4_IRQHandler  drv_uart.o
    0x00003960   0x00003960   0x00000058   Code   RO         1104    i.UART5_IRQHandler  drv_uart.o
    0x000039b8   0x000039b8   0x00000058   Code   RO         2339    i.UARTIntRegister   driverlib.lib(uart.o)
    0x00003a10   0x00003a10   0x00000164   Code   RO          785    i.UBLOX_M8_GPS_Data_Receive  drv_ubloxgps.o
    0x00003b74   0x00003b74   0x000000c8   Code   RO          165    i.UserTask_OneKeyCmd  user_task.o
    0x00003c3c   0x00003c3c   0x0000000e   Code   RO         4954    i.__scatterload_copy  mc_w.l(handlers.o)
    0x00003c4a   0x00003c4a   0x00000002   Code   RO         4955    i.__scatterload_null  mc_w.l(handlers.o)
    0x00003c4c   0x00003c4c   0x0000000e   Code   RO         4956    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x00003c5a   0x00003c5a   0x00000002   PAD
    0x00003c5c   0x00003c5c   0x00000018   Code   RO          896    i.drvAdcTrigger     drv_adc.o
    0x00003c74   0x00003c74   0x00000010   Code   RO            4    i.main              main.o
    0x00003c84   0x00003c84   0x00000036   Code   RO          610    i.my_deadzone       ano_math.o
    0x00003cba   0x00003cba   0x00000002   PAD
    0x00003cbc   0x00003cbc   0x000000b8   Code   RO          543    i.rcSignalCheck     drv_bsp.o
    0x00003d74   0x00003d74   0x00000254   Data   RO         1769    .constdata          driverlib.lib(sysctl.o)
    0x00003fc8   0x00003fc8   0x000000a0   Data   RO         2105    .constdata          driverlib.lib(timer.o)
    0x00004068   0x00004068   0x00000080   Data   RO         2356    .constdata          driverlib.lib(uart.o)
    0x000040e8   0x000040e8   0x0000010c   Data   RO         3669    .constdata          driverlib.lib(interrupt.o)
    0x000041f4   0x000041f4   0x00000020   Data   RO         4952    Region$$Table       anon$$obj.o


    Execution Region ER_RW (Exec base: 0x20000000, Load base: 0x00004214, Size: 0x0000066c, Max: 0xffffffff, ABSOLUTE, COMPRESSED[0x000000a0])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000054   Data   RW          104    .data               ano_scheduler.o
    0x20000054   COMPRESSED   0x00000004   Data   RW          166    .data               user_task.o
    0x20000058   COMPRESSED   0x00000006   Data   RW          209    .data               ano_dt_lx.o
    0x2000005e   COMPRESSED   0x0000001e   Data   RW          307    .data               ano_lx.o
    0x2000007c   COMPRESSED   0x00000004   Data   RW          378    .data               lx_fc_ext_sensor.o
    0x20000080   COMPRESSED   0x00000001   Data   RW          422    .data               lx_fc_fun.o
    0x20000081   COMPRESSED   0x00000001   PAD
    0x20000082   COMPRESSED   0x00000006   Data   RW          500    .data               lx_fc_state.o
    0x20000088   COMPRESSED   0x00000013   Data   RW          545    .data               drv_bsp.o
    0x2000009b   COMPRESSED   0x00000003   Data   RW          743    .data               drv_anoof.o
    0x2000009e   COMPRESSED   0x000000b6   Data   RW          787    .data               drv_ubloxgps.o
    0x20000154   COMPRESSED   0x00000004   PAD
    0x20000158   COMPRESSED   0x00000008   Data   RW          839    .data               drv_sys.o
    0x20000160   COMPRESSED   0x00000004   Data   RW          897    .data               drv_adc.o
    0x20000164   COMPRESSED   0x0000000c   Data   RW          952    .data               drv_led.o
    0x20000170   COMPRESSED   0x0000000c   Data   RW         1040    .data               drv_rcin.o
    0x2000017c   COMPRESSED   0x0000000a   Data   RW         1106    .data               drv_uart.o
    0x20000186   COMPRESSED   0x0000027a   PAD
    0x20000400   COMPRESSED   0x0000026c   Data   RW         3670    vtable              driverlib.lib(interrupt.o)


    Execution Region ER_ZI (Exec base: 0x2000066c, Load base: 0x000042b4, Size: 0x00001814, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x2000066c        -       0x00000750   Zero   RW          208    .bss                ano_dt_lx.o
    0x20000dbc        -       0x00000038   Zero   RW          306    .bss                ano_lx.o
    0x20000df4        -       0x00000030   Zero   RW          377    .bss                lx_fc_ext_sensor.o
    0x20000e24        -       0x0000000b   Zero   RW          499    .bss                lx_fc_state.o
    0x20000e2f   0x000042b4   0x00000001   PAD
    0x20000e30        -       0x0000006b   Zero   RW          544    .bss                drv_bsp.o
    0x20000e9b   0x000042b4   0x00000001   PAD
    0x20000e9c        -       0x0000007c   Zero   RW          742    .bss                drv_anoof.o
    0x20000f18        -       0x00000064   Zero   RW          786    .bss                drv_ubloxgps.o
    0x20000f7c        -       0x00000500   Zero   RW         1105    .bss                drv_uart.o
    0x2000147c   0x000042b4   0x00000004   PAD
    0x20001480        -       0x00000a00   Zero   RW         1357    STACK               startup_tm4c123.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1980        130          0          6       1872      10932   ano_dt_lx.o
      1236        106          0         30         56       6862   ano_lx.o
        54          0          0          0          0        746   ano_math.o
       180          8          0         84          0       5418   ano_scheduler.o
       364         64          0          4          0       2774   drv_adc.o
       840         44          0          3        124       4124   drv_anoof.o
      1240         74          0         19        107       5910   drv_bsp.o
       400         32          0         12          0       2807   drv_led.o
       964         76          0          0          0       2141   drv_pwmout.o
       608         80          0         12          0       2551   drv_rcin.o
       258         34          0          8          0      34181   drv_sys.o
       144         18          0          0          0       1217   drv_timer.o
      2010        268          0         10       1280      14555   drv_uart.o
       928         86          0        182        100       4658   drv_ubloxgps.o
       240         42          0          4         48       3315   lx_fc_ext_sensor.o
       516         58          0          1          0       5154   lx_fc_fun.o
       660         46          0          6         11       3109   lx_fc_state.o
        16          0          0          0          0      32623   main.o
       252         10        620          0       2560       5512   startup_tm4c123.o
       152         40          0          0          0        697   system_tm4c123.o
       200         22          0          4          0        789   user_task.o

    ----------------------------------------------------------------------
     13248       <USER>        <GROUP>       1024       6164     150075   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          0        639          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       104         12          0          0          0       4777   adc.o
       152         16        268        620          0       2150   interrupt.o
       340         36        596          0          0       2653   sysctl.o
       108         16        160          0          0       2255   timer.o
        88         18        128          0          0       1795   uart.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        56          0          0          0          0         88   d2f.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      1864        <USER>       <GROUP>        620          0      14538   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       792         98       1152        620          0      13630   driverlib.lib
       238         16          0          0          0        204   mc_w.l
       828          0          0          0          0        704   mf_w.l

    ----------------------------------------------------------------------
      1864        <USER>       <GROUP>        620          0      14538   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15112       1352       1804       1644       6164     155333   Grand Totals
     15112       1352       1804        160       6164     155333   ELF Image Totals (compressed)
     15112       1352       1804        160          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                16916 (  16.52kB)
    Total RW  Size (RW Data + ZI Data)              7808 (   7.63kB)
    Total ROM Size (Code + RO Data + RW Data)      17076 (  16.68kB)

==============================================================================

