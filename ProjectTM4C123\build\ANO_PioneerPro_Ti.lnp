--cpu=Cortex-M4.fp.sp
".\build\main.o"
".\build\ano_scheduler.o"
".\build\user_task.o"
".\build\ano_dt_lx.o"
".\build\ano_lx.o"
".\build\lx_fc_ext_sensor.o"
".\build\lx_fc_fun.o"
".\build\lx_fc_state.o"
".\build\drv_bsp.o"
".\build\ano_math.o"
".\build\drv_anoof.o"
".\build\drv_ubloxgps.o"
".\build\drv_sys.o"
".\build\drv_adc.o"
".\build\drv_led.o"
".\build\drv_pwmout.o"
".\build\drv_rcin.o"
".\build\drv_uart.o"
".\build\drv_usb.o"
".\build\drv_timer.o"
"..\DriversMcu\TM4C123\Libraries\driverlib.lib"
"..\DriversMcu\TM4C123\Libraries\usblib.lib"
".\build\startup_tm4c123.o"
".\build\system_tm4c123.o"
".\build\usb_serial_structs.o"
--library_type=microlib --ro-base 0x00000000 --entry 0x00000000 --rw-base 0x20000000 --entry Reset_Handler --first __Vectors --strict
--entry Reset_Handler --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\build\ANO_PioneerPro_Ti.map" -o .\build\ANO_PioneerPro_Ti.axf