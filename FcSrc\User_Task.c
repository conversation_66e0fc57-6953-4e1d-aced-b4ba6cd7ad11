#include "User_Task.h"
u8 time_1s = 0;
int time = 0;
u8 send_01_flag = 0;


void user_task(void)
{
	if(send_01_flag == 1)
	{
		usart_send01(point.barrier1,point.barrier2,point.barrier3);
		send_01_flag = 0;
	}
	if(fly == 1)
	{
		usart_send02();
		fly = 0;
	}
	if(get_now == 1)
	{
		Send_pandian(send.position,send.animal,send.num);
		
		//Send_pandian(61,1,2);
		get_now = 0;
	}
	if(all_get == 1)
	{
		Send_zong(animal_total_count[1],animal_total_count[2],animal_total_count[3],animal_total_count[4],animal_total_count[5]);
		all_get = 0;
	}
//	else
//	{
//		//no_animal();
//	}
	
	
//	animal_total_count[1] += 1;
////	Send_zong(10,10,10,10,10);
}


void user_1ms(void)
{
DrvUartDataCheck();
if(time_1s==1)
{
	time++;
	led(1);
	if(time>=1000)
	{
		time = 0;
		time_1s = 0;
		led(0);
	
	}
}
else {time = 0;}
	
}
