/* ----------------------------------------------------------------------   
* Copyright (C) 2010 ARM Limited. All rights reserved.   
*   
* $Date:        15. July 2011  
* $Revision: 	V1.0.10  
*   
* Project: 	    CMSIS DSP Library   
* Title:	    arm_cfft_radix4_init_f32.c   
*   
* Description:	Radix-4 Decimation in Frequency Floating-point CFFT & CIFFT Initialization function   
*   
* Target Processor: Cortex-M4/Cortex-M3/Cortex-M0
*  
* Version 1.0.10 2011/7/15 
*    Big Endian support added and Merged M0 and M3/M4 Source code.  
*   
* Version 1.0.3 2010/11/29  
*    Re-organized the CMSIS folders and updated documentation.   
*    
* Version 1.0.2 2010/11/11   
*    Documentation updated.    
*   
* Version 1.0.1 2010/10/05    
*    Production release and review comments incorporated.   
*   
* Version 1.0.0 2010/09/20    
*    Production release and review comments incorporated.   
*   
* Version 0.0.5  2010/04/26    
* 	 incorporated review comments and updated with latest CMSIS layer   
*   
* Version 0.0.3  2010/03/10    
*    Initial version   
* -------------------------------------------------------------------- */


#include "arm_math.h"
#include "arm_common_tables.h"

/**   
 * @ingroup groupTransforms   
 */

/**   
 * @addtogroup CFFT_CIFFT   
 * @{   
 */

/*   
* @brief  Floating-point Twiddle factors Table Generation   
*/


/**   
* \par   
* Example code for Floating-point Twiddle factors Generation:   
* \par   
* <pre>for(i = 0; i< N; i++)   
* {   
*	twiddleCoef[2*i]= cos(i * 2*PI/(float)N);   
*	twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);   
* } </pre>   
* \par   
* where N = 1024	and PI = 3.14159265358979   
* \par   
* Cos and Sin values are in interleaved fashion   
*    
*/

static const float32_t twiddleCoef[2048] = {
  1.000000000000000000f, 0.000000000000000000f,
  0.999981175282601110f, 0.006135884649154475f,
  0.999924701839144500f, 0.012271538285719925f,
  0.999830581795823400f, 0.018406729905804820f,
  0.999698818696204250f, 0.024541228522912288f,
  0.999529417501093140f, 0.030674803176636626f,
  0.999322384588349540f, 0.036807222941358832f,
  0.999077727752645360f, 0.042938256934940820f,
  0.998795456205172410f, 0.049067674327418015f,
  0.998475580573294770f, 0.055195244349689934f,
  0.998118112900149180f, 0.061320736302208578f,
  0.997723066644191640f, 0.067443919563664051f,
  0.997290456678690210f, 0.073564563599667426f,
  0.996820299291165670f, 0.079682437971430126f,
  0.996312612182778000f, 0.085797312344439894f,
  0.995767414467659820f, 0.091908956497132724f,
  0.995184726672196930f, 0.098017140329560604f,
  0.994564570734255420f, 0.104121633872054590f,
  0.993906970002356060f, 0.110222207293883060f,
  0.993211949234794500f, 0.116318630911904750f,
  0.992479534598709970f, 0.122410675199216200f,
  0.991709753669099530f, 0.128498110793793170f,
  0.990902635427780010f, 0.134580708507126170f,
  0.990058210262297120f, 0.140658239332849210f,
  0.989176509964781010f, 0.146730474455361750f,
  0.988257567730749460f, 0.152797185258443440f,
  0.987301418157858430f, 0.158858143333861450f,
  0.986308097244598670f, 0.164913120489969890f,
  0.985277642388941220f, 0.170961888760301220f,
  0.984210092386929030f, 0.177004220412148750f,
  0.983105487431216290f, 0.183039887955140950f,
  0.981963869109555240f, 0.189068664149806190f,
  0.980785280403230430f, 0.195090322016128250f,
  0.979569765685440520f, 0.201104634842091900f,
  0.978317370719627650f, 0.207111376192218560f,
  0.977028142657754390f, 0.213110319916091360f,
  0.975702130038528570f, 0.219101240156869800f,
  0.974339382785575860f, 0.225083911359792830f,
  0.972939952205560180f, 0.231058108280671110f,
  0.971503890986251780f, 0.237023605994367200f,
  0.970031253194543970f, 0.242980179903263870f,
  0.968522094274417380f, 0.248927605745720150f,
  0.966976471044852070f, 0.254865659604514570f,
  0.965394441697689400f, 0.260794117915275510f,
  0.963776065795439840f, 0.266712757474898370f,
  0.962121404269041580f, 0.272621355449948980f,
  0.960430519415565790f, 0.278519689385053060f,
  0.958703474895871600f, 0.284407537211271880f,
  0.956940335732208820f, 0.290284677254462330f,
  0.955141168305770780f, 0.296150888243623790f,
  0.953306040354193860f, 0.302005949319228080f,
  0.951435020969008340f, 0.307849640041534870f,
  0.949528180593036670f, 0.313681740398891520f,
  0.947585591017741090f, 0.319502030816015690f,
  0.945607325380521280f, 0.325310292162262930f,
  0.943593458161960390f, 0.331106305759876430f,
  0.941544065183020810f, 0.336889853392220050f,
  0.939459223602189920f, 0.342660717311994380f,
  0.937339011912574960f, 0.348418680249434560f,
  0.935183509938947610f, 0.354163525420490340f,
  0.932992798834738960f, 0.359895036534988110f,
  0.930766961078983710f, 0.365612997804773850f,
  0.928506080473215590f, 0.371317193951837540f,
  0.926210242138311380f, 0.377007410216418260f,
  0.923879532511286740f, 0.382683432365089780f,
  0.921514039342042010f, 0.388345046698826250f,
  0.919113851690057770f, 0.393992040061048100f,
  0.916679059921042700f, 0.399624199845646790f,
  0.914209755703530690f, 0.405241314004989860f,
  0.911706032005429880f, 0.410843171057903910f,
  0.909167983090522380f, 0.416429560097637150f,
  0.906595704514915330f, 0.422000270799799680f,
  0.903989293123443340f, 0.427555093430282080f,
  0.901348847046022030f, 0.433093818853151960f,
  0.898674465693953820f, 0.438616238538527660f,
  0.895966249756185220f, 0.444122144570429200f,
  0.893224301195515320f, 0.449611329654606540f,
  0.890448723244757880f, 0.455083587126343840f,
  0.887639620402853930f, 0.460538710958240010f,
  0.884797098430937790f, 0.465976495767966180f,
  0.881921264348355050f, 0.471396736825997640f,
  0.879012226428633530f, 0.476799230063322090f,
  0.876070094195406600f, 0.482183772079122720f,
  0.873094978418290090f, 0.487550160148436000f,
  0.870086991108711460f, 0.492898192229784040f,
  0.867046245515692650f, 0.498227666972781870f,
  0.863972856121586810f, 0.503538383725717580f,
  0.860866938637767310f, 0.508830142543106990f,
  0.857728610000272120f, 0.514102744193221660f,
  0.854557988365400530f, 0.519355990165589640f,
  0.851355193105265200f, 0.524589682678468950f,
  0.848120344803297230f, 0.529803624686294610f,
  0.844853565249707120f, 0.534997619887097150f,
  0.841554977436898440f, 0.540171472729892850f,
  0.838224705554838080f, 0.545324988422046460f,
  0.834862874986380010f, 0.550457972936604810f,
  0.831469612302545240f, 0.555570233019602180f,
  0.828045045257755800f, 0.560661576197336030f,
  0.824589302785025290f, 0.565731810783613120f,
  0.821102514991104650f, 0.570780745886967260f,
  0.817584813151583710f, 0.575808191417845340f,
  0.814036329705948410f, 0.580813958095764530f,
  0.810457198252594770f, 0.585797857456438860f,
  0.806847553543799330f, 0.590759701858874160f,
  0.803207531480644940f, 0.595699304492433360f,
  0.799537269107905010f, 0.600616479383868970f,
  0.795836904608883570f, 0.605511041404325550f,
  0.792106577300212390f, 0.610382806276309480f,
  0.788346427626606340f, 0.615231590580626820f,
  0.784556597155575240f, 0.620057211763289100f,
  0.780737228572094490f, 0.624859488142386340f,
  0.776888465673232440f, 0.629638238914926980f,
  0.773010453362736990f, 0.634393284163645490f,
  0.769103337645579700f, 0.639124444863775730f,
  0.765167265622458960f, 0.643831542889791390f,
  0.761202385484261780f, 0.648514401022112440f,
  0.757208846506484570f, 0.653172842953776760f,
  0.753186799043612520f, 0.657806693297078640f,
  0.749136394523459370f, 0.662415777590171780f,
  0.745057785441466060f, 0.666999922303637470f,
  0.740951125354959110f, 0.671558954847018330f,
  0.736816568877369900f, 0.676092703575315920f,
  0.732654271672412820f, 0.680600997795453020f,
  0.728464390448225200f, 0.685083667772700360f,
  0.724247082951467000f, 0.689540544737066830f,
  0.720002507961381650f, 0.693971460889654000f,
  0.715730825283818590f, 0.698376249408972920f,
  0.711432195745216430f, 0.702754744457225300f,
  0.707106781186547570f, 0.707106781186547460f,
  0.702754744457225300f, 0.711432195745216430f,
  0.698376249408972920f, 0.715730825283818590f,
  0.693971460889654000f, 0.720002507961381650f,
  0.689540544737066940f, 0.724247082951466890f,
  0.685083667772700360f, 0.728464390448225200f,
  0.680600997795453130f, 0.732654271672412820f,
  0.676092703575316030f, 0.736816568877369790f,
  0.671558954847018330f, 0.740951125354959110f,
  0.666999922303637470f, 0.745057785441465950f,
  0.662415777590171780f, 0.749136394523459260f,
  0.657806693297078640f, 0.753186799043612410f,
  0.653172842953776760f, 0.757208846506484460f,
  0.648514401022112550f, 0.761202385484261780f,
  0.643831542889791500f, 0.765167265622458960f,
  0.639124444863775730f, 0.769103337645579590f,
  0.634393284163645490f, 0.773010453362736990f,
  0.629638238914927100f, 0.776888465673232440f,
  0.624859488142386450f, 0.780737228572094380f,
  0.620057211763289210f, 0.784556597155575240f,
  0.615231590580626820f, 0.788346427626606230f,
  0.610382806276309480f, 0.792106577300212390f,
  0.605511041404325550f, 0.795836904608883460f,
  0.600616479383868970f, 0.799537269107905010f,
  0.595699304492433470f, 0.803207531480644830f,
  0.590759701858874280f, 0.806847553543799220f,
  0.585797857456438860f, 0.810457198252594770f,
  0.580813958095764530f, 0.814036329705948300f,
  0.575808191417845340f, 0.817584813151583710f,
  0.570780745886967370f, 0.821102514991104650f,
  0.565731810783613230f, 0.824589302785025290f,
  0.560661576197336030f, 0.828045045257755800f,
  0.555570233019602290f, 0.831469612302545240f,
  0.550457972936604810f, 0.834862874986380010f,
  0.545324988422046460f, 0.838224705554837970f,
  0.540171472729892970f, 0.841554977436898330f,
  0.534997619887097260f, 0.844853565249707010f,
  0.529803624686294830f, 0.848120344803297120f,
  0.524589682678468840f, 0.851355193105265200f,
  0.519355990165589530f, 0.854557988365400530f,
  0.514102744193221660f, 0.857728610000272120f,
  0.508830142543106990f, 0.860866938637767310f,
  0.503538383725717580f, 0.863972856121586700f,
  0.498227666972781870f, 0.867046245515692650f,
  0.492898192229784090f, 0.870086991108711350f,
  0.487550160148436050f, 0.873094978418290090f,
  0.482183772079122830f, 0.876070094195406600f,
  0.476799230063322250f, 0.879012226428633410f,
  0.471396736825997810f, 0.881921264348354940f,
  0.465976495767966130f, 0.884797098430937790f,
  0.460538710958240010f, 0.887639620402853930f,
  0.455083587126343840f, 0.890448723244757880f,
  0.449611329654606600f, 0.893224301195515320f,
  0.444122144570429260f, 0.895966249756185110f,
  0.438616238538527710f, 0.898674465693953820f,
  0.433093818853152010f, 0.901348847046022030f,
  0.427555093430282200f, 0.903989293123443340f,
  0.422000270799799790f, 0.906595704514915330f,
  0.416429560097637320f, 0.909167983090522270f,
  0.410843171057903910f, 0.911706032005429880f,
  0.405241314004989860f, 0.914209755703530690f,
  0.399624199845646790f, 0.916679059921042700f,
  0.393992040061048100f, 0.919113851690057770f,
  0.388345046698826300f, 0.921514039342041900f,
  0.382683432365089840f, 0.923879532511286740f,
  0.377007410216418310f, 0.926210242138311270f,
  0.371317193951837600f, 0.928506080473215480f,
  0.365612997804773960f, 0.930766961078983710f,
  0.359895036534988280f, 0.932992798834738850f,
  0.354163525420490510f, 0.935183509938947500f,
  0.348418680249434510f, 0.937339011912574960f,
  0.342660717311994380f, 0.939459223602189920f,
  0.336889853392220050f, 0.941544065183020810f,
  0.331106305759876430f, 0.943593458161960390f,
  0.325310292162262980f, 0.945607325380521280f,
  0.319502030816015750f, 0.947585591017741090f,
  0.313681740398891570f, 0.949528180593036670f,
  0.307849640041534980f, 0.951435020969008340f,
  0.302005949319228200f, 0.953306040354193750f,
  0.296150888243623960f, 0.955141168305770670f,
  0.290284677254462330f, 0.956940335732208940f,
  0.284407537211271820f, 0.958703474895871600f,
  0.278519689385053060f, 0.960430519415565790f,
  0.272621355449948980f, 0.962121404269041580f,
  0.266712757474898420f, 0.963776065795439840f,
  0.260794117915275570f, 0.965394441697689400f,
  0.254865659604514630f, 0.966976471044852070f,
  0.248927605745720260f, 0.968522094274417270f,
  0.242980179903263980f, 0.970031253194543970f,
  0.237023605994367340f, 0.971503890986251780f,
  0.231058108280671280f, 0.972939952205560070f,
  0.225083911359792780f, 0.974339382785575860f,
  0.219101240156869770f, 0.975702130038528570f,
  0.213110319916091360f, 0.977028142657754390f,
  0.207111376192218560f, 0.978317370719627650f,
  0.201104634842091960f, 0.979569765685440520f,
  0.195090322016128330f, 0.980785280403230430f,
  0.189068664149806280f, 0.981963869109555240f,
  0.183039887955141060f, 0.983105487431216290f,
  0.177004220412148860f, 0.984210092386929030f,
  0.170961888760301360f, 0.985277642388941220f,
  0.164913120489970090f, 0.986308097244598670f,
  0.158858143333861390f, 0.987301418157858430f,
  0.152797185258443410f, 0.988257567730749460f,
  0.146730474455361750f, 0.989176509964781010f,
  0.140658239332849240f, 0.990058210262297120f,
  0.134580708507126220f, 0.990902635427780010f,
  0.128498110793793220f, 0.991709753669099530f,
  0.122410675199216280f, 0.992479534598709970f,
  0.116318630911904880f, 0.993211949234794500f,
  0.110222207293883180f, 0.993906970002356060f,
  0.104121633872054730f, 0.994564570734255420f,
  0.098017140329560770f, 0.995184726672196820f,
  0.091908956497132696f, 0.995767414467659820f,
  0.085797312344439880f, 0.996312612182778000f,
  0.079682437971430126f, 0.996820299291165670f,
  0.073564563599667454f, 0.997290456678690210f,
  0.067443919563664106f, 0.997723066644191640f,
  0.061320736302208648f, 0.998118112900149180f,
  0.055195244349690031f, 0.998475580573294770f,
  0.049067674327418126f, 0.998795456205172410f,
  0.042938256934940959f, 0.999077727752645360f,
  0.036807222941358991f, 0.999322384588349540f,
  0.030674803176636581f, 0.999529417501093140f,
  0.024541228522912264f, 0.999698818696204250f,
  0.018406729905804820f, 0.999830581795823400f,
  0.012271538285719944f, 0.999924701839144500f,
  0.006135884649154515f, 0.999981175282601110f,
  0.000000000000000061f, 1.000000000000000000f,
  -0.006135884649154393f, 0.999981175282601110f,
  -0.012271538285719823f, 0.999924701839144500f,
  -0.018406729905804695f, 0.999830581795823400f,
  -0.024541228522912142f, 0.999698818696204250f,
  -0.030674803176636459f, 0.999529417501093140f,
  -0.036807222941358866f, 0.999322384588349540f,
  -0.042938256934940834f, 0.999077727752645360f,
  -0.049067674327418008f, 0.998795456205172410f,
  -0.055195244349689913f, 0.998475580573294770f,
  -0.061320736302208530f, 0.998118112900149180f,
  -0.067443919563663982f, 0.997723066644191640f,
  -0.073564563599667329f, 0.997290456678690210f,
  -0.079682437971430015f, 0.996820299291165780f,
  -0.085797312344439755f, 0.996312612182778000f,
  -0.091908956497132571f, 0.995767414467659820f,
  -0.098017140329560645f, 0.995184726672196930f,
  -0.104121633872054600f, 0.994564570734255420f,
  -0.110222207293883060f, 0.993906970002356060f,
  -0.116318630911904750f, 0.993211949234794500f,
  -0.122410675199216150f, 0.992479534598709970f,
  -0.128498110793793110f, 0.991709753669099530f,
  -0.134580708507126110f, 0.990902635427780010f,
  -0.140658239332849130f, 0.990058210262297120f,
  -0.146730474455361640f, 0.989176509964781010f,
  -0.152797185258443300f, 0.988257567730749460f,
  -0.158858143333861280f, 0.987301418157858430f,
  -0.164913120489969950f, 0.986308097244598670f,
  -0.170961888760301240f, 0.985277642388941220f,
  -0.177004220412148750f, 0.984210092386929030f,
  -0.183039887955140920f, 0.983105487431216290f,
  -0.189068664149806160f, 0.981963869109555240f,
  -0.195090322016128190f, 0.980785280403230430f,
  -0.201104634842091820f, 0.979569765685440520f,
  -0.207111376192218450f, 0.978317370719627650f,
  -0.213110319916091250f, 0.977028142657754390f,
  -0.219101240156869660f, 0.975702130038528570f,
  -0.225083911359792670f, 0.974339382785575860f,
  -0.231058108280671140f, 0.972939952205560180f,
  -0.237023605994367230f, 0.971503890986251780f,
  -0.242980179903263870f, 0.970031253194543970f,
  -0.248927605745720120f, 0.968522094274417380f,
  -0.254865659604514520f, 0.966976471044852070f,
  -0.260794117915275460f, 0.965394441697689400f,
  -0.266712757474898310f, 0.963776065795439840f,
  -0.272621355449948870f, 0.962121404269041580f,
  -0.278519689385052950f, 0.960430519415565900f,
  -0.284407537211271710f, 0.958703474895871600f,
  -0.290284677254462160f, 0.956940335732208940f,
  -0.296150888243623840f, 0.955141168305770670f,
  -0.302005949319228080f, 0.953306040354193860f,
  -0.307849640041534870f, 0.951435020969008340f,
  -0.313681740398891410f, 0.949528180593036670f,
  -0.319502030816015640f, 0.947585591017741200f,
  -0.325310292162262870f, 0.945607325380521390f,
  -0.331106305759876320f, 0.943593458161960390f,
  -0.336889853392219940f, 0.941544065183020810f,
  -0.342660717311994270f, 0.939459223602189920f,
  -0.348418680249434400f, 0.937339011912574960f,
  -0.354163525420490400f, 0.935183509938947610f,
  -0.359895036534988170f, 0.932992798834738850f,
  -0.365612997804773850f, 0.930766961078983710f,
  -0.371317193951837490f, 0.928506080473215590f,
  -0.377007410216418200f, 0.926210242138311380f,
  -0.382683432365089730f, 0.923879532511286740f,
  -0.388345046698826190f, 0.921514039342042010f,
  -0.393992040061047990f, 0.919113851690057770f,
  -0.399624199845646680f, 0.916679059921042700f,
  -0.405241314004989750f, 0.914209755703530690f,
  -0.410843171057903800f, 0.911706032005429880f,
  -0.416429560097636990f, 0.909167983090522490f,
  -0.422000270799799680f, 0.906595704514915330f,
  -0.427555093430281860f, 0.903989293123443450f,
  -0.433093818853151900f, 0.901348847046022030f,
  -0.438616238538527380f, 0.898674465693953930f,
  -0.444122144570429140f, 0.895966249756185220f,
  -0.449611329654606710f, 0.893224301195515210f,
  -0.455083587126343720f, 0.890448723244757990f,
  -0.460538710958240060f, 0.887639620402853930f,
  -0.465976495767966010f, 0.884797098430937900f,
  -0.471396736825997700f, 0.881921264348355050f,
  -0.476799230063321920f, 0.879012226428633530f,
  -0.482183772079122720f, 0.876070094195406600f,
  -0.487550160148435720f, 0.873094978418290200f,
  -0.492898192229783980f, 0.870086991108711460f,
  -0.498227666972781590f, 0.867046245515692760f,
  -0.503538383725717460f, 0.863972856121586810f,
  -0.508830142543107100f, 0.860866938637767200f,
  -0.514102744193221660f, 0.857728610000272120f,
  -0.519355990165589640f, 0.854557988365400530f,
  -0.524589682678468730f, 0.851355193105265200f,
  -0.529803624686294720f, 0.848120344803297230f,
  -0.534997619887097040f, 0.844853565249707230f,
  -0.540171472729892850f, 0.841554977436898440f,
  -0.545324988422046240f, 0.838224705554838190f,
  -0.550457972936604700f, 0.834862874986380120f,
  -0.555570233019601960f, 0.831469612302545460f,
  -0.560661576197335920f, 0.828045045257755800f,
  -0.565731810783613230f, 0.824589302785025180f,
  -0.570780745886967140f, 0.821102514991104760f,
  -0.575808191417845340f, 0.817584813151583710f,
  -0.580813958095764420f, 0.814036329705948520f,
  -0.585797857456438860f, 0.810457198252594770f,
  -0.590759701858874050f, 0.806847553543799450f,
  -0.595699304492433360f, 0.803207531480644940f,
  -0.600616479383868750f, 0.799537269107905240f,
  -0.605511041404325430f, 0.795836904608883570f,
  -0.610382806276309590f, 0.792106577300212280f,
  -0.615231590580626710f, 0.788346427626606340f,
  -0.620057211763289210f, 0.784556597155575130f,
  -0.624859488142386230f, 0.780737228572094600f,
  -0.629638238914927100f, 0.776888465673232440f,
  -0.634393284163645380f, 0.773010453362737100f,
  -0.639124444863775730f, 0.769103337645579590f,
  -0.643831542889791280f, 0.765167265622459070f,
  -0.648514401022112440f, 0.761202385484261890f,
  -0.653172842953776530f, 0.757208846506484680f,
  -0.657806693297078640f, 0.753186799043612520f,
  -0.662415777590171890f, 0.749136394523459260f,
  -0.666999922303637360f, 0.745057785441466060f,
  -0.671558954847018440f, 0.740951125354958990f,
  -0.676092703575315810f, 0.736816568877370020f,
  -0.680600997795453020f, 0.732654271672412820f,
  -0.685083667772700240f, 0.728464390448225310f,
  -0.689540544737066940f, 0.724247082951466890f,
  -0.693971460889653780f, 0.720002507961381770f,
  -0.698376249408972800f, 0.715730825283818710f,
  -0.702754744457225080f, 0.711432195745216660f,
  -0.707106781186547460f, 0.707106781186547570f,
  -0.711432195745216540f, 0.702754744457225190f,
  -0.715730825283818590f, 0.698376249408972920f,
  -0.720002507961381650f, 0.693971460889654000f,
  -0.724247082951466780f, 0.689540544737067050f,
  -0.728464390448225200f, 0.685083667772700360f,
  -0.732654271672412700f, 0.680600997795453240f,
  -0.736816568877369900f, 0.676092703575315920f,
  -0.740951125354958880f, 0.671558954847018550f,
  -0.745057785441465950f, 0.666999922303637580f,
  -0.749136394523459150f, 0.662415777590172010f,
  -0.753186799043612410f, 0.657806693297078750f,
  -0.757208846506484570f, 0.653172842953776640f,
  -0.761202385484261670f, 0.648514401022112550f,
  -0.765167265622458960f, 0.643831542889791390f,
  -0.769103337645579480f, 0.639124444863775840f,
  -0.773010453362736990f, 0.634393284163645490f,
  -0.776888465673232330f, 0.629638238914927210f,
  -0.780737228572094490f, 0.624859488142386340f,
  -0.784556597155575020f, 0.620057211763289430f,
  -0.788346427626606230f, 0.615231590580626930f,
  -0.792106577300212170f, 0.610382806276309700f,
  -0.795836904608883460f, 0.605511041404325660f,
  -0.799537269107905120f, 0.600616479383868860f,
  -0.803207531480644830f, 0.595699304492433470f,
  -0.806847553543799330f, 0.590759701858874160f,
  -0.810457198252594660f, 0.585797857456438980f,
  -0.814036329705948410f, 0.580813958095764530f,
  -0.817584813151583600f, 0.575808191417845450f,
  -0.821102514991104650f, 0.570780745886967260f,
  -0.824589302785025070f, 0.565731810783613450f,
  -0.828045045257755690f, 0.560661576197336140f,
  -0.831469612302545350f, 0.555570233019602180f,
  -0.834862874986380010f, 0.550457972936604920f,
  -0.838224705554838080f, 0.545324988422046350f,
  -0.841554977436898330f, 0.540171472729892970f,
  -0.844853565249707120f, 0.534997619887097150f,
  -0.848120344803297120f, 0.529803624686294830f,
  -0.851355193105265200f, 0.524589682678468950f,
  -0.854557988365400420f, 0.519355990165589750f,
  -0.857728610000272010f, 0.514102744193221770f,
  -0.860866938637767090f, 0.508830142543107320f,
  -0.863972856121586700f, 0.503538383725717690f,
  -0.867046245515692760f, 0.498227666972781760f,
  -0.870086991108711350f, 0.492898192229784150f,
  -0.873094978418290090f, 0.487550160148435880f,
  -0.876070094195406490f, 0.482183772079122890f,
  -0.879012226428633530f, 0.476799230063322090f,
  -0.881921264348354940f, 0.471396736825997860f,
  -0.884797098430937790f, 0.465976495767966180f,
  -0.887639620402853820f, 0.460538710958240230f,
  -0.890448723244757880f, 0.455083587126343890f,
  -0.893224301195515210f, 0.449611329654606870f,
  -0.895966249756185110f, 0.444122144570429310f,
  -0.898674465693953930f, 0.438616238538527550f,
  -0.901348847046021920f, 0.433093818853152070f,
  -0.903989293123443340f, 0.427555093430282030f,
  -0.906595704514915330f, 0.422000270799799850f,
  -0.909167983090522380f, 0.416429560097637150f,
  -0.911706032005429770f, 0.410843171057904130f,
  -0.914209755703530690f, 0.405241314004989920f,
  -0.916679059921042590f, 0.399624199845647070f,
  -0.919113851690057770f, 0.393992040061048150f,
  -0.921514039342041790f, 0.388345046698826580f,
  -0.923879532511286740f, 0.382683432365089890f,
  -0.926210242138311380f, 0.377007410216418150f,
  -0.928506080473215480f, 0.371317193951837710f,
  -0.930766961078983710f, 0.365612997804773800f,
  -0.932992798834738850f, 0.359895036534988330f,
  -0.935183509938947610f, 0.354163525420490400f,
  -0.937339011912574850f, 0.348418680249434790f,
  -0.939459223602189920f, 0.342660717311994430f,
  -0.941544065183020700f, 0.336889853392220330f,
  -0.943593458161960390f, 0.331106305759876480f,
  -0.945607325380521170f, 0.325310292162263260f,
  -0.947585591017741090f, 0.319502030816015800f,
  -0.949528180593036670f, 0.313681740398891410f,
  -0.951435020969008340f, 0.307849640041535030f,
  -0.953306040354193860f, 0.302005949319228030f,
  -0.955141168305770670f, 0.296150888243624010f,
  -0.956940335732208820f, 0.290284677254462390f,
  -0.958703474895871490f, 0.284407537211272100f,
  -0.960430519415565790f, 0.278519689385053170f,
  -0.962121404269041470f, 0.272621355449949250f,
  -0.963776065795439840f, 0.266712757474898480f,
  -0.965394441697689290f, 0.260794117915275850f,
  -0.966976471044852070f, 0.254865659604514680f,
  -0.968522094274417380f, 0.248927605745720090f,
  -0.970031253194543970f, 0.242980179903264070f,
  -0.971503890986251780f, 0.237023605994367170f,
  -0.972939952205560070f, 0.231058108280671330f,
  -0.974339382785575860f, 0.225083911359792830f,
  -0.975702130038528460f, 0.219101240156870050f,
  -0.977028142657754390f, 0.213110319916091420f,
  -0.978317370719627540f, 0.207111376192218840f,
  -0.979569765685440520f, 0.201104634842092010f,
  -0.980785280403230430f, 0.195090322016128610f,
  -0.981963869109555240f, 0.189068664149806360f,
  -0.983105487431216290f, 0.183039887955140900f,
  -0.984210092386929030f, 0.177004220412148940f,
  -0.985277642388941220f, 0.170961888760301220f,
  -0.986308097244598560f, 0.164913120489970140f,
  -0.987301418157858430f, 0.158858143333861470f,
  -0.988257567730749460f, 0.152797185258443690f,
  -0.989176509964781010f, 0.146730474455361800f,
  -0.990058210262297010f, 0.140658239332849540f,
  -0.990902635427780010f, 0.134580708507126280f,
  -0.991709753669099530f, 0.128498110793793090f,
  -0.992479534598709970f, 0.122410675199216350f,
  -0.993211949234794500f, 0.116318630911904710f,
  -0.993906970002356060f, 0.110222207293883240f,
  -0.994564570734255420f, 0.104121633872054570f,
  -0.995184726672196820f, 0.098017140329560826f,
  -0.995767414467659820f, 0.091908956497132752f,
  -0.996312612182778000f, 0.085797312344440158f,
  -0.996820299291165670f, 0.079682437971430195f,
  -0.997290456678690210f, 0.073564563599667732f,
  -0.997723066644191640f, 0.067443919563664176f,
  -0.998118112900149180f, 0.061320736302208488f,
  -0.998475580573294770f, 0.055195244349690094f,
  -0.998795456205172410f, 0.049067674327417966f,
  -0.999077727752645360f, 0.042938256934941021f,
  -0.999322384588349540f, 0.036807222941358832f,
  -0.999529417501093140f, 0.030674803176636865f,
  -0.999698818696204250f, 0.024541228522912326f,
  -0.999830581795823400f, 0.018406729905805101f,
  -0.999924701839144500f, 0.012271538285720007f,
  -0.999981175282601110f, 0.006135884649154799f,
  -1.000000000000000000f, 0.000000000000000122f,
  -0.999981175282601110f, -0.006135884649154554f,
  -0.999924701839144500f, -0.012271538285719762f,
  -0.999830581795823400f, -0.018406729905804858f,
  -0.999698818696204250f, -0.024541228522912080f,
  -0.999529417501093140f, -0.030674803176636619f,
  -0.999322384588349540f, -0.036807222941358582f,
  -0.999077727752645360f, -0.042938256934940779f,
  -0.998795456205172410f, -0.049067674327417724f,
  -0.998475580573294770f, -0.055195244349689851f,
  -0.998118112900149180f, -0.061320736302208245f,
  -0.997723066644191640f, -0.067443919563663926f,
  -0.997290456678690210f, -0.073564563599667496f,
  -0.996820299291165780f, -0.079682437971429945f,
  -0.996312612182778000f, -0.085797312344439922f,
  -0.995767414467659820f, -0.091908956497132516f,
  -0.995184726672196930f, -0.098017140329560590f,
  -0.994564570734255530f, -0.104121633872054320f,
  -0.993906970002356060f, -0.110222207293883000f,
  -0.993211949234794610f, -0.116318630911904470f,
  -0.992479534598709970f, -0.122410675199216100f,
  -0.991709753669099530f, -0.128498110793792840f,
  -0.990902635427780010f, -0.134580708507126060f,
  -0.990058210262297120f, -0.140658239332849290f,
  -0.989176509964781010f, -0.146730474455361580f,
  -0.988257567730749460f, -0.152797185258443440f,
  -0.987301418157858430f, -0.158858143333861220f,
  -0.986308097244598670f, -0.164913120489969890f,
  -0.985277642388941330f, -0.170961888760300970f,
  -0.984210092386929140f, -0.177004220412148690f,
  -0.983105487431216400f, -0.183039887955140650f,
  -0.981963869109555240f, -0.189068664149806110f,
  -0.980785280403230430f, -0.195090322016128360f,
  -0.979569765685440520f, -0.201104634842091760f,
  -0.978317370719627650f, -0.207111376192218590f,
  -0.977028142657754390f, -0.213110319916091200f,
  -0.975702130038528570f, -0.219101240156869800f,
  -0.974339382785575860f, -0.225083911359792610f,
  -0.972939952205560180f, -0.231058108280671080f,
  -0.971503890986251890f, -0.237023605994366950f,
  -0.970031253194543970f, -0.242980179903263820f,
  -0.968522094274417380f, -0.248927605745719870f,
  -0.966976471044852180f, -0.254865659604514460f,
  -0.965394441697689400f, -0.260794117915275630f,
  -0.963776065795439950f, -0.266712757474898250f,
  -0.962121404269041580f, -0.272621355449949030f,
  -0.960430519415565900f, -0.278519689385052890f,
  -0.958703474895871600f, -0.284407537211271820f,
  -0.956940335732208940f, -0.290284677254462110f,
  -0.955141168305770780f, -0.296150888243623790f,
  -0.953306040354193970f, -0.302005949319227810f,
  -0.951435020969008450f, -0.307849640041534810f,
  -0.949528180593036790f, -0.313681740398891180f,
  -0.947585591017741200f, -0.319502030816015580f,
  -0.945607325380521280f, -0.325310292162262980f,
  -0.943593458161960390f, -0.331106305759876260f,
  -0.941544065183020810f, -0.336889853392220110f,
  -0.939459223602190030f, -0.342660717311994210f,
  -0.937339011912574960f, -0.348418680249434560f,
  -0.935183509938947720f, -0.354163525420490120f,
  -0.932992798834738960f, -0.359895036534988110f,
  -0.930766961078983820f, -0.365612997804773580f,
  -0.928506080473215590f, -0.371317193951837430f,
  -0.926210242138311490f, -0.377007410216417930f,
  -0.923879532511286850f, -0.382683432365089670f,
  -0.921514039342041900f, -0.388345046698826360f,
  -0.919113851690057770f, -0.393992040061047930f,
  -0.916679059921042700f, -0.399624199845646840f,
  -0.914209755703530690f, -0.405241314004989690f,
  -0.911706032005429880f, -0.410843171057903910f,
  -0.909167983090522490f, -0.416429560097636930f,
  -0.906595704514915450f, -0.422000270799799630f,
  -0.903989293123443450f, -0.427555093430281810f,
  -0.901348847046022030f, -0.433093818853151850f,
  -0.898674465693954040f, -0.438616238538527330f,
  -0.895966249756185220f, -0.444122144570429090f,
  -0.893224301195515320f, -0.449611329654606650f,
  -0.890448723244757990f, -0.455083587126343670f,
  -0.887639620402853930f, -0.460538710958240060f,
  -0.884797098430937900f, -0.465976495767965960f,
  -0.881921264348355050f, -0.471396736825997640f,
  -0.879012226428633640f, -0.476799230063321870f,
  -0.876070094195406600f, -0.482183772079122660f,
  -0.873094978418290200f, -0.487550160148435660f,
  -0.870086991108711460f, -0.492898192229783930f,
  -0.867046245515692870f, -0.498227666972781540f,
  -0.863972856121586810f, -0.503538383725717460f,
  -0.860866938637767310f, -0.508830142543107100f,
  -0.857728610000272120f, -0.514102744193221550f,
  -0.854557988365400530f, -0.519355990165589640f,
  -0.851355193105265310f, -0.524589682678468730f,
  -0.848120344803297230f, -0.529803624686294610f,
  -0.844853565249707230f, -0.534997619887096930f,
  -0.841554977436898440f, -0.540171472729892850f,
  -0.838224705554838190f, -0.545324988422046130f,
  -0.834862874986380120f, -0.550457972936604700f,
  -0.831469612302545460f, -0.555570233019601960f,
  -0.828045045257755800f, -0.560661576197335920f,
  -0.824589302785025290f, -0.565731810783613230f,
  -0.821102514991104760f, -0.570780745886967140f,
  -0.817584813151583710f, -0.575808191417845340f,
  -0.814036329705948520f, -0.580813958095764300f,
  -0.810457198252594770f, -0.585797857456438860f,
  -0.806847553543799450f, -0.590759701858873940f,
  -0.803207531480644940f, -0.595699304492433250f,
  -0.799537269107905240f, -0.600616479383868640f,
  -0.795836904608883570f, -0.605511041404325430f,
  -0.792106577300212280f, -0.610382806276309480f,
  -0.788346427626606340f, -0.615231590580626710f,
  -0.784556597155575240f, -0.620057211763289210f,
  -0.780737228572094600f, -0.624859488142386230f,
  -0.776888465673232440f, -0.629638238914926980f,
  -0.773010453362737100f, -0.634393284163645270f,
  -0.769103337645579700f, -0.639124444863775730f,
  -0.765167265622459070f, -0.643831542889791280f,
  -0.761202385484261890f, -0.648514401022112330f,
  -0.757208846506484790f, -0.653172842953776530f,
  -0.753186799043612630f, -0.657806693297078530f,
  -0.749136394523459260f, -0.662415777590171780f,
  -0.745057785441466060f, -0.666999922303637360f,
  -0.740951125354959110f, -0.671558954847018440f,
  -0.736816568877370020f, -0.676092703575315810f,
  -0.732654271672412820f, -0.680600997795453020f,
  -0.728464390448225420f, -0.685083667772700130f,
  -0.724247082951467000f, -0.689540544737066830f,
  -0.720002507961381880f, -0.693971460889653780f,
  -0.715730825283818710f, -0.698376249408972800f,
  -0.711432195745216660f, -0.702754744457225080f,
  -0.707106781186547680f, -0.707106781186547460f,
  -0.702754744457225300f, -0.711432195745216430f,
  -0.698376249408973030f, -0.715730825283818480f,
  -0.693971460889654000f, -0.720002507961381650f,
  -0.689540544737067050f, -0.724247082951466780f,
  -0.685083667772700360f, -0.728464390448225200f,
  -0.680600997795453240f, -0.732654271672412590f,
  -0.676092703575316030f, -0.736816568877369790f,
  -0.671558954847018660f, -0.740951125354958880f,
  -0.666999922303637580f, -0.745057785441465840f,
  -0.662415777590172010f, -0.749136394523459040f,
  -0.657806693297078750f, -0.753186799043612410f,
  -0.653172842953777090f, -0.757208846506484230f,
  -0.648514401022112220f, -0.761202385484262000f,
  -0.643831542889791500f, -0.765167265622458960f,
  -0.639124444863775950f, -0.769103337645579480f,
  -0.634393284163645930f, -0.773010453362736660f,
  -0.629638238914926870f, -0.776888465673232550f,
  -0.624859488142386450f, -0.780737228572094380f,
  -0.620057211763289430f, -0.784556597155575020f,
  -0.615231590580627260f, -0.788346427626605890f,
  -0.610382806276309360f, -0.792106577300212390f,
  -0.605511041404325660f, -0.795836904608883460f,
  -0.600616479383869310f, -0.799537269107904790f,
  -0.595699304492433130f, -0.803207531480645050f,
  -0.590759701858874280f, -0.806847553543799220f,
  -0.585797857456439090f, -0.810457198252594660f,
  -0.580813958095764970f, -0.814036329705948080f,
  -0.575808191417845230f, -0.817584813151583820f,
  -0.570780745886967370f, -0.821102514991104650f,
  -0.565731810783613450f, -0.824589302785025070f,
  -0.560661576197336480f, -0.828045045257755460f,
  -0.555570233019602180f, -0.831469612302545240f,
  -0.550457972936604920f, -0.834862874986380010f,
  -0.545324988422046800f, -0.838224705554837860f,
  -0.540171472729892740f, -0.841554977436898550f,
  -0.534997619887097260f, -0.844853565249707010f,
  -0.529803624686294940f, -0.848120344803297120f,
  -0.524589682678469390f, -0.851355193105264860f,
  -0.519355990165589420f, -0.854557988365400640f,
  -0.514102744193221770f, -0.857728610000272010f,
  -0.508830142543107320f, -0.860866938637767090f,
  -0.503538383725718020f, -0.863972856121586470f,
  -0.498227666972781810f, -0.867046245515692650f,
  -0.492898192229784200f, -0.870086991108711350f,
  -0.487550160148436330f, -0.873094978418289870f,
  -0.482183772079122550f, -0.876070094195406710f,
  -0.476799230063322140f, -0.879012226428633410f,
  -0.471396736825997860f, -0.881921264348354940f,
  -0.465976495767966630f, -0.884797098430937570f,
  -0.460538710958239890f, -0.887639620402854050f,
  -0.455083587126343950f, -0.890448723244757880f,
  -0.449611329654606930f, -0.893224301195515210f,
  -0.444122144570429760f, -0.895966249756184880f,
  -0.438616238538527600f, -0.898674465693953820f,
  -0.433093818853152120f, -0.901348847046021920f,
  -0.427555093430282470f, -0.903989293123443120f,
  -0.422000270799799520f, -0.906595704514915450f,
  -0.416429560097637210f, -0.909167983090522380f,
  -0.410843171057904190f, -0.911706032005429770f,
  -0.405241314004990360f, -0.914209755703530470f,
  -0.399624199845646730f, -0.916679059921042700f,
  -0.393992040061048210f, -0.919113851690057660f,
  -0.388345046698826630f, -0.921514039342041790f,
  -0.382683432365090340f, -0.923879532511286520f,
  -0.377007410216418200f, -0.926210242138311380f,
  -0.371317193951837770f, -0.928506080473215480f,
  -0.365612997804774300f, -0.930766961078983600f,
  -0.359895036534987940f, -0.932992798834738960f,
  -0.354163525420490450f, -0.935183509938947610f,
  -0.348418680249434840f, -0.937339011912574850f,
  -0.342660717311994880f, -0.939459223602189700f,
  -0.336889853392219940f, -0.941544065183020810f,
  -0.331106305759876540f, -0.943593458161960270f,
  -0.325310292162263310f, -0.945607325380521170f,
  -0.319502030816015410f, -0.947585591017741200f,
  -0.313681740398891460f, -0.949528180593036670f,
  -0.307849640041535090f, -0.951435020969008340f,
  -0.302005949319228530f, -0.953306040354193750f,
  -0.296150888243623680f, -0.955141168305770780f,
  -0.290284677254462440f, -0.956940335732208820f,
  -0.284407537211272150f, -0.958703474895871490f,
  -0.278519689385053610f, -0.960430519415565680f,
  -0.272621355449948870f, -0.962121404269041580f,
  -0.266712757474898530f, -0.963776065795439840f,
  -0.260794117915275900f, -0.965394441697689290f,
  -0.254865659604514350f, -0.966976471044852180f,
  -0.248927605745720150f, -0.968522094274417270f,
  -0.242980179903264120f, -0.970031253194543970f,
  -0.237023605994367670f, -0.971503890986251670f,
  -0.231058108280670940f, -0.972939952205560180f,
  -0.225083911359792920f, -0.974339382785575860f,
  -0.219101240156870100f, -0.975702130038528460f,
  -0.213110319916091920f, -0.977028142657754280f,
  -0.207111376192218480f, -0.978317370719627650f,
  -0.201104634842092070f, -0.979569765685440520f,
  -0.195090322016128660f, -0.980785280403230320f,
  -0.189068664149805970f, -0.981963869109555350f,
  -0.183039887955140950f, -0.983105487431216290f,
  -0.177004220412149000f, -0.984210092386929030f,
  -0.170961888760301690f, -0.985277642388941110f,
  -0.164913120489969760f, -0.986308097244598670f,
  -0.158858143333861530f, -0.987301418157858320f,
  -0.152797185258443740f, -0.988257567730749460f,
  -0.146730474455362300f, -0.989176509964780900f,
  -0.140658239332849160f, -0.990058210262297120f,
  -0.134580708507126360f, -0.990902635427780010f,
  -0.128498110793793590f, -0.991709753669099530f,
  -0.122410675199215960f, -0.992479534598710080f,
  -0.116318630911904770f, -0.993211949234794500f,
  -0.110222207293883310f, -0.993906970002356060f,
  -0.104121633872055070f, -0.994564570734255420f,
  -0.098017140329560451f, -0.995184726672196930f,
  -0.091908956497132821f, -0.995767414467659820f,
  -0.085797312344440227f, -0.996312612182778000f,
  -0.079682437971430695f, -0.996820299291165670f,
  -0.073564563599667357f, -0.997290456678690210f,
  -0.067443919563664231f, -0.997723066644191640f,
  -0.061320736302208995f, -0.998118112900149180f,
  -0.055195244349689712f, -0.998475580573294770f,
  -0.049067674327418029f, -0.998795456205172410f,
  -0.042938256934941084f, -0.999077727752645360f,
  -0.036807222941359331f, -0.999322384588349430f,
  -0.030674803176636484f, -0.999529417501093140f,
  -0.024541228522912389f, -0.999698818696204250f,
  -0.018406729905805164f, -0.999830581795823400f,
  -0.012271538285720512f, -0.999924701839144500f,
  -0.006135884649154416f, -0.999981175282601110f,
  -0.000000000000000184f, -1.000000000000000000f,
  0.006135884649154049f, -0.999981175282601110f,
  0.012271538285720144f, -0.999924701839144500f,
  0.018406729905804796f, -0.999830581795823400f,
  0.024541228522912021f, -0.999698818696204250f,
  0.030674803176636116f, -0.999529417501093140f,
  0.036807222941358964f, -0.999322384588349540f,
  0.042938256934940716f, -0.999077727752645360f,
  0.049067674327417661f, -0.998795456205172410f,
  0.055195244349689344f, -0.998475580573294770f,
  0.061320736302208627f, -0.998118112900149180f,
  0.067443919563663871f, -0.997723066644191640f,
  0.073564563599666982f, -0.997290456678690210f,
  0.079682437971430334f, -0.996820299291165670f,
  0.085797312344439852f, -0.996312612182778000f,
  0.091908956497132446f, -0.995767414467659820f,
  0.098017140329560090f, -0.995184726672196930f,
  0.104121633872054700f, -0.994564570734255420f,
  0.110222207293882930f, -0.993906970002356060f,
  0.116318630911904410f, -0.993211949234794610f,
  0.122410675199215600f, -0.992479534598710080f,
  0.128498110793793220f, -0.991709753669099530f,
  0.134580708507125970f, -0.990902635427780010f,
  0.140658239332848790f, -0.990058210262297120f,
  0.146730474455361940f, -0.989176509964780900f,
  0.152797185258443380f, -0.988257567730749460f,
  0.158858143333861170f, -0.987301418157858430f,
  0.164913120489969390f, -0.986308097244598780f,
  0.170961888760301330f, -0.985277642388941220f,
  0.177004220412148640f, -0.984210092386929140f,
  0.183039887955140590f, -0.983105487431216400f,
  0.189068664149805610f, -0.981963869109555350f,
  0.195090322016128300f, -0.980785280403230430f,
  0.201104634842091710f, -0.979569765685440630f,
  0.207111376192218120f, -0.978317370719627770f,
  0.213110319916091560f, -0.977028142657754280f,
  0.219101240156869740f, -0.975702130038528570f,
  0.225083911359792550f, -0.974339382785575970f,
  0.231058108280670580f, -0.972939952205560290f,
  0.237023605994367310f, -0.971503890986251780f,
  0.242980179903263760f, -0.970031253194543970f,
  0.248927605745719790f, -0.968522094274417380f,
  0.254865659604513960f, -0.966976471044852290f,
  0.260794117915275510f, -0.965394441697689400f,
  0.266712757474898200f, -0.963776065795439950f,
  0.272621355449948530f, -0.962121404269041690f,
  0.278519689385053280f, -0.960430519415565790f,
  0.284407537211271770f, -0.958703474895871600f,
  0.290284677254462050f, -0.956940335732208940f,
  0.296150888243623290f, -0.955141168305770890f,
  0.302005949319228140f, -0.953306040354193860f,
  0.307849640041534760f, -0.951435020969008450f,
  0.313681740398891130f, -0.949528180593036790f,
  0.319502030816015080f, -0.947585591017741310f,
  0.325310292162262930f, -0.945607325380521280f,
  0.331106305759876210f, -0.943593458161960390f,
  0.336889853392219610f, -0.941544065183020920f,
  0.342660717311994540f, -0.939459223602189810f,
  0.348418680249434510f, -0.937339011912574960f,
  0.354163525420490070f, -0.935183509938947720f,
  0.359895036534987610f, -0.932992798834739070f,
  0.365612997804773960f, -0.930766961078983710f,
  0.371317193951837380f, -0.928506080473215590f,
  0.377007410216417870f, -0.926210242138311490f,
  0.382683432365090000f, -0.923879532511286630f,
  0.388345046698826300f, -0.921514039342041900f,
  0.393992040061047880f, -0.919113851690057880f,
  0.399624199845646400f, -0.916679059921042820f,
  0.405241314004990030f, -0.914209755703530580f,
  0.410843171057903860f, -0.911706032005429880f,
  0.416429560097636870f, -0.909167983090522490f,
  0.422000270799799180f, -0.906595704514915560f,
  0.427555093430282140f, -0.903989293123443340f,
  0.433093818853151790f, -0.901348847046022140f,
  0.438616238538527270f, -0.898674465693954040f,
  0.444122144570429420f, -0.895966249756185000f,
  0.449611329654606600f, -0.893224301195515320f,
  0.455083587126343610f, -0.890448723244757990f,
  0.460538710958239560f, -0.887639620402854160f,
  0.465976495767966290f, -0.884797098430937680f,
  0.471396736825997590f, -0.881921264348355050f,
  0.476799230063321870f, -0.879012226428633640f,
  0.482183772079122220f, -0.876070094195406930f,
  0.487550160148436000f, -0.873094978418290090f,
  0.492898192229783870f, -0.870086991108711460f,
  0.498227666972781480f, -0.867046245515692870f,
  0.503538383725717800f, -0.863972856121586590f,
  0.508830142543106990f, -0.860866938637767310f,
  0.514102744193221550f, -0.857728610000272230f,
  0.519355990165589200f, -0.854557988365400760f,
  0.524589682678469060f, -0.851355193105265080f,
  0.529803624686294610f, -0.848120344803297340f,
  0.534997619887096930f, -0.844853565249707230f,
  0.540171472729892410f, -0.841554977436898780f,
  0.545324988422046460f, -0.838224705554837970f,
  0.550457972936604700f, -0.834862874986380120f,
  0.555570233019601840f, -0.831469612302545460f,
  0.560661576197336250f, -0.828045045257755690f,
  0.565731810783613120f, -0.824589302785025290f,
  0.570780745886967030f, -0.821102514991104870f,
  0.575808191417844890f, -0.817584813151584040f,
  0.580813958095764640f, -0.814036329705948300f,
  0.585797857456438750f, -0.810457198252594880f,
  0.590759701858873940f, -0.806847553543799450f,
  0.595699304492432910f, -0.803207531480645280f,
  0.600616479383868970f, -0.799537269107905010f,
  0.605511041404325320f, -0.795836904608883680f,
  0.610382806276309140f, -0.792106577300212610f,
  0.615231590580627040f, -0.788346427626606120f,
  0.620057211763289100f, -0.784556597155575240f,
  0.624859488142386120f, -0.780737228572094600f,
  0.629638238914926650f, -0.776888465673232780f,
  0.634393284163645600f, -0.773010453362736880f,
  0.639124444863775620f, -0.769103337645579700f,
  0.643831542889791160f, -0.765167265622459180f,
  0.648514401022112000f, -0.761202385484262220f,
  0.653172842953776760f, -0.757208846506484570f,
  0.657806693297078530f, -0.753186799043612630f,
  0.662415777590171450f, -0.749136394523459590f,
  0.666999922303637690f, -0.745057785441465840f,
  0.671558954847018330f, -0.740951125354959110f,
  0.676092703575315700f, -0.736816568877370020f,
  0.680600997795452690f, -0.732654271672413150f,
  0.685083667772700470f, -0.728464390448225090f,
  0.689540544737066830f, -0.724247082951467000f,
  0.693971460889653780f, -0.720002507961381880f,
  0.698376249408972360f, -0.715730825283819040f,
  0.702754744457225300f, -0.711432195745216430f,
  0.707106781186547350f, -0.707106781186547680f,
  0.711432195745216100f, -0.702754744457225630f,
  0.715730825283818820f, -0.698376249408972690f,
  0.720002507961381540f, -0.693971460889654000f,
  0.724247082951466670f, -0.689540544737067160f,
  0.728464390448224860f, -0.685083667772700800f,
  0.732654271672412930f, -0.680600997795453020f,
  0.736816568877369790f, -0.676092703575316030f,
  0.740951125354958880f, -0.671558954847018660f,
  0.745057785441465500f, -0.666999922303638030f,
  0.749136394523459370f, -0.662415777590171780f,
  0.753186799043612300f, -0.657806693297078860f,
  0.757208846506484230f, -0.653172842953777090f,
  0.761202385484261890f, -0.648514401022112330f,
  0.765167265622458850f, -0.643831542889791500f,
  0.769103337645579480f, -0.639124444863775950f,
  0.773010453362736660f, -0.634393284163645930f,
  0.776888465673232550f, -0.629638238914926980f,
  0.780737228572094380f, -0.624859488142386450f,
  0.784556597155575020f, -0.620057211763289540f,
  0.788346427626605890f, -0.615231590580627370f,
  0.792106577300212390f, -0.610382806276309480f,
  0.795836904608883340f, -0.605511041404325660f,
  0.799537269107904790f, -0.600616479383869310f,
  0.803207531480645050f, -0.595699304492433250f,
  0.806847553543799220f, -0.590759701858874280f,
  0.810457198252594660f, -0.585797857456439090f,
  0.814036329705948080f, -0.580813958095764970f,
  0.817584813151583710f, -0.575808191417845230f,
  0.821102514991104540f, -0.570780745886967370f,
  0.824589302785025070f, -0.565731810783613560f,
  0.828045045257755350f, -0.560661576197336590f,
  0.831469612302545240f, -0.555570233019602180f,
  0.834862874986379900f, -0.550457972936605030f,
  0.838224705554837750f, -0.545324988422046800f,
  0.841554977436898440f, -0.540171472729892740f,
  0.844853565249707010f, -0.534997619887097260f,
  0.848120344803297120f, -0.529803624686294940f,
  0.851355193105264860f, -0.524589682678469390f,
  0.854557988365400530f, -0.519355990165589530f,
  0.857728610000272010f, -0.514102744193221880f,
  0.860866938637767090f, -0.508830142543107430f,
  0.863972856121586360f, -0.503538383725718130f,
  0.867046245515692650f, -0.498227666972781870f,
  0.870086991108711350f, -0.492898192229784260f,
  0.873094978418289870f, -0.487550160148436380f,
  0.876070094195406710f, -0.482183772079122610f,
  0.879012226428633410f, -0.476799230063322200f,
  0.881921264348354830f, -0.471396736825997920f,
  0.884797098430937460f, -0.465976495767966680f,
  0.887639620402853930f, -0.460538710958239950f,
  0.890448723244757770f, -0.455083587126344000f,
  0.893224301195515100f, -0.449611329654606980f,
  0.895966249756184880f, -0.444122144570429810f,
  0.898674465693953820f, -0.438616238538527660f,
  0.901348847046021920f, -0.433093818853152180f,
  0.903989293123443120f, -0.427555093430282530f,
  0.906595704514915450f, -0.422000270799799570f,
  0.909167983090522380f, -0.416429560097637260f,
  0.911706032005429660f, -0.410843171057904240f,
  0.914209755703530470f, -0.405241314004990420f,
  0.916679059921042700f, -0.399624199845646790f,
  0.919113851690057660f, -0.393992040061048270f,
  0.921514039342041790f, -0.388345046698826690f,
  0.923879532511286520f, -0.382683432365090390f,
  0.926210242138311380f, -0.377007410216418260f,
  0.928506080473215480f, -0.371317193951837820f,
  0.930766961078983490f, -0.365612997804774350f,
  0.932992798834738960f, -0.359895036534988000f,
  0.935183509938947500f, -0.354163525420490510f,
  0.937339011912574850f, -0.348418680249434900f,
  0.939459223602189700f, -0.342660717311994930f,
  0.941544065183020810f, -0.336889853392220000f,
  0.943593458161960270f, -0.331106305759876600f,
  0.945607325380521170f, -0.325310292162263370f,
  0.947585591017741200f, -0.319502030816015470f,
  0.949528180593036670f, -0.313681740398891520f,
  0.951435020969008340f, -0.307849640041535140f,
  0.953306040354193640f, -0.302005949319228580f,
  0.955141168305770780f, -0.296150888243623730f,
  0.956940335732208820f, -0.290284677254462500f,
  0.958703474895871490f, -0.284407537211272210f,
  0.960430519415565680f, -0.278519689385053670f,
  0.962121404269041580f, -0.272621355449948980f,
  0.963776065795439840f, -0.266712757474898590f,
  0.965394441697689290f, -0.260794117915275960f,
  0.966976471044852180f, -0.254865659604514410f,
  0.968522094274417270f, -0.248927605745720200f,
  0.970031253194543970f, -0.242980179903264180f,
  0.971503890986251670f, -0.237023605994367730f,
  0.972939952205560180f, -0.231058108280671000f,
  0.974339382785575860f, -0.225083911359792970f,
  0.975702130038528460f, -0.219101240156870160f,
  0.977028142657754170f, -0.213110319916091970f,
  0.978317370719627650f, -0.207111376192218530f,
  0.979569765685440520f, -0.201104634842092120f,
  0.980785280403230320f, -0.195090322016128720f,
  0.981963869109555350f, -0.189068664149806030f,
  0.983105487431216290f, -0.183039887955141010f,
  0.984210092386929030f, -0.177004220412149050f,
  0.985277642388941110f, -0.170961888760301770f,
  0.986308097244598670f, -0.164913120489969810f,
  0.987301418157858320f, -0.158858143333861580f,
  0.988257567730749460f, -0.152797185258443800f,
  0.989176509964780900f, -0.146730474455362390f,
  0.990058210262297120f, -0.140658239332849210f,
  0.990902635427780010f, -0.134580708507126420f,
  0.991709753669099410f, -0.128498110793793640f,
  0.992479534598709970f, -0.122410675199216030f,
  0.993211949234794500f, -0.116318630911904840f,
  0.993906970002356060f, -0.110222207293883360f,
  0.994564570734255420f, -0.104121633872055130f,
  0.995184726672196930f, -0.098017140329560506f,
  0.995767414467659820f, -0.091908956497132877f,
  0.996312612182778000f, -0.085797312344440282f,
  0.996820299291165670f, -0.079682437971430750f,
  0.997290456678690210f, -0.073564563599667412f,
  0.997723066644191640f, -0.067443919563664287f,
  0.998118112900149180f, -0.061320736302209057f,
  0.998475580573294770f, -0.055195244349689775f,
  0.998795456205172410f, -0.049067674327418091f,
  0.999077727752645360f, -0.042938256934941139f,
  0.999322384588349430f, -0.036807222941359394f,
  0.999529417501093140f, -0.030674803176636543f,
  0.999698818696204250f, -0.024541228522912448f,
  0.999830581795823400f, -0.018406729905805226f,
  0.999924701839144500f, -0.012271538285720572f,
  0.999981175282601110f, -0.006135884649154477f
};

/**   
* @brief  Initialization function for the floating-point CFFT/CIFFT.  
* @param[in,out] *S             points to an instance of the floating-point CFFT/CIFFT structure.  
* @param[in]     fftLen         length of the FFT.  
* @param[in]     ifftFlag       flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform.  
* @param[in]     bitReverseFlag flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output.  
* @return        The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.  
*   
* \par Description:  
* \par   
* The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed.   
* Set(=1) ifftFlag for calculation of CIFFT otherwise  CFFT is calculated  
* \par   
* The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order.   
* Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order.   
* \par   
* The parameter <code>fftLen</code>	Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024.   
* \par   
* This Function also initializes Twiddle factor table pointer and Bit reversal table pointer.   
*/

arm_status arm_cfft_radix4_init_f32(
  arm_cfft_radix4_instance_f32 * S,
  uint16_t fftLen,
  uint8_t ifftFlag,
  uint8_t bitReverseFlag)
{
  /*  Initialise the default arm status */
  arm_status status = ARM_MATH_SUCCESS;

  /*  Initialise the FFT length */
  S->fftLen = fftLen;

  /*  Initialise the Twiddle coefficient pointer */
  S->pTwiddle = (float32_t *) twiddleCoef;

  /*  Initialise the Flag for selection of CFFT or CIFFT */
  S->ifftFlag = ifftFlag;

  /*  Initialise the Flag for calculation Bit reversal or not */
  S->bitReverseFlag = bitReverseFlag;

  /*  Initializations of structure parameters depending on the FFT length */
  switch (S->fftLen)
  {

  case 1024u:
    /*  Initializations of structure parameters for 1024 point FFT */

    /*  Initialise the twiddle coef modifier value */
    S->twidCoefModifier = 1u;
    /*  Initialise the bit reversal table modifier */
    S->bitRevFactor = 1u;
    /*  Initialise the bit reversal table pointer */
    S->pBitRevTable = armBitRevTable;
    /*  Initialise the 1/fftLen Value */
    S->onebyfftLen = 0.0009765625f;
    break;


  case 256u:
    /*  Initializations of structure parameters for 256 point FFT */
    S->twidCoefModifier = 4u;
    S->bitRevFactor = 4u;
    S->pBitRevTable = &armBitRevTable[3];
    S->onebyfftLen = 0.00390625f;
    break;

  case 64u:
    /*  Initializations of structure parameters for 64 point FFT */
    S->twidCoefModifier = 16u;
    S->bitRevFactor = 16u;
    S->pBitRevTable = &armBitRevTable[15];
    S->onebyfftLen = 0.015625f;
    break;

  case 16u:
    /*  Initializations of structure parameters for 16 point FFT */
    S->twidCoefModifier = 64u;
    S->bitRevFactor = 64u;
    S->pBitRevTable = &armBitRevTable[63];
    S->onebyfftLen = 0.0625f;
    break;


  default:
    /*  Reporting argument error if fftSize is not valid value */
    status = ARM_MATH_ARGUMENT_ERROR;
    break;
  }

  return (status);
}

/**   
 * @} end of CFFT_CIFFT group   
 */
