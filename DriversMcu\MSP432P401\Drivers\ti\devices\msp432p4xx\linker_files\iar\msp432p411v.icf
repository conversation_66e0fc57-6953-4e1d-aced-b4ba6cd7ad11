/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_4.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x00000000;

/*-The following memory regions show the usage of the INFO flash memory-*/
/*-INFO flash memory is intended to be used for the following-*/
/*-device specific purposes (see datasheet for details):-*/
/*-Flash mailbox for device security operations-*/
/*-TLV table for device identification and characterization-*/
/*-BSL area for device bootstrap loader-*/

/*-Memory Regions-*/
define symbol __ICFEDIT_region_IROM1_start__ = 0x00000000;
define symbol __ICFEDIT_region_IROM1_end__   = 0x0007FFFF;
define symbol __ICFEDIT_region_IROM2_start__ = 0x00200000;
define symbol __ICFEDIT_region_IROM2_end__   = 0x00207FFF;
define symbol __ICFEDIT_region_EROM1_start__ = 0x0;
define symbol __ICFEDIT_region_EROM1_end__   = 0x0;
define symbol __ICFEDIT_region_EROM2_start__ = 0x0;
define symbol __ICFEDIT_region_EROM2_end__   = 0x0;
define symbol __ICFEDIT_region_EROM3_start__ = 0x0;
define symbol __ICFEDIT_region_EROM3_end__   = 0x0;
define symbol __ICFEDIT_region_IRAM1_start__ = 0x20000000;
define symbol __ICFEDIT_region_IRAM1_end__   = 0x2001FFFF;
define symbol __ICFEDIT_region_IRAM2_start__ = 0x0;
define symbol __ICFEDIT_region_IRAM2_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM1_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM1_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM2_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM2_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM3_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM3_end__   = 0x0;
/*-Sizes-*/
define symbol __ICFEDIT_size_proc_stack__ = 0x0000;
define symbol __ICFEDIT_size_cstack__     = 0x1000;
define symbol __ICFEDIT_size_heap__       = 0x2000;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region IROM_region   =   mem:[from __ICFEDIT_region_IROM1_start__ to __ICFEDIT_region_IROM1_end__];
define region EROM_region   =   mem:[from __ICFEDIT_region_EROM1_start__ to __ICFEDIT_region_EROM1_end__]
                              | mem:[from __ICFEDIT_region_EROM2_start__ to __ICFEDIT_region_EROM2_end__]
                              | mem:[from __ICFEDIT_region_EROM3_start__ to __ICFEDIT_region_EROM3_end__];
define region IRAM_region   =   mem:[from __ICFEDIT_region_IRAM1_start__ to __ICFEDIT_region_IRAM1_end__]
                              | mem:[from __ICFEDIT_region_IRAM2_start__ to __ICFEDIT_region_IRAM2_end__];
define region ERAM_region   =   mem:[from __ICFEDIT_region_ERAM1_start__ to __ICFEDIT_region_ERAM1_end__]
                              | mem:[from __ICFEDIT_region_ERAM2_start__ to __ICFEDIT_region_ERAM2_end__]
                              | mem:[from __ICFEDIT_region_ERAM3_start__ to __ICFEDIT_region_ERAM3_end__];

define region INFO_region   =   mem:[from __ICFEDIT_region_IROM2_start__ to __ICFEDIT_region_IROM2_end__];

define block PROC_STACK with alignment = 8, size = __ICFEDIT_size_proc_stack__  { };
define block CSTACK     with alignment = 8, size = __ICFEDIT_size_cstack__      { };
define block HEAP       with alignment = 8, size = __ICFEDIT_size_heap__        { };

initialize by copy { readwrite };
do not initialize  { section .noinit };
if (isdefinedsymbol(__USE_DLIB_PERTHREAD))
{
  // Required in a multi-threaded application
  initialize by copy with packing = none { section __DLIB_PERTHREAD };
}

place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };

place in IROM_region  { readonly };
place in EROM_region  { readonly section application_specific_ro };
place in IRAM_region  { readwrite, block CSTACK, block PROC_STACK, block HEAP };
place in ERAM_region  { readwrite section application_specific_rw };
place in INFO_region  { section .info };
